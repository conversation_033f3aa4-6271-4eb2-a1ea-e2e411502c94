<template>
  <div class="no-attack-box" :style="style">
    <img src="@/assets/security/noData.png" alt />
    <p>暂无数据</p>
  </div>
</template>

<script>
export default {
  name: 'NullData',
  props: {
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.no-attack-box {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #52c4ff;
  flex-direction: column;
  p {
    margin-top: 10px;
  }
}
</style>
