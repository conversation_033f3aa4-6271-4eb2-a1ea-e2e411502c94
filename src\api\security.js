import request from '@/utils/request'
//安全态势
//外部威胁-攻击源IP
export function wafAttackip(data) {
  // debugger
  return request({
    url: '/api/safe/wafAttackSourceClientIp',
    method: 'post',
    params:data 
  })
}
//外部威胁-威胁事件类型
export function wafProtect(data) {
  // debugger
  return request({
    url: '/api/safe/wafProtectionType',
    method: 'post',
    params:data 
  })
}
//内部威胁-威胁top10
export function threatRanking(data) {
  // debugger
  return request({
    url: '/api/safe/threatRankingTopTen',
    method: 'post',
    params:data 
  })
}
//安全态势-雷达图
export function radar(data) {
  // debugger
  return request({
    url: '/api/log/event',
    method: 'post',
    params:data 
  })
}
//安全态势-网络流量排行榜
export function bandWidth(data) {
  // debugger
  return request({
    url: '/api/log/bandwidth',
    method: 'post',
    params:data 
  })
}
// 攻击日志
export function attackLog(data) {
  // debugger
  return request({
    url: '/api/log/ipsRaw',
    method: 'post',
    params:data 
  })
}
//云主机cpu、内存负载
export function loadCpu(data) {
  // debugger
  return request({
    url: '/api/safe/getCpuTop5',
    method: 'post',
    params:data 
  })
}
export function loadRam(data) {
  // debugger
  return request({
    url: '/api/safe/getRamTop5',
    method: 'post',
    params:data 
  })
}
//云主机磁盘读写速度
export function getDisk(metricName ) {
  // debugger
  return request({
    url: '/api/safe/getDiskReadAndWriteSpeed',
    method: 'post',
    params:{metricName:metricName }
  })
}
export function getDiskReadAndOut(uuid) {
  // debugger
  return request({
    url: '/api/safe/getDiskReadAndOutSpeed',
    method: 'post',
    params:uuid
  })
}
//云主机网卡出入速度
export function getNetworkCard(metricName ) {
  // debugger
  return request({
    url: '/api/safe/getNetworkCardAccessSpeed',
    method: 'post',
    params:{metricName:metricName }
  })
}
export function getNetWorkOut(uuid ) {
  // debugger
  return request({
    url: '/api/safe/getNetWorkOutSpeed',
    method: 'post',
    params:uuid
  })
}

//安全态势-密码安全态势-设备服务态势
export function sbfw( ) {
  // debugger
  return request({
    url: '/api/hx/sbfw',
    method: 'get',
    
  })
}

//安全态势-密码安全态势-密码业务态势
export function mmyw( ) {
  // debugger
  return request({
    url: '/api/hx/mmyw',
    method: 'get',
    
  })
}

//轮播图时间设置
export function queryTime() {
  // debugger
  return request({
    url: '/api/queryTime',
    method: 'get',
    
  })
}