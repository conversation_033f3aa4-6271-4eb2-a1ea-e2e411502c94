import request from '@/utils/request'
//安全态势
//攻击源TOP5
export function threatRanking() {
  return request({
    url: '/api/firewall/threatRankingTopTen',
    method: 'post'
   
  })
}
//攻击类型TOP5 
export function threatType() {
  return request({
    url: '/api/firewall/getTFirewallNameList',
    method: 'post'
    
  })
}


//安全态势---流量统计
export function getDeviceList(dateType) {
  // debugger
  return request({
    url: `/api/firewall/getDeviceList?dateType=${dateType}`,
    method: 'post',
    
  })
}
//安全态势---流量统计
export function getFlowList(dateType) {
  // debugger
  return request({
    url: `/api/firewall/getFlowList?dateType=${dateType}`,
    method: 'post',
    
  })
}

//安全态势---云主机流量top5
export function getUserTotalStream(dateType) {
  // debugger
  return request({
    url: `/api/firewall/getUserTotalStream?dateType=${dateType}`,
    method: 'post',
   
  })
}
//安全态势---应用流量top5
export function getAppTotalStream(dateType) {
  // debugger
  return request({
    url: `/api/firewall/getAppTotalStream?dateType=${dateType}`,
    method: 'post'
   
  })
}
//安全态势---云主机并发连接top5
export function getUserTotalSession() {
  // debugger
  return request({
    url: '/api/firewall/getUserTotalSession',
    method: 'post'
   
  })
}
//安全态势---接口信息统计
export function getInterfaceList() {
  // debugger
  return request({
    url: '/api/firewall/getInterfaceList',
    method: 'post'
   
  })
}
//安全态势---设备健康统计
export function getEquipmentList() {
  // debugger
  return request({
    url: '/api/firewall/getEquipmentList',
    method: 'post'
   
  })
}
//安全态势---应用并发连接top5
export function getAppTotalSession() {
  // debugger
  return request({
    url: '/api/firewall/getAppTotalSession',
    method: 'post'
  
  })
}








