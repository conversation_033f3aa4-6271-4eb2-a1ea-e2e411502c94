﻿@charset "utf-8";

html {
    font-family: 宋体;
}

.sy-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    background: rgba(0,0,0,0.8);
    left: 0;
    top: 0;
    z-index: 1000
}

.sy-alert {
    position: fixed;
    display: none;
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
    width: 300px;
    max-width: 90%;
    max-height: 80%;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 9999
}

    .sy-alert.animated {
        -webkit-animation-duration: .3s;
        animation-duration: .3s
    }

    .sy-alert .sy-title {
        height: 45px;
        color: #333;
        line-height: 45px;
        font-size: 20px;
        border-bottom: 1px solid #eee;
        padding: 0 12px;
        font-weight: 900;
        background-color: lavender;
        font-family: 宋体;
    }

    .sy-alert .sy-content {
        padding: 20px;
        text-align: center;
        font-size: 20px;
        line-height: 24px;
        color: #666;
        overflow-y: auto
    }

    .sy-alert .sy-btn {
        border-top: 1px solid #eee;
        overflow: hidden;
        
    }

        .sy-alert .sy-btn button {
            float: left;
            border: 0;
            color: #333;
            cursor: pointer;
            background: #fff;
            width: 50%;
            line-height: 45px;
            font-size: 15px;
            text-align: center
        }

            .sy-alert .sy-btn button:nth-child(1) {
                color: #888;
                border-right: 1px solid #eee
            }

    .sy-alert.sy-alert-alert .sy-btn button {
        float: none;
        font-size: 20px;
        width: 100%;
        font-weight: 900;
        font-family: 宋体;
    }

    .sy-alert.sy-alert-tips {
        text-align: center;
        width: 150px;
        background: rgba(0,0,0,0.7)
    }

        .sy-alert.sy-alert-tips .sy-content {
            padding: 8px;
            color: #fff;
            font-size: 14px
        }

    .sy-alert.sy-alert-model .sy-content {
        text-align: left
    }

        .sy-alert.sy-alert-model .sy-content .form .input-item {
            margin-bottom: 12px;
            position: relative
        }

            .sy-alert.sy-alert-model .sy-content .form .input-item input {
                display: block;
                position: relative;
                width: 100%;
                border: 1px solid #eee;
                padding: 10px
            }

            .sy-alert.sy-alert-model .sy-content .form .input-item .getcode {
                border: 0;
                top: 0;
                right: 0;
                position: absolute;
                background: 0;
                line-height: 37px;
                color: #f60;
                width: 100px;
                text-align: center
            }
<!--
body {
    text-align: center
}

-->
.sty1 {
    width: 820px;
}

.sty2 {
    width: 200px;
}

.textClass {
    text-align: center;
}

    .textClass input {
        text-align: center;
    }

.labelClass {
    text-align: left;
}

.titleClass {
    background-color: #B0C4DE;
    font-size: 30px;
    font-weight: 900;
    border-radius: 50%;
    width: 800px;
    height: 60px;
    margin: auto;
    margin-bottom: -31px;
}

.titleClass_Div {
    padding-top: 10px;
}

.blockClass {
    border: 2px solid #B0C4DE;
    width: 1260px;
    margin: auto;
    border-radius: 10px;
    margin-bottom: 20px;
}

.tableClass {
    margin-top: 55px;
}

.titleLeftInfo {
    width: 200px;
    text-align: right;
}

.seleClass {
    width: 450px;
}

.seleClassType {
    width: 120px;
    margin-left: 20px;
}

.btnClass {
    width: 120px;
    margin-left: 20px;
}

.textClassStyle {
    width: 450px;
}

.textareaClass {
    height: 80px;
    width: 450px;
}

.labelPwdClass {
    text-align: left;
}

.textareaClassPro {
    height: 300px;
    width: 450px;
}
.AppDivClass{
    border: 1px solid #808080; 
}
.AppDivTableClass{
    font-size:15px;
    margin:auto;
}


/*悬浮链接*/
.suspension{position:fixed;z-index:55;left:0;top:120px;width:70px;height:240px;}
.suspension-box{position:relative;float: left;}
.suspension .a{display:block;width:44px;height:44px;background-color:#353535;margin-bottom:4px;cursor:pointer;outline:none;}
.suspension .a.active,
.suspension .a:hover{background:#F05352;}
.suspension .a .i{float:left;width:44px;height:44px;background-image:url(../images/side_icon.png);background-repeat:no-repeat;}
/* .suspension .a-service .i{background-position:0 0;} */
.suspension .a-service .i{width:20px;height:20px;margin-top:12px;margin-left:12px;background-image:url(../images/suspension-bg.png);background-repeat:no-repeat;background-position:0 0;}
.suspension .a-service-phone .i{width:20px;height:20px;margin-top:12px;margin-left:12px;background-image:url(../images/suspension-bg.png);background-repeat:no-repeat;background-position:-27px 0;}
.suspension .a-qrcode .i{background-position:-44px 0;}
.suspension .a-cart .i{background-position:-88px 0;}
.suspension .a-top .i{background-position:-132px 0;}
.suspension .a-top{background:#D2D3D6;display:none;}
.suspension .a-top:hover{background:#c0c1c3;}
.suspension .d{display:none;width:223px;background:#fff;position:absolute;left:67px;min-height:90px;border:1px solid #E0E1E5;border-radius:3px;box-shadow:0px 2px 5px 0px rgba(161, 163, 175, 0.11);}
.suspension .d-service{top:0;}
.suspension .d-service-phone{top:34px;}
.suspension .d-qrcode{top:78px;}
.suspension .d .inner-box{padding:8px 22px 12px;}
.suspension .d-service-item{border-bottom:1px solid #eee;padding:14px 0;}
.suspension .d-service-item .circle{width:44px;height:44px;border-radius:50%;overflow:hidden;background:#F1F1F3;display:block;float:left;}
.suspension .d-service-item .i-qq{width:44px;height:44px;background:url(../images/side_con_icon03.png) no-repeat center 15px;display:block;transition:all .2s;border-radius:50%;overflow:hidden;}
.suspension .d-service-item:hover .i-qq{background-position:center 3px;}
.suspension .d-service-item .i-tel{width:44px;height:44px;background:url(../images/side_con_icon02.png) no-repeat center center;display:block;}
.suspension .d-service-item .i-tel2{width:44px;height:44px;background:url(../images/side_con_icon03.png) no-repeat center center;display:block;}
.suspension .d-service-item h3{float:left;width:112px;line-height:28px;font-size:15px;margin-left:12px;}
.suspension .d-service-item .text{float:left;width:112px;line-height:22px;font-size:15px;margin-left:12px;}
.suspension .d-service-item .text .number{font-family:Arial,"Microsoft Yahei","HanHei SC",PingHei,"PingFang SC","Helvetica Neue",Helvetica,Arial,"Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;}
.suspension .d-service-intro{padding-top:10px;}
.suspension .d-service-intro p{float:left;line-height:27px;font-size:12px;width:50%;white-space:nowrap;color:#888;}
.suspension .d-service-intro i{background:url(../images/side_con_icon01.png) no-repeat center center;height:27px;width:14px;margin-right:5px;vertical-align:top;display:inline-block;}
.suspension .d-qrcode{text-align:center;}
.suspension .d-qrcode .inner-box{padding:20px 0;}
.suspension .d-qrcode p{font-size:16px;color:#93959c;}
.longmaiImgClass{width: 200px;}
