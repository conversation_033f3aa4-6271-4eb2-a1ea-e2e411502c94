<template>
  <div>

      <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">
              <span class="el-icon-arrow-left back-icon" @click="$router.back(-1);"></span>添加租户
            </h3>
          </div>
          <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <el-form :model="accountForm" ref="accountForm" :rules="accountRules">
                  <el-form-item label="账号" :label-width="formLabelWidth" prop="loginName">
                    <el-input
                      v-model="accountForm.loginName"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="租户" :label-width="formLabelWidth" prop="tenant">
                    <el-input
                      v-model="accountForm.tenant"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="密码" :label-width="formLabelWidth" prop="loginPwd">
                    <el-input
                      type="password"
                      v-model="accountForm.loginPwd"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="联系人" :label-width="formLabelWidth" prop="userName">
                    <el-input
                      v-model="accountForm.userName"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="联系电话" :label-width="formLabelWidth" prop="mobile">
                    <el-input
                      v-model="accountForm.mobile"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
           </el-scrollbar>
        </div>
      </div>


     <div class="form-foot">
        <el-button type="primary" @click="handleAddClick('accountForm')">确认添加</el-button>
      </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { tenant, addAccount, editTenant, delTenant } from "@/api/account.js";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      listLoading: false,
      downloadLoading: false,
      userId: "",
      tenantId: "", //账号id
      formLabelWidth: "120px",
      forminputWidth: "forminputWidth",
      accountForm: {
        loginName: "",
        tenant: "",
        loginPwd: "",
        userName: "",
        accountType: 1,
        mobile: "",
      }, //添加账号表单
      emptyForm: {
        loginName: "",
        tenant: "",
        loginPwd: "",
        userName: "",
        accountType: 1,
        mobile: "",
      }, //账号表单
      roleList: [], //租户角色
      accountRules: {
        loginName: [
          { required: true, message: "账号不能为空", trigger: "blur" },
        ],
        tenant: [
          { required: true, message: "租户名称不能为空", trigger: "blur" },
        ],
        loginPwd: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        accountType: [
          { required: true, message: "账号类型不能为空", trigger: "blur" },
        ],
        mobile: [{ required: true, message: "请输入手机号", trigger: "blur" }],
      },
    };
  },
  created() {},
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
    //租户添加
    handleAddClick(accountForm) {
      this.$refs[accountForm].validate((valid) => {
        if (valid) {
          let addAccountArry = {
            loginName: this.accountForm.loginName,
            tenant: this.accountForm.tenant,
            loginPwd: this.accountForm.loginPwd,
            userName: this.accountForm.userName,
            accountType: this.accountForm.accountType,
            mobile: this.accountForm.mobile,
            userId: this.userid,
          };
          // console.log(addAccountArry);
          addAccount(addAccountArry)
            .then((res) => {
              // console.log(res);
               if (res.ok == true) {
                 this.$router.push({ path: `/nested/tenant` });
              } else {
                this.$message({
                  showClose: true,
                  message: res.errorMsg,
                  type: "warning",
                });
              }

            })
            .catch((error) => {
              console.log(error);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
    background: #F1F6FA;
  }
}
</style>

