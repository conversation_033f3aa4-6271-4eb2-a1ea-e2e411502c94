<template>
  <div>

      <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">
              <span class="el-icon-arrow-left back-icon" @click="$router.back(-1);"></span>添加角色
            </h3>
          </div>
            <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <div v-if="dialogFormVisible">
                  <el-form :model="accountForm" ref="accountForm" :rules="accountRules">
                    <el-form-item label="角色名称" :label-width="formLabelWidth" prop="roleName">
                      <el-input v-model="accountForm.roleName" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="别名" :label-width="formLabelWidth" prop="remark">
                      <el-input v-model="accountForm.remark" autocomplete="off"></el-input>
                    </el-form-item>
                  </el-form>

                </div>
                <div v-if="ptFormVisible">
                  <el-form :model="accountForm" ref="accountForm" :rules="accountRules">
                    <el-form-item label="租户" :label-width="formLabelWidth" prop="roleName">
                      <el-input v-model="accountForm.roleName" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="角色名称" :label-width="formLabelWidth" prop="roleName">
                      <el-input v-model="accountForm.roleName" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="别名" :label-width="formLabelWidth" prop="remark">
                      <el-input v-model="accountForm.remark" autocomplete="off"></el-input>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
           </el-scrollbar>
        </div>
      </div>

     <div class="form-foot">
     <el-button type="primary" @click="handleAddClick('accountForm')">确 定</el-button>
    </div>

  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import { roleAdd } from "@/api/system.js";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      listLoading: false,
      downloadLoading: false,
      dialogFormVisible: false,
      ptFormVisible: false,
      accountForm: {
        roleName: "",
        remark: "",
      },
      formLabelWidth: "120px",
      accountRules: {
        roleName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        remark: [{ required: true, message: "别名不能为空", trigger: "blur" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  created() {
      //  if (this.usertype === 2) {
      //   this.dialogFormVisible = true;
      // } else if (this.usertype === 1) {
      //   this.ptFormVisible = true;
      // }
      this.dialogFormVisible = true;
  },
  methods: {
    //角色添加
    handleAddClick(accountForm) {
      var uId = "";
      if (this.usertype === 2) {
        uId = this.tenantid;
      } else if (this.usertype === 1) {
        uId = this.userid;
      }
      this.$refs[accountForm].validate((valid) => {
        if (valid) {
          let addAccountArry = {
            roleName: this.accountForm.roleName, //角色key
            remark: this.accountForm.remark, //角色名称
            userId: uId, //用户id
            userType: this.usertype, //用户类型
            tenantIds: "",
          };
          // console.log(addAccountArry);
          roleAdd(addAccountArry)
            .then((res) => {
              // console.log(res);
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.$router.push({ path: `/system/role` });

            })
            .catch((error) => {
              this.$message.error({
                message: "添加失败",
              });
            });
          this.dialogFormVisible = false;
          this.ptFormVisible = false;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
     background: #F1F6FA;

  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>
