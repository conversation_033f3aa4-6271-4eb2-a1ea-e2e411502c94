import request from '@/utils/request'
//网元登录列表接口
export function networkList(userId) {
  return request({
    url: '/api/product/networkList',
    method: 'post',
    params:{userId:userId}
    
  })
}

//云主机安全登录接口
// export function hostLogin(data) {
//     return request({
//       url: '/host/login/login',
//       method: 'post',
//       params:data
      
//     })
// }
export function hostLogin(data) {
    return request({
      url: '/api/qiAnxinLogin',
      method: 'post',
      params:data
      
    })
}
export function host(url) {
  return request({
    url: `/host${url}`,
    method: 'get' 
  })
}
//数据库审计登录接口
export function databaseLogin(data) {
  return request({
    url: `/api/user/getToken`,
    method: 'post',
    params:data
  })
}
//zstack登录接口
export function zsTackLogin() {
  return request({
    url: `/api/safe/zsTackLogin`,
    method: 'post'
  })
}

