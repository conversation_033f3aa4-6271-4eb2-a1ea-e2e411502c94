<template>
  <el-scrollbar wrap-class="scrollbar-wrapper"  >
    <!-- 租户展示页 start-->
    <div class="dashboard-container" v-if="showTenant">
      <div class="dashboard-title">您好，尊敬的{{tenant}}</div>
      <div class="tenant-list clearfix">
        <div class="list-left">
          <ul>
            <li>
              <p class="number">6</p>
              <a href="#" class="text">我的产品</a>
            </li>
          </ul>
        </div>
        <div class="list-right">
          <ul>
            <li>
              <p class="number">0</p>
              <a href="#" class="text">待续费产品</a>
            </li>
            <li>
              <p class="number" v-show="messageNumber<99">{{messageNumber}}</p>
              <p class="number" v-show="messageNumber>99">99+</p>
              <a href="#" class="text">我的消息</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="products-box">
        <div class="products-title">
          <p class="text">丰富的云安全产品选择</p>
          <p class="sub-text">海量的云安全功能。给您更多的优质选择</p>
        </div>
        <div class="products-banner-box">
          <div class="products-banner">
            <el-carousel :interval="5000" arrow="never">
              <el-carousel-item v-for="(item,index) in productList" :key="index">
                <div class="products-item">
                  <div class="products-img">
                    <img src="item.image" alt />
                  </div>
                  <div class="products-intro">
                    <div class="products-intro-hd">
                      <p class="name">{{item.name}}</p>
                      <p class="des">{{item.des}}</p>
                    </div>
                    <div class="features-box">
                      <h3>产品特点</h3>
                      <div
                        class="features-item"
                        v-for="(features,index) in item.productFeatures"
                        :key="index"
                      >
                        <p class="title">{{features.title}}</p>
                        <p class="des">{{features.titieDes}}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </div>
    <!-- 租户展示页 end -->
    <!-- 管理员展示页 start -->
    <div class="admin-container" v-if="!showTenant">
      <div class="admin-box">
        <el-row :gutter="15">
          <el-col :xs="12" :sm="12" :md="6" :lg="6">
            <router-link to>
              <div class="grid-content">
                <div class="grid-content-left">
                  <p class="number">{{ numberList.productNum}}</p>
                  <p class="text">产品数量</p>
                </div>
                <div class="grid-content-right">
                  <img src="@/assets/main/product.png" alt title="产品数量" />
                </div>
              </div>
            </router-link>
          </el-col>
          <el-col :xs="12" :sm="12" :md="6" :lg="6">
            <router-link to>
              <div class="grid-content">
                <div class="grid-content-left">
                  <p class="number">{{numberList.tenantNum}}</p>
                  <p class="text">我的租户</p>
                </div>
                <div class="grid-content-right">
                  <img src="@/assets/main/tenant.png" alt title="我的租户" />
                </div>
              </div>
            </router-link>
          </el-col>
          <el-col :xs="12" :sm="12" :md="6" :lg="6">
            <router-link to>
              <div class="grid-content">
                <div class="grid-content-left">
                  <p class="number">{{numberList.orderNum}}</p>
                  <p class="text">告警</p>
                </div>
                <div class="grid-content-right">
                  <img src="@/assets/alarm.png" alt title="告警" />
                </div>
              </div>
            </router-link>
          </el-col>
          <el-col :xs="12" :sm="12" :md="6" :lg="6">
            <!-- <div class="grid-content grid-content-column">
              <router-link to>
                <div class="grid-content-item">
                  <span class="title">待审核订单</span>
                  <span class="num">[{{ numberList.auditOrderNum}}]</span>
                </div>
              </router-link>
              <router-link to>
                <div class="grid-content-item">
                  <span class="title">待续费产品</span>
                  <span class="num">[{{ numberList.feeNum}}]</span>
                </div>
              </router-link>
              <router-link to>
                <div class="grid-content-item">
                  <span class="title">我的消息</span>
                  <span class="num">[{{ numberList.messageNum}}]</span>
                </div>
              </router-link>
            </div> -->
             <router-link to>
              <div class="grid-content">
                <div class="grid-content-left">
                  <p class="number">{{numberList.orderNum}}</p>
                  <p class="text">租户日志</p>
                </div>
                <div class="grid-content-right">
                  <img src="@/assets/main/order.png" alt title="告警" />
                </div>
              </div>
            </router-link>
          </el-col>
        </el-row>
      </div>
      <div class="admin-box2">
        <div class="box2-container">
          <el-row :gutter="15">
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd">
                    <img src="@/assets/main/net.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">网络安全</div>
                <div v-for="(network,index) in networkList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{network.title}}</div>
                    <div class="supplier">({{network.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_orange">
                    <img src="@/assets/main/host.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">主机安全</div>
                <div v-for="(host,index) in hostList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{host.title}}</div>
                    <div class="supplier">({{host.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_red">
                    <img src="@/assets/main/application.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">应用安全</div>
                <div
                  v-for="(application,index) in applicationList"
                  :key="index"
                  class="grid2-content-wrap"
                >
                  <div class="item-wrap">
                    <div class="item-title">{{application.title}}</div>
                    <div class="supplier">({{application.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_green">
                    <img src="@/assets/main/data.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">主机安全</div>
                <div v-for="(data,index) in dataList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{data.title}}</div>
                    <div class="supplier">({{data.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="admin-box2 admin-box3">
        <div class="box2-container">
          <el-row :gutter="15">
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd">
                    <img src="@/assets/main/net.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">网络安全</div>
                <div v-for="(network,index) in networkList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{network.title}}</div>
                    <div class="supplier">({{network.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_orange">
                    <img src="@/assets/main/host.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">主机安全</div>
                <div v-for="(host,index) in hostList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{host.title}}</div>
                    <div class="supplier">({{host.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_red">
                    <img src="@/assets/main/application.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">应用安全</div>
                <div
                  v-for="(application,index) in applicationList"
                  :key="index"
                  class="grid2-content-wrap"
                >
                  <div class="item-wrap">
                    <div class="item-title">{{application.title}}</div>
                    <div class="supplier">({{application.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12" :md="6" :lg="6">
              <div class="grid2-content">
                <div class="icon-box">
                  <span class="icon-box-bd bg_green">
                    <img src="@/assets/main/data.png" alt class="icon-box-img" />
                  </span>
                </div>
                <div class="grid2-content-title">主机安全</div>
                <div v-for="(data,index) in dataList" :key="index" class="grid2-content-wrap">
                  <div class="item-wrap">
                    <div class="item-title">{{data.title}}</div>
                    <div class="supplier">({{data.supplier}})</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!-- 管理员展示页 end -->
  </el-scrollbar>

</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "Dashboard",
  data() {
    return {
      showTenant: false,
      tenant: "test001", //租户名称
      messageNumber: 90,
      productList: [
        {
          image: "",
          name: "下一代防火墙",
          des:
            "下一代云防火墙是基于NFV架构的虚拟综合业务网关，具备丰富的网关业务能力，如vFW,vIPSec,vLB,vIPS,vAV,vURL过滤等",
          productFeatures: [
            {
              title: "网络隔离",
              titieDes: "实现资产的网络隔离和网络访问控制"
            },
            {
              title: "入侵防护",
              titieDes: "攻击检测和防御，轻松识别攻击并防护"
            },
            {
              title: "病毒过滤(AV)",
              titieDes: "高性能病毒引擎，可防护500万种以上的病毒和木马"
            }
          ]
        },
        {
          image: "",
          name: "下一代云主机",
          des:
            "下一代云主机是基于NFV架构的虚拟综合业务网关，具备丰富的网关业务能力，如vFW,vIPSec,vLB,vIPS,vAV,vURL过滤等",
          productFeatures: [
            {
              title: "网络隔离",
              titieDes: "实现资产的网络隔离和网络访问控制"
            },
            {
              title: "入侵防护",
              titieDes: "攻击检测和防御，轻松识别攻击并防护"
            },
            {
              title: "病毒过滤(AV)",
              titieDes: "高性能病毒引擎，可防护500万种以上的病毒和木马"
            }
          ]
        }
      ],
      numberList: {
        productNum: 10,
        tenantNum: 9,
        orderNum: 85,
        auditOrderNum: 22,
        feeNum: 5,
        messageNum: 10
      },
      networkList: [
        {
          title: "下一代防火墙",
          supplier: "奇安信"
        }
      ], //网络安全列表
      hostList: [
        {
          title: "主机安全及系统管理",
          supplier: "奇安信"
        }
      ], //主机安全
      applicationList: [
        {
          title: "综合漏洞扫描",
          supplier: "奇安信"
        },
        {
          title: "云WEB应用防火墙",
          supplier: "奇安信"
        },
        {
          title: "网页防篡改",
          supplier: "奇安信"
        },
        {
          title: "玄武盾",
          supplier: "奇安信"
        }
      ], //应用安全
      dataList: [
        {
          title: "综合漏洞扫描",
          supplier: "奇安信"
        }
      ] //数据安全
    };
  },
  computed: {
    ...mapGetters(["name"])
  }
};
</script>

<style lang="scss" scoped>
.el-scrollbar{
   height: calc(100vh - 60px);

}
.scrollbar-wrapper{
  
.dashboard {
  &-container {
    height: calc(100vh - 60px);
    background: url("~@/assets/banner.jpg") no-repeat left top;
    background-size: contain;
    position: relative;
    .tenant-list {
      padding: 30px;
      .list-left {
        float: left;
      }
      .list-right {
        float: right;
      }
      li {
        width: 100px;
        text-align: center;
        float: left;
        margin-right: 20px;
        .number {
          font-size: 46px;
          font-weight: normal;
          color: #00ffff;
          margin-bottom: 8px;
        }
        .text {
          color: rgb(190, 191, 193);
          display: inline-block;
          padding: 3px 8px;
          border: 1px solid rgb(190, 191, 193);
          border-radius: 10px;
        }
      }
    }
    .products-box {
      width: 100%;
      background: #ffffff;
      border-radius: 2px 2px 0 0;
      position: absolute;
      top: 200px;
      left: 0;
      right: 0;
      bottom: 0;
      .products-title {
        padding: 20px 0 30px 0;
        text-align: center;
        line-height: 32px;
        .text {
          font-size: 16px;
          color: #475669;
        }
        .sub-text {
          font-size: 12px;
          color: #998;
        }
      }
      .products-banner-box {
        padding: 0 12px 30px 12px;
        .products-banner {
          width: 100%;
          height: 320px;
          padding: 10px;
          border: 1px solid #ebeef5;
          box-shadow: 0 0 10px #e6e6e6;
          border-radius: 3px;

          .products-item {
            display: flex;
            line-height: 1.6;
            .products-img {
              flex: 0 0 70%;
              background: #f8f9fb;
              height: 300px;
            }
            .products-intro {
              flex: 0 0 30%;
              padding-left: 20px;
              height: 300px;
              .products-intro-hd {
                border-bottom: 1px solid #dddddd;
                .name {
                  font-size: 16px;
                  color: #333;
                }
                .des {
                  color: #999;
                  margin: 6px 0 12px 0;
                  font-size: 10px;
                }
              }
              .features-box {
                h3 {
                  font-size: 14px;
                  margin: 6px 0;
                }
                .features-item {
                  .title {
                    font-size: 12px;
                  }
                  .des {
                    color: #999;
                    font-size: 10px;
                    margin-bottom: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  &-title {
    text-align: center;
    font-size: 18px;
    padding-top: 30px;
    color: #fff;
  }
}
.admin {
  &-container {
    background-color: #f1f6fa;
    padding-bottom: 10px;
  }
  &-box {
    padding: 20px 30px;
    background-color: #4f535b;
    .grid-content {
      display: flex;
      align-items: center;
      height: 120px;
      padding: 20px 30px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;

      .grid-content-left {
        flex: 1;
        text-align: center;
        .number {
          width: 50px;
          font-size: 26px;
          color: #0ee2e5;
        }
        .text {
          width: 60px;
          margin-top: 6px;
          padding-top: 8px;
          border-top: 2px solid #0ee2e5;
          color: #fff;
        }
      }
      .grid-content-right {
        width: 60px;
        float: right;
        img {
          width: 60px;
          height: 60px;
        }
      }

      .grid-content-item {
        width: 100%;
        color: #fff;
        margin-bottom: 10px;
        .num {
          color: #0ee2e5;
          float: right;
        }
      }
    }
    .grid-content-column {
      flex-direction: column;
      justify-content: center;
      .router-link-active {
        width: 100%;
      }
      .router-link-active:last-child .grid-content-item {
        margin-bottom: 0;
      }
    }
  }

  &-box2 {
    padding: 10px;
     background-color: #F1F6FA;
    .box2-container {
      padding: 30px 0;
      background: #fff;
      border-radius: 3px;
      height: 320px;
      .grid2-content {
        position: relative;
        padding: 0 20px;
        &::after {
          position: absolute;
          content: "";
          top: 0;
          right: 0;
          height: 260px;
          border-right: 1px solid #e6e6e6;
        }

        .icon-box {
          text-align: center;
          .icon-box-bd {
            display: inline-block;
            width: 60px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            border-radius: 100%;
            background: #61adf7;
            margin: 10px 0 20px 0;
            box-shadow: 0 0 6px #3eaefe;
            .icon-box-img {
              width: 42px;
              height: 42px;
              vertical-align: middle;
            }
          }
          .bg_orange {
            background: #f5b955;
            box-shadow: 0 0 6px #feef3e;
          }
          .bg_red {
            background: #f59a98;
            box-shadow: 0 0 6px #fe593e;
          }
          .bg_green {
            background: #a2ea98;
            box-shadow: 0 0 6px #64fe3e;
          }
        }
        .grid2-content-title {
          text-align: center;
          margin-bottom: 10px;
        }
        .grid2-content-wrap {
          margin-top: 10px;
          .item-wrap {
            display: flex;
            .item-title {
              flex: 1;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
      .el-col:nth-child(4n+4) .grid2-content::after{
        border-right:none !important;
       }
    }
  }
  &-box3 {
    padding-top: 0;  
  }
}
}
</style>
