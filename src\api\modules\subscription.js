import request from '@/utils/request'
//系统设置
//订阅配置---订阅信息
export function msgDisposeList() {
  // debugger
  return request({
    url: '/api/msgPush/msgDisposeList',
    method: 'post',
    
  })
}
//订阅配置---订阅配置提交
export function updateMsgDispose(data) {
  // debugger
  return request({
    url: '/api/msgPush/updateMsgDispose',
    method: 'post',
    params:data
    
  })
}
//内容订阅---订阅信息
export function dispose(data) {
  // debugger
  return request({
    url: '/api/msgPush/dispose',
    method: 'post',
    params:data
    
  })
}
//内容订阅---开启订阅
export function modifyDispose(data) {
  // debugger
  return request({
    url: '/api/msgPush/modifyDispose',
    method: 'post',
    params:data
    
  })
}