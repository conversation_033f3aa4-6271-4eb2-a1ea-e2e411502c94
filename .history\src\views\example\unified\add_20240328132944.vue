<template>
    <el-dialog width="460px" :visible.sync="true" title="添加产品" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <el-form ref="dataForm" :model="dataForm" :rules="dataRule" label-width="120px"
            @keyup.enter.native="dataFormSubmitHandle()">
            <el-form-item label="名称" prop="NAME">
                <el-input v-model="dataForm.NAME" disabled />
            </el-form-item>
            <el-form-item label="阈值条件">
                <el-input value="大于等于" disabled />
            </el-form-item>
            <el-form-item label="警告阈值" prop="waring_threshold">
                <el-input v-model="dataForm.waring_threshold" />
            </el-form-item>
            <el-form-item label="严重阈值" prop="severe_threshold">
                <el-input v-model="dataForm.severe_threshold" />
            </el-form-item>
        </el-form>
        <template slot="footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">确定
            </el-button>
        </template>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            // visible: false,
            dataForm: {},
            // dataRule:{}
        }
    },
    computed: {
        dataRule() {
            return {
                waring_threshold: [
                    { required: true, validator: validNumberPass1, trigger: 'blur' },
                ],
                severe_threshold: [
                    { required: true, validator: validNumberPass1, trigger: 'blur' },
                ],
            };
        },
    },
    methods: {
        dataFormSubmitHandle(){

        }
    },

}
</script>

<style lang="scss" scoped></style>