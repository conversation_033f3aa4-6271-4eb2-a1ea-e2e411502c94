<template>
  <!-- <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">网络威胁</h3>
      </div> -->
      
  <div>
    <div class="serch-box clearfix">
      <div class="filter-container">
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          @click="fireThreatRefresh"
        >
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-select
          v-model="listQueryFireThreat.date"
          placeholder="请选择"
          @change="fireThreatDateSelct"
        >
          <el-option
            v-for="item in dateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="page-box">
        <el-pagination
          @size-change="handleSizeChangeFireThreat"
          @current-change="handleCurrentChangFireThreat"
          :current-page="listQueryFireThreat.currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="sizes, prev,slot, next,total"
          :total="listQueryFireThreat.total"
        >
          <span class="pageNum">
            {{ this.listQueryFireThreat.page }}
            <i class="divider">/</i>
            {{ this.listQueryFireThreat.totalPage }}
          </span>
        </el-pagination>
      </div>
    </div>
    <div class="table-box">
      <el-table-bar>
        <el-table
          :data="fireThreatData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
          v-if="versionSreen==3"
        >
          <el-table-column
            prop="name"
            label="名称"
            width="210"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="类型" prop="type" show-overflow-tooltip />
          <el-table-column
            label="级别"
            align="center"
            width="100"
            sortable
            show-overflow-tooltip
            prop="level"
          />
          <el-table-column
            prop="source"
            label="源"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="destination"
            label="目的"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="endTime"
            label="结束时间"
            width="210"
            :formatter="dateFormat"
            sortable
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
        <el-table v-else
          :data="fireThreatData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
        >
          <el-table-column
            prop="name"
            label="名称"
            width="210"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="类型" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.threatType.name }}-{{ scope.row.threatSubtype.name }}
            </template>
          </el-table-column>

          <el-table-column
            label="级别"
            align="center"
            width="100"
            sortable
            show-overflow-tooltip
            prop="severity"
          >
            <template slot-scope="scope">
              <div>
                <span v-if="scope.row.severity == 2" class="bg-level bg-warm"
                  >中</span
                >
                <span v-if="scope.row.severity == 4" class="bg-level bg-warn"
                  >严重</span
                >
                <span v-if="scope.row.severity == 3" class="bg-level bg-danger"
                  >高</span
                >
                <span v-if="scope.row.severity == 1" class="bg-level bg-normal"
                  >低</span
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="source.hostName"
            label="源"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="destination.hostName"
            label="目的"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="protocolApplication"
            label="应用/协议"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="endTime"
            label="结束时间"
            width="210"
            :formatter="dateFormat"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="defender.name"
            label="检测引擎"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </el-table-bar>
    </div>
  </div>
  <!-- </div>
  </div> -->
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { Loading } from 'element-ui';
import { mapGetters } from 'vuex';
import moment from 'moment'; //时间格式转化
import { threatLog } from '@/api/log.js';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      fireThreatData: [], //云防火墙威胁日志
      listQueryFireThreat: {
        page: 1,
        limit: 10,
        date: 'month',
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      dateOptions: [
        {
          value: 'hour',
          label: '最近一小时',
        },
        {
          value: 'day',
          label: '最近一天',
        },
        {
          value: 'month',
          label: '最近一月',
        },
      ],
      versionSreen:null,
    };
  },
  created() {
    this.getFireThreatData();
    this.versionSreen = this.version;
  },
  computed: {
    ...mapGetters(['userid', 'usertype','version', 'tenantid']),
  },
  methods: {
    dateFormat(row, column) {
      var moment = require('moment');
      var date = row[column.property];
      return moment(date).format('YYYY-MM-DD hh:mm:ss');
    },
    getFireThreatData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let systemData = {
        date: this.listQueryFireThreat.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryFireThreat.page,
        limit: this.listQueryFireThreat.limit,
        userId: this.userid,
      };
      // console.log(systemData)
      threatLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            setTimeout(() => {
              this.listLoading.close();
            }, 200);
            if (JSON.stringify(res.data) != '{}' && res.data.result != null) {
              // console.log(res.data);
              this.fireThreatData = res.data.result;
              this.listQueryFireThreat.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryFireThreat.totalPage = 1;
              } else {
                this.listQueryFireThreat.totalPage = Math.ceil(
                  this.listQueryFireThreat.total / this.listQueryFireThreat.limit
                );
              }
            } else {
              this.listQueryFireThreat.totalPage = 1;
              this.fireThreatData = [];
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //条件删选
    fireThreatDateSelct() {
      this.getFireThreatData();
    },
    //云防火墙威胁日志刷新
    fireThreatRefresh() {
      this.getFireThreatData();
    },
    //云防火墙---威胁日志
    handleSizeChangeFireThreat(val) {
      // console.log(`每页 ${val} 条`);
      this.listQueryFireThreat.limit = val;
      this.getFireThreatData();
    },
    handleCurrentChangFireThreat(val) {
      // console.log(`当前页: ${val}`);
      this.listQueryFireThreat.page = val;
      this.getFireThreatData();
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 230px);
}

.mainWrapper {
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.el-tabs--border-card {
  border: none;
  box-shadow: none;
}
.el-date-editor::v-deep .el-range__icon {
  position: absolute;
  right: 5px;
  top: 2px;
}
.el-date-editor::v-deep .el-input__inner {
  padding-left: 15px;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 5px;
}
.bg-warm {
  background: rgba(242, 174, 27, 1);
}
.bg-warn {
  background: rgba(236, 89, 96, 1);
}
.bg-normal {
  background: rgba(0, 94, 164, 1);
}
.bg-danger {
  background: rgba(188, 78, 115, 1);
}
.loginSelect {
  width: 120px;
  border-right: 1px solid #dae0e6;
}
.page-btn {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  border: none;
  outline: none;
  padding: 0 6px;
  background: none;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }

  i {
    width: 34px;
    height: 34px;
    background: #fff;
    border: 1px solid #d7dce2;
    border-radius: 2px;
    padding: 8px;
    font-size: 14px;
    color: #005ea4;
  }
}
</style>
