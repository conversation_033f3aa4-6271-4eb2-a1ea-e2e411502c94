<template>
  <div :id="id" :style="style"></div>
</template>

<script>
import { math } from '@/utils/math.js';
export default {
  name: 'LiquidFill',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.init();
    });
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      this.$nextTick(() => {
        this.setOption();
        // console.log(this.chartData)
      });
      window.addEventListener('resize', this.chart.resize);
    },
    setOption() {
      const that = this;
      let color = this.getColor(this.chartData.type);
      // console.log(color);
      let option = {};
      option = {
        series: [
          {
            type: 'liquidFill',
            radius: '72%',
            center: ['50%', '50%'],
            name: this.chartData.name,
            data: [
              {
                value: this.chartData.value,
                amplitude: 8, // 水波纹的振幅
                itemStyle: {
                  //一个波浪设置透明度
                  normal: {
                    color: color[0],
                  },
                },
              },
              {
                value: this.chartData.value + 0.02,
                amplitude: 6, // 水波纹的振幅
                itemStyle: {
                  //一个波浪设置透明度
                  normal: {
                    color: color[1],
                  },
                },
              },
              {
                value: this.chartData.value + 0.04,
                amplitude: 6, // 水波纹的振幅
                itemStyle: {
                  color: color[2],
                },
              },
            ],
            itemStyle: {
              opacity: 0.9,
              shadowBlur: 0,
              shadowColor: '#fff',
            },
            emphasis: {
              itemStyle: {
                opacity: 0.9,
              },
            },
            label: {
              fontSize: 18,
              color: '#999',
              fontWeight: 'normal',
              insideColor: '#fff',
              align: 'center',
              baseline: 'middle',
              position: 'inside',
              formatter: function (params) {
                // console.log('params: ', params.value);
                return math.multiply(params.value, 100) + '%';
              },
            },
            waveAnimation: true,
            animationDuration: 0,
            animationDurationUpdate: 0,
            outline: {
              itemStyle: {
                borderColor: color[0],
                borderWidth: 2,
                shadowBlur: 0,
                shadowColor: '#fff',
              },
              borderDistance: 0,
            },
            backgroundStyle: {
              color: '#fff',
            },
          },
        ],
      };
      this.chart.setOption(option, true);
    },
    //设置颜色
    getColor(value) {
      //['#108FF4',#ffc700','#f95b6c','#00A700']
      return value == 1
        ? ['rgba(16,143,244,1)', 'rgba(16,143,244,0.6)', 'rgba(16,143,244,.3)']
        : value == 2
          ? ['rgba(255,199,0,1)', 'rgba(255,199,0,0.6)', 'rgba(255,199,0,.3)']
          : value == 3
            ? ['rgba(249,91,108,1)', 'rgba(249,91,108,0.6)', 'rgba(249,91,108,.3)']
            : ['rgba(0,167,0,1)', 'rgba(0,167,0,0.6)', 'rgba(0,167,0,.3)'];
    },
  },
};
</script>
