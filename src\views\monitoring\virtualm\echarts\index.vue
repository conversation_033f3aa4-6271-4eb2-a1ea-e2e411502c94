<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="echarts-box">
          <div class="echarts-box-hd clearfix">
            <span class="title">CPU(%):</span>
            <el-select v-model="value" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
           
          </div>
          <div class="echarts-box-bd">
            <div id="chart1" :style="{width: '100%', height: '300px'}"></div>
          </div>
        </div>
        <div class="echarts-box">
          <div class="echarts-box-hd clearfix">
            <span class="title">内存(%):</span>
            <el-select v-model="value" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
           
          </div>
          <div class="echarts-box-bd">
            <div id="chart2" :style="{width: '100%', height: '300px'}"></div>
          </div>
        </div>

        <div class="echarts-box">
          <div class="echarts-box-hd clearfix">
            <span class="title">磁盘IO(%):</span>
            <el-select v-model="value" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
           
          </div>
          <div class="echarts-box-bd">
            <div id="chart3" :style="{width: '100%', height: '300px'}"></div>
          </div>
        </div>
        <div class="echarts-box">
          <div class="echarts-box-hd clearfix">
            <span class="title">网卡(B/s):</span>
            <el-select v-model="value" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            
          </div>
          <div class="echarts-box-bd">
            <div id="chart4" :style="{width: '100%', height: '300px'}"></div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          label: "15分钟",
          value: "15分钟"
        },
        {
          label: "30分钟",
          value: "20分钟"
        },
        {
          label: "1小时",
          value: "1小时"
        },
        {
          label: "6小时",
          value: "6小时"
        }
      ],
      options2: [
        {
          label: "Average",
          value: "Average"
        },
        {
          label: "1",
          value: "1"
        },
        {
          label: "2",
          value: "2"
        },
        {
          label: "3",
          value: "3"
        }
      ],
      value: "15分钟",
      value2: "Average",
      echartsArr: []
    };
  },
  created() {},
  mounted() {
    this.initCharts();
    const self = this;
    window.addEventListener("resize", function() {
      self.echartsArr.forEach(item => {
        item.resize();
      });
    });
  },
  watch: {},
  methods: {
    initCharts() {
      this.chart_cpu = this.$echarts.init(document.getElementById("chart1"));
      this.chart_ram = this.$echarts.init(document.getElementById("chart2"));
      this.chart_disk = this.$echarts.init(document.getElementById("chart3"));
      this.chart_card = this.$echarts.init(document.getElementById("chart4"));
      this.echartsArr = [
        this.chart_cpu,
        this.chart_ram,
        this.chart_disk,
        this.chart_card
      ];

      this.setOptions();
    },
    setOptions() {
      this.chart_cpu.setOption({
        color: "#76D1F2",
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "10:31",
            "10:32",
            "10:33",
            "10:34",
            "10:35",
            "10:36",
            "10:37",
            "10:38",
            "10:39",
            "10:40",
            "10:41",
            "10:42",
            "10:43",
            "10:44",
            "10:45"
          ]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [
              "20",
              "20.36",
              "20.36",
              "20.36",
              "21.36",
              "20.36",
              "23.36",
              "20.36",
              "20.36",
              "20.36",
              "20.16",
              "20.28",
              "20.36",
              "20.36",
              "20.36"
            ],
            type: "line",
            areaStyle: {}
          }
        ]
      });
      this.chart_ram.setOption({
        color: "#76D1F2",
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "10:31",
            "10:32",
            "10:33",
            "10:34",
            "10:35",
            "10:36",
            "10:37",
            "10:38",
            "10:39",
            "10:40",
            "10:41",
            "10:42",
            "10:43",
            "10:44",
            "10:45"
          ]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [
              "20",
              "20.36",
              "20.36",
              "20.36",
              "21.36",
              "20.36",
              "23.36",
              "20.36",
              "20.36",
              "20.36",
              "20.16",
              "20.28",
              "20.36",
              "20.36",
              "20.36"
            ],
            type: "line",
            areaStyle: {}
          }
        ]
      });
      this.chart_disk.setOption({
        color: "#76D1F2",
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "10:31",
            "10:32",
            "10:33",
            "10:34",
            "10:35",
            "10:36",
            "10:37",
            "10:38",
            "10:39",
            "10:40",
            "10:41",
            "10:42",
            "10:43",
            "10:44",
            "10:45"
          ]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [
              "20",
              "30.36",
              "30.36",
              "20.36",
              "21.36",
              "20.36",
              "53.36",
              "20.36",
              "20.36",
              "20.36",
              "20.16",
              "50.98",
              "20.36",
              "20.36",
              "20.36"
            ],
            type: "line",
            areaStyle: {}
          }
        ]
      });
      this.chart_card.setOption({
        color: "#76D1F2",
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "10:31",
            "10:32",
            "10:33",
            "10:34",
            "10:35",
            "10:36",
            "10:37",
            "10:38",
            "10:39",
            "10:40",
            "10:41",
            "10:42",
            "10:43",
            "10:44",
            "10:45"
          ]
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            data: [
              "20",
              "60.36",
              "10.36",
              "20.36",
              "21.36",
              "20.36",
              "53.36",
              "70.36",
              "20.36",
              "20.36",
              "20.16",
              "50.98",
              "20.36",
              "20.36",
              "20.36"
            ],
            type: "line",
            areaStyle: {}
          }
        ]
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  .mainBox {
    background: #fff;
  }
}
.el-select::v-deep {
  width: 100px;
}
</style>

