<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">
          <span class="el-icon-arrow-left back-icon" @click="$router.back(-1)"></span>监控数据
        </h3>
      </div>
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="echarts-box-wrap">
          <div class="echarts-box">
            <div class="echarts-box-hd clearfix">
              <span class="title">CPU(%):</span>
              <el-select v-model="cpuValue" placeholder="请选择" @change="cpuChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="echarts-box-bd">
              <div id="chart1" :style="{ width: '100%', height: '300px' }"></div>
            </div>
          </div>
          <div class="echarts-box">
            <div class="echarts-box-hd clearfix">
              <span class="title">内存:</span>
              <el-select v-model="memoryValue" placeholder="请选择" @change="memoryChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="echarts-box-bd">
              <div id="chart2" :style="{ width: '100%', height: '300px' }"></div>
            </div>
          </div>

          <div class="echarts-box">
            <div class="echarts-box-hd clearfix">
              <span class="title">磁盘IO:</span>
              <el-select v-model="diskValue" placeholder="请选择" @change="diskChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select v-model="diskRwValue" placeholder="请选择" @change="diskRwChange">
                <el-option
                  v-for="item in diskOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="echarts-box-bd">
              <div id="chart3" :style="{ width: '100%', height: '300px' }" v-if="diskData.length>0"></div>
              <div class="echarts-null-box" v-else>
                <img src="@/assets/data_null.png" alt />
                <p>暂无数据</p>
              </div>
            </div>
          </div>
          <div class="echarts-box">
            <div class="echarts-box-hd clearfix">
              <span class="title">网卡:</span>
              <el-select v-model="cardValue" placeholder="请选择" @change="cardChange">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-select v-model="cardInoutValue" placeholder="请选择" @change="cardInoutChange">
                <el-option
                  v-for="item in cardOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div class="echarts-box-bd">
              <div id="chart4" :style="{ width: '100%', height: '300px' }" v-if="cardData.length>0"></div>
              <div class="echarts-null-box" v-else>
                <img src="@/assets/data_null.png" alt />
                <p>暂无数据</p>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script>
import { echartsList } from "@/api/monitoring.js";
import moment from "moment";
export default {
  data() {
    return {
      uuid: "",
      timestamp: "", //当前时间
      options: [
        {
          label: "15分钟",
          value: "15分钟",
        },
        {
          label: "30分钟",
          value: "30分钟",
        },
        {
          label: "1小时",
          value: "1小时",
        },
        {
          label: "6小时",
          value: "6小时",
        },
      ],
      diskOptions: [
        {
          label: "读速度",
          value: "DiskReadBytes",
        },
        {
          label: "写速度",
          value: "DiskWriteBytes",
        },
      ],
      cardOptions: [
        {
          label: "入速度",
          value: "NetworkInBytes",
        },
        {
          label: "出速度",
          value: "NetworkOutBytes",
        },
      ],
      cpuValue: "15分钟", //cpu
      memoryValue: "15分钟", //内存
      diskValue: "15分钟", //磁盘
      cardValue: "15分钟", //网卡
      cupStartTime: "", //cpu开始时间
      memoryStartTime: "", //内存开始时间
      diskStartTime: "", //磁盘开始时间
      cardStartTime: "", //网卡开始时间
      cpuData: [],
      cpuDataValues: [],
      memoryData: [],
      memoryDataValues: [],
      diskData: [], //磁盘数据
      diskDataValues: [],
      cardData: [], //网卡数据
      cardDataValues: [],
      cpuDataTimes: [],
      echartsArr: [],
      timeDiver: 60,
      memoryTimeDiver: 60,
      diskTimeDiver: 60,
      cardTimeDiver: 60,
      diskRwValue: "读速度",
      diskSelectmetric: "",
      cardInoutValue: "入速度",
      cardSelectmetric: "",
      cpuName: "",
      memoryName: "",
      diskName: "",
      cardName: "",
    };
  },
  created() {
    // console.log(this.$route.query);
    this.uuid = this.$route.query.id;
    this.getData();
  },
  mounted() {
    this.currentTime();

    const self = this;
    window.addEventListener("resize", function () {
      self.echartsArr.forEach((item) => {
        item.resize();
      });
    });
  },
  watch: {},
  methods: {
    currentTime() {
      setInterval(this.getData, 60000);
    },
    getData() {
      this.getCpuData();
      this.getMemoryData();
      this.getDiskData();
      this.getCardData();
    },
    //cpu接口数据
    getCpuData() {
      this.getNowtime();
      if (this.cpuValue == "15分钟") {
        this.cupStartTime = this.beforeNowtimeByMinu(15);
        this.timeDiver = 60;
        // console.log(this.cupStartTime);
      } else if (this.cpuValue == "30分钟") {
        this.cupStartTime = this.beforeNowtimeByMinu(30);
        this.timeDiver = 60 * 2;
      } else if (this.cpuValue == "1小时") {
        this.cupStartTime = this.beforeNowtime(1);
        this.timeDiver = 60 * 4;
      } else if (this.cpuValue == "6小时") {
        this.cupStartTime = this.beforeNowtime(6);
        this.timeDiver = 60 * 16;
      }
      let data = {
        startTime: this.cupStartTime, // 开始时间（时间戳）
        endTime: this.timestamp, //结束时间(时间戳)
        uuid: this.uuid, // 云主机uuid
        metricName: "CPUAverageUsedUtilization",
      };
      // console.log(data);
      let that = this;
      echartsList(data)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            that.cpuDataValues = [];
            if (res.data.data.length > 0) {
              that.cpuData = res.data.data;
              //this.cpuName= res.data.data[0].labels

              for (let i = 0; i < that.cpuData.length; i++) {
                let dataList = {
                  value: [],
                };
                dataList.value = [
                  that.cpuData[i].time * 1000,
                  that.cpuData[i].value.toFixed(2),
                ];
                // this.cpuDataValues.push(this.cpuData[i].value.toFixed(2));
                // this.cpuDataTimes.push(
                //   moment(this.cpuData[i].time * 1000).format("YYYY-MM-DD HH:mm")
                // );
                that.cpuDataValues.push(dataList);
              }
              // console.log(this.cpuDataValues);
            } else {
              let num =
                (that.timestamp * 1000 - that.cupStartTime * 1000) /
                (that.timeDiver * 1000);

              for (let i = 0; i <= num; i++) {
                let dataList = {
                  value: [],
                };
                if (that.diskValue == "15分钟") {
                  dataList.value = [that.beforeNowtimeByMinu(i) * 1000, 0];
                that.cpuDataValues.push(dataList);
                  // console.log(this.cupStartTime);
                } else if (that.diskValue == "30分钟") {
                  dataList.value = [that.beforeNowtimeByMinu(i) * 1000, 0];
                that.cpuDataValues.push(dataList);
                } else if (that.diskValue == "1小时") {
                 dataList.value = [that.beforeNowtime(i) * 1000, 0];
                that.cpuDataValues.push(dataList);
                } else if (that.diskValue == "6小时") {
                  dataList.value = [that.beforeNowtime(i) * 1000, 0];
                  that.cpuDataValues.push(dataList);
                }
              }
              // console.log(this.cpuDataValues);
            }

            that.$nextTick(() => {
              that.initChartsCpu();
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //内存接口数据
    getMemoryData() {
      this.getNowtime();
      if (this.memoryValue == "15分钟") {
        this.memoryStartTime = this.beforeNowtimeByMinu(15);
        this.memoryTimeDiver = 60;
        // console.log(this.cupStartTime);
      } else if (this.memoryValue == "30分钟") {
        this.memoryStartTime = this.beforeNowtimeByMinu(30);
        this.memoryTimeDiver = 60 * 2;
      } else if (this.memoryValue == "1小时") {
        this.memoryStartTime = this.beforeNowtime(1);
        this.memoryTimeDiver = 60 * 4;
      } else if (this.memoryValue == "6小时") {
        this.memoryStartTime = this.beforeNowtime(6);
        this.memoryTimeDiver = 60 * 16;
      }
      let data = {
        startTime: this.memoryStartTime, // 开始时间（时间戳）
        endTime: this.timestamp, //结束时间(时间戳)
        uuid: this.uuid, // 云主机uuid
        metricName: "MemoryUsedBytes",
      };
      // console.log(data);
      let that = this;
      echartsList(data)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            that.memoryData = res.data.data;
            that.memoryDataValues = [];
            if (that.memoryData.length > 0) {
              for (let i = 0; i < that.memoryData.length; i++) {
                let dataList = {
                  value: [],
                };
                dataList.value = [
                  that.memoryData[i].time * 1000,
                  that.memoryData[i].value,
                ];

                that.memoryDataValues.push(dataList);
              }
            } else {
              let newArr = [];
              let num =
                (that.timestamp * 1000 - that.memoryStartTime * 1000) /
                (that.timeDiver * 1000);
              for (let i = 0; i <= num; i++) {
                // console.log(i);
                let dataList = {
                  value: [],
                };
                if (that.diskValue == "15分钟") {
                  dataList.value = [that.beforeNowtimeByMinu(i) * 1000, 0];

                  that.memoryDataValues.push(dataList);
                  // console.log(this.cupStartTime);
                } else if (that.diskValue == "30分钟") {
                  dataList.value = [that.beforeNowtimeByMinu(i) * 1000, 0];

                  that.memoryDataValues.push(dataList);
                } else if (that.diskValue == "1小时") {
                  dataList.value = [that.beforeNowtime(i) * 1000, 0];

                  that.memoryDataValues.push(dataList);
                } else if (that.diskValue == "6小时") {
                  dataList.value = [that.beforeNowtime(i) * 1000, 0];

                  that.memoryDataValues.push(dataList);
                }
              }
              // console.log(that.memoryDataValues);
            }

            that.$nextTick(() => {
              that.initChartsMemory();
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },

    //磁盘IO接口数据
    getDiskData() {
      this.getNowtime();
      if (this.diskValue == "15分钟") {
        this.diskStartTime = this.beforeNowtimeByMinu(15);
        this.diskTimeDiver = 60;
        // console.log(this.cupStartTime);
      } else if (this.diskValue == "30分钟") {
        this.diskStartTime = this.beforeNowtimeByMinu(30);
        this.diskTimeDiver = 60 * 2;
      } else if (this.diskValue == "1小时") {
        this.diskStartTime = this.beforeNowtime(1);
        this.diskTimeDiver = 60 * 4;
      } else if (this.diskValue == "6小时") {
        this.diskStartTime = this.beforeNowtime(6);
        this.diskTimeDiver = 60 * 16;
      }
      if (this.diskRwValue == "读速度") {
        this.diskSelectmetric = "DiskReadBytes";
      } else if (this.diskRwValue == "写速度") {
        this.diskSelectmetric = "DiskWriteBytes";
      }
      let data = {
        startTime: this.diskStartTime, // 开始时间（时间戳）
        endTime: this.timestamp, //结束时间(时间戳)
        uuid: this.uuid, // 云主机uuid
        metricName: this.diskSelectmetric,
      };
      // console.log(data);
      let that = this;
      echartsList(data)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            that.diskData = res.data.data;
            that.diskDataValues = [];
            if (that.diskData.length > 0) {
              that.diskName = res.data.data[0].labels.DiskDeviceLetter;
              //console.log(  this.diskName);
              for (let i = 0; i < that.diskData.length; i++) {
                let dataList = {
                  value: [],
                };
                dataList.value = [
                  that.diskData[i].time * 1000,
                  that.diskData[i].value,
                ];

                that.diskDataValues.push(dataList);
              }
              that.$nextTick(() => {
                that.initChartsDisk();
              });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //网卡接口数据
    getCardData() {
      this.getNowtime();
      if (this.cardValue == "15分钟") {
        this.cardStartTime = this.beforeNowtimeByMinu(15);
        this.cardTimeDiver = 60;
        // console.log(this.cupStartTime);
      } else if (this.cardValue == "30分钟") {
        this.cardStartTime = this.beforeNowtimeByMinu(30);
        this.cardTimeDiver = 60 * 2;
      } else if (this.cardValue == "1小时") {
        this.cardStartTime = this.beforeNowtime(1);
        this.cardTimeDiver = 60 * 4;
      } else if (this.cardValue == "6小时") {
        this.cardStartTime = this.beforeNowtime(6);
        this.cardTimeDiver = 60 * 16;
      }
      if (this.cardInoutValue == "入速度") {
        this.cardSelectmetric = "NetworkInBytes";
      } else if (this.cardInoutValue == "出速度") {
        this.cardSelectmetric = "NetworkOutBytes";
      }
      let data = {
        startTime: this.cardStartTime, // 开始时间（时间戳）
        endTime: this.timestamp, //结束时间(时间戳)
        uuid: this.uuid, // 云主机uuid
        metricName: this.cardSelectmetric,
      };
      // console.log(data);
      let that = this;
      echartsList(data)
        .then((res) => {
          // console.log(res);

          if (res.code == 1) {
            that.cardData = res.data.data;
            that.cardDataValues = [];
            if (that.cardData.length > 0) {
              that.cardName = res.data.data[0].labels.NetworkDeviceLetter;
              //console.log(  this.cardName);

              for (let i = 0; i < that.cardData.length; i++) {
                let dataList = {
                  value: [],
                };
                dataList.value = [
                  that.cardData[i].time * 1000,
                  that.cardData[i].value,
                ];

                that.cardDataValues.push(dataList);

              }
              that.$nextTick(() => {
                that.initChartsCard();
              });
            } else {
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },

    //获取当前时间
    getNowtime() {
      this.timestamp = parseInt(new Date().getTime() / 1000);
      //console.log(this.timestamp);
    },
    //时间位数为1位数时，前面补0
    setFormat(x) {
      if (x < 10) {
        x = "0" + x;
      }
      return x;
    },
    //获取从现在到 beforetime 小时前的时间（beforetime 只能是整数）
    beforeNowtime(beforetime) {
      var date = new Date(); //日期对象
      date.setHours(date.getHours() - beforetime);
      var now = "";
      now = date.getFullYear() + "-"; //读英文就行了
      now =
        now +
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) +
        "-"; //取月的时候取的是当前月-1如果想取当前月+1就可以了
      now = now + this.setFormat(date.getDate()) + " ";
      now = now + this.setFormat(date.getHours()) + ":";
      now = now + this.setFormat(date.getMinutes()) + ":";
      now = now + this.setFormat(date.getSeconds()) + "";
      return parseInt(new Date(now).getTime() / 1000);
    },
    //获取从现在到 beforetime 分钟前的时间
    beforeNowtimeByMinu(beforetime) {
      var date = new Date(); //日期对象
      date.setMinutes(date.getMinutes() - beforetime);
      var now = "";
      now = date.getFullYear() + "-"; //读英文就行了
      now =
        now +
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) +
        "-"; //取月的时候取的是当前月-1如果想取当前月+1就可以了
      now = now + this.setFormat(date.getDate()) + " ";
      now = now + this.setFormat(date.getHours()) + ":";
      now = now + this.setFormat(date.getMinutes()) + ":";
      now = now + this.setFormat(date.getSeconds()) + "";
      return parseInt(new Date(now).getTime() / 1000);
    },

    //cpu时间选择
    cpuChange(value) {
      this.cpuValue = value;
      // console.log(this.cpuValue);
      this.getCpuData();
    },
    //内存时间选择
    memoryChange(value) {
      this.memoryValue = value;
      // console.log(this.memoryValue);
      this.getMemoryData();
    },
    //磁盘io时间选择
    diskChange(value) {
      this.diskValue = value;
      // console.log(this.diskValue);
      this.getDiskData();
    },
    //磁盘io读写速度选择
    diskRwChange(value) {
      this.diskSelectmetric = value;
      // console.log(this.diskSelectmetric);
      this.getDiskData();
    },
    //网卡io时间选择
    cardChange(value) {
      this.cardValue = value;
      // console.log(this.cardValue);
      this.getCardData();
    },
    //磁盘io读写速度选择
    cardInoutChange(value) {
      this.cardSelectmetric = value;
      // console.log(this.cardSelectmetric);
      this.getCardData();
    },
    unique(arr) {
      const res = new Map();
      return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1));
    },

    initChartsCpu() {
      this.chart_cpu = this.$echarts.init(document.getElementById("chart1"));

      this.echartsArr.push(this.chart_cpu);

      this.setOptionsCpu();
    },
    initChartsMemory() {
      this.chart_memory = this.$echarts.init(document.getElementById("chart2"));

      this.echartsArr.push(this.chart_memory);

      this.setOptionsMemory();
    },
    //磁盘io
    initChartsDisk() {
      this.chart_disk = this.$echarts.init(document.getElementById("chart3"));

      this.echartsArr.push(this.chart_disk);

      this.setOptionsDisk();
    },
    //网卡
    initChartsCard() {
      this.chart_card = this.$echarts.init(document.getElementById("chart4"));
      this.echartsArr.push(this.chart_card);
      this.setOptionsCard();
    },
    setOptionsCpu() {
      this.chart_cpu.setOption({
        color: "#25A1FF",
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            //console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.value[0]).format("YYYY-MM-DD HH:mm") +
                "</br>" +
                item.marker +
                " " +
                item.seriesName +
                " : " +
                item.value[1] +
                "%" +
                "</br>";
            });
            return result;
          },
        },

        xAxis: {
          type: "time",
          min: this.cupStartTime * 1000,
          max: this.timestamp * 1000,
          interval: this.timeDiver * 1000,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#97A4B6",
            fontSize: 16,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(value).format(" HH:mm");
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#97A4B6",
            fontSize: 14,
            formatter: "{value}%",
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dotted", //设置网格线类型 dotted：虚线   solid:实线
              color: "#DAE0E6",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.cpuDataValues,
            symbol: "none",
            type: "line",
            name: "Average",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(37, 161, 255, 0.07)" },

                  { offset: 1, color: " rgba(37, 161, 255, 0)" },
                ]),
              },
            },
          },
        ],
      });
    },
    setOptionsMemory() {
      let that = this;
      this.chart_memory.setOption({
        color: "#25A1FF",
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            //console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.value[0]).format("YYYY-MM-DD HH:mm") +
                "</br>" +
                item.marker +
                " " +
                item.seriesName +
                " : " +
                that.diskSize(item.value[1]) +
                "</br>";
            });
            return result;
          },
        },

        xAxis: {
          type: "time",
          min: this.memoryStartTime * 1000,
          max: this.timestamp * 1000,
          interval: this.memoryTimeDiver * 1000,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#97A4B6",
            fontSize: 16,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(value).format(" HH:mm");
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#97A4B6",
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = that.diskSize(value);
              return capacity;
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dotted", //设置网格线类型 dotted：虚线   solid:实线
              color: "#DAE0E6",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.memoryDataValues,
            symbol: "none",
            type: "line",
            name: "Used",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(37, 161, 255, 0.07)" },

                  { offset: 1, color: " rgba(37, 161, 255, 0)" },
                ]),
              },
            },
          },
        ],
      });
    },
    setOptionsDisk() {
      let that = this;
      this.chart_disk.setOption({
        color: "#25A1FF",
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            //console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.value[0]).format("YYYY-MM-DD HH:mm") +
                "</br>" +
                item.marker +
                " " +
                item.seriesName +
                " : " +
                that.diskSize(item.value[1]) +
                "/s" +
                "</br>";
            });
            return result;
          },
        },

        xAxis: {
          type: "time",
          min: this.diskStartTime * 1000,
          max: this.timestamp * 1000,
          interval: this.diskTimeDiver * 1000,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#97A4B6",
            fontSize: 16,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(value).format(" HH:mm");
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#97A4B6",
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = that.diskSize(value);
              return capacity + "/s";
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dotted", //设置网格线类型 dotted：虚线   solid:实线
              color: "#DAE0E6",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.diskDataValues,
            symbol: "none",
            type: "line",
            name: this.diskName,
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(37, 161, 255, 0.07)" },

                  { offset: 1, color: " rgba(37, 161, 255, 0)" },
                ]),
              },
            },
          },
        ],
      });
    },
    setOptionsCard() {
      let that = this;
      this.chart_card.setOption({
        color: "#25A1FF",
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            //console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.value[0]).format("YYYY-MM-DD HH:mm") +
                "</br>" +
                item.marker +
                " " +
                item.seriesName +
                " : " +
                that.diskSize(item.value[1]) +
                "/s" +
                "</br>";
            });
            return result;
          },
        },

        xAxis: {
          type: "time",
          min: this.cardStartTime * 1000,
          max: this.timestamp * 1000,
          interval: this.cardTimeDiver * 1000,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#97A4B6",
            fontSize: 16,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(value).format(" HH:mm");
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#97A4B6",
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = that.diskSize(value);
              return capacity + "/s";
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dotted", //设置网格线类型 dotted：虚线   solid:实线
              color: "#DAE0E6",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.cardDataValues,
            symbol: "none",
            type: "line",
            name: this.cardName,
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(37, 161, 255, 0.07)" },

                  { offset: 1, color: " rgba(37, 161, 255, 0)" },
                ]),
              },
            },
          },
        ],
      });
    },

    //容量转换
    diskSize(num) {
      if (num == 0) return "0 B";
      var k = 1024; //设定基础容量大小
      var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  所以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },
  },
  beforeDestroy() {
    // 离开页面的时候清除

    if (this.getData) {
      //console.log("销毁定时器");
      clearInterval(this.getData); // 在Vue实例销毁前，清除时间定时器
    }
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}
.mainWrapper {
  .mainBox {
    background: #fff;
    .echarts-box-wrap {
      padding: 20px 24px;
      box-sizing: border-box;
      .echarts-null-box {
        display: flex;
        height: 300px;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        img {
          width: 160px;
          height: auto;
        }
        p {
          font-size: 14px;
          color: #97a4b6;
          position: relative;
        }
      }
    }
  }
}
.el-select::v-deep {
  width: 100px;
}
</style>
