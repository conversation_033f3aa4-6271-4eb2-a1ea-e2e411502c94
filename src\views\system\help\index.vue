<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">帮助管理</h3>
      </div>
      <div class="search-box clearfix">
        <div class="filter-container">
          <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
            <svg-icon icon-class="refresh" />
          </el-button>
          <div class="search-container filter-item">
            <el-input
              v-model="keyWord"
              placeholder="文档名称"
              style="width: 200px"
              v-on:input="search"
            />
            <span class="el-icon-search search-btn" @click="handleSearch()"></span>
          </div>
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="addOrUpdateDoc((type = 'addFile'))"
            >添加</el-button
          >
        </div>
        <div class="page-box">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="sizes, prev,slot, next,total"
            :total="total"
          >
            <span class="pageNum">
              {{ this.listQuery.page }}
              <i class="divider">/</i>
              {{ totalPage }}
            </span>
          </el-pagination>
        </div>
      </div>
      <div class="table-box">
        <el-table-bar>
          <el-table
            :data="tableData"
            style="width: 100%; margin-bottom: 20px"
            row-key="id"
            ref="theTable"
          >
            <el-table-column label="文档名称" sortable>
              <template slot-scope="scope">
                <span class="text" @click="openDoc(scope.row)"
                  ><svg-icon icon-class="word" />{{ scope.row.title }}</span
                >
              </template></el-table-column
            >
            <!-- <el-table-column prop="annex" label="文档地址"></el-table-column> -->
            <el-table-column label="文本内容" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-html="scope.row.content" class="doc-content"></div>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间"></el-table-column>
            <el-table-column label="操作" align="center" width="150px">
              <template slot-scope="scope">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <div
                      @click="addOrUpdateDoc((type = 'editFile'), scope.row)"
                      class="opt"
                    >
                      修改
                    </div>
                    <div @click="handleDel(scope.row)" class="opt">删除</div>
                    <div @click="openDoc(scope.row)" class="opt">查看</div>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-table-bar>
      </div>
    </div>
    <!-- 添加-修改文档 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="docAddDialogVisible"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="doc-box">
        <div class="form-box clearfix">
          <div class="form-box-right">
            <el-form :model="addContent" ref="dataForm" :rules="accountRules">
              <el-form-item label="文档名称" :label-width="formLabelWidth" prop="title">
                <el-input v-model="addContent.title" autocomplete="off"></el-input>
              </el-form-item>
              <el-form-item label="描述" :label-width="formLabelWidth" prop="desc">
                <div id="wangeditor">
                  <div ref="editorElem"></div>
                </div>
              </el-form-item>
              <el-form-item label="上传附件" :label-width="formLabelWidth">
                <el-button
                  v-waves
                  class="filter-item"
                  type="primary"
                  icon="el-icon-plus"
                  @click="uploadAttachment"
                  >上传附件</el-button
                >
              </el-form-item>
              <el-form-item label="" :label-width="formLabelWidth">
                <ul class="file-list-box">
                  <li v-for="(item, index) in addContent.jsonList" :key="index">
                    <span class="name"
                      ><svg-icon icon-class="word" />{{ item.fileName }}</span
                    >
                    <span class="del" @click="delFiles(index)">
                      <svg-icon icon-class="del"
                    /></span>
                  </li>
                </ul>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="addDocFile" v-dbClick>确 定</el-button>
      </span>
    </el-dialog>
    <!-- 上传附件 -->
    <el-dialog
      title="上传附件"
      :visible.sync="uploadDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <div class="file-box" style="min-height: 320px">
        <el-upload
          class="upload-demo"
          ref="upload"
          drag
          multiple
          :on-change="handleChange"
          :file-list="fileList"
          :auto-upload="false"
          :action="upload.newurl"
          :before-remove="beforeRemove"
          :before-upload="handleFileBefore"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <!-- <div class="el-upload__tip" slot="tip">
            上传文件不超过3M
          </div> -->
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFile" v-dbClick>确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :title="info.docTitle" :visible.sync="docDialogVisible" width="50%">
      <div class="info-box">
        <div v-html="info.docContent" class="info-cont"></div>
        <div v-if="info.fileArray.length > 0" class="info-list-box">
          <h3>参考文档：</h3>
          <ul>
            <li v-for="(item, index) in info.fileArray" :key="index">
              <span class="text" @click="preVirwFile(item)"
                ><svg-icon icon-class="word" /> {{ item.fileName }}</span
              >
              <span class="download" @click="downloadFileBtn(item)"
                ><svg-icon icon-class="download"
              /></span>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :title="fileName"
      :visible.sync="docPreviewVisible"
      width="50%"
      @close="closePreview"
      :close-on-click-modal="false"
    >
      <div class="file-wrap" style="min-height: 260px">
        <!-- pdf文档预览 -->
        <div v-if="fileType == 'pdf'">
          <el-row :gutter="20">
            <span>共{{ pageCount }}页， 当前第 {{ pdfPage }} 页 </span>
            <el-button type="text" size="mini" @click.stop="previousPage"
              >上一页</el-button
            >
            <el-button type="text" size="mini" @click.stop="nextPage">下一页</el-button>
          </el-row>

          <pdf
            :src="src"
            :page="pdfPage"
            ref="pdf"
            @num-pages="pageCount = $event"
            @page-loaded="pdfPage = $event"
            style="display: inline-block; width: 100%"
          ></pdf>
        </div>
        <!-- word文档预览 -->
        <div v-else-if="fileType == 'word'">
          <div id="wordView" v-html="wordHtml"></div>
        </div>
        <!-- excel表格预览 -->
        <div v-else-if="fileType == 'excel'">
          <div id="table">
            <el-table :data="excelData" style="width: 100%">
              <el-table-column
                v-for="(value, key, index) in excelData[2]"
                :key="index"
                :prop="key"
                :label="key"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!-- 图片预览 -->
        <div v-else-if="fileType == 'image'">
          <img :src="imgUrl" alt="" />
        </div>
        <!-- ppt -->
        <div v-else-if="fileType == 'ppt'">
          <iframe
            id="iframe1"
            width="100%"
            height="100%"
            frameborder="0"
            border="0"
            marginwidth="0"
            marginheight="0"
            scrolling="no"
            allowtransparency="yes"
            :src="'http://view.officeapps.live.com/op/view.aspx?src=' + pptUrl"
          ></iframe>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import pdf from 'vue-pdf';
import mammoth from 'mammoth';
import XLSX from 'xlsx';
import { helpList, addHelp, uploadFiles, delHelpList, fileDown } from '@/api/help.js';
import { Loading } from 'element-ui';
import E from 'wangeditor'; //导入组件
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      keyWord: '', //文档关键词搜索
      tableData: [],
      docDialogVisible: false,
      docAddDialogVisible: false, //添加文档
      uploadDialogVisible: false, //上传附件
      total: 0,
      totalPage: 2,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
      },
      currentPage: 1,
      addContent: {
        id: '',
        title: '',
        createName: '',
        desc: '',
        jsonList: [],
      },
      formLabelWidth: '120px',
      accountRules: {
        title: [{ required: true, message: '文档名称不能为空', trigger: 'blur' }],

        desc: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
      },
      editor: null, //添加富文本内容
      placeholder: '',
      fileList: [],
      // 上传参数
      upload: {
        // 上传的地址
        newurl: '',
        url: process.env.VUE_APP_BASE_API + '/api/help/uploadFile',
        relationid: null,
      },
      dialogTitle: '添加',
      btnType: '',
      info: {
        docTitle: '',
        docContent: '',
        fileArray: [],
      },
      docPreviewVisible: false,
      file: {},
      fileName: '',
      src: null,
      pdfPage: 1,
      pageCount: 0,
      fileType: '',
      wordHtml: '', //word文件内容
      wordUrl: '', //word文件地址
      excelData: [],
      workbook: {},
      excelUrl: '', //文件地址
      imgUrl: '', //图片地址
      pptUrl: '',
    };
  },
  created() {
    this.getData();
  },
  computed: {
    ...mapGetters(['name', 'userid', 'token']),
  },
  methods: {
    //添加-修改
    addOrUpdateDoc(type, row) {
      // console.log(type);
      // console.log(row);
      this.btnType = type;
      if (type == 'addFile') {
        this.dialogTitle = '添加';
        this.addContent.createName = this.name;
      } else if (type == 'editFile') {
        this.dialogTitle = '修改';
        this.addContent.id = row.id;
        this.addContent.createName = row.create_name;
        this.addContent.title = row.title;
        this.addContent.desc = row.content;
        if (row.annex != '') {
          this.addContent.jsonList = JSON.parse(row.annex);
        }
      }
      this.docAddDialogVisible = true;
      this.$nextTick(() => {
        this.createE();
      });
    },
    //编辑文本
    createE() {
      let _this = this;
      this.editor = new E(_this.$refs.editorElem); //获取组件并构造编辑器
      this.editor.config.excludeMenus = [
        //定义不需要显示的菜单
        'video',
      ];
      this.editor.config.placeholder = this.placeholder || '';
      this.editor.config.uploadImgShowBase64 = true; // base 64 存储图片
      this.editor.config.uploadImgServer = '/api/help/uploadFile';
      this.editor.config.uploadFileName = 'file'; // 后端接受上传文件的参数名
      this.editor.config.uploadImgAccept = [];
      // this.editor.config.uploadImgHeaders = {token: this.token, }
      // this.editor.config.showLinkImg = false; // 隐藏网络上传图片
      this.editor.config.focus = false;
      this.editor.config.zIndex = 100;
      this.editor.create();
      console.log(this.editor);
      this.editor.txt.html(this.addContent.desc);
      // 将图片大小限制为 3M
      this.editor.config.uploadImgMaxSize = 3 * 1024 * 1024;
      // 限制一次最多上传 5 张图片
      this.editor.config.uploadImgMaxLength = 5;

      this.editor.config.onchange = (html) => {
        _this.addContent.desc = html;
        // console.log(_this.addContent.desc);
      };

      // 上传图片的结果反馈
      this.editor.config.uploadImgHooks = {
        before: function (xhr, editor, files) {
          // 图片上传之前触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，files 是选择的图片文件
          // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传
          // return {
          // prevent: true,
          // msg: '放弃上传'
          // }
          // console.log("before:",xhr)
        },
        success: function (xhr, editor, result) {
          // 图片上传并返回结果，图片插入成功之后触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
          console.log('success:', result);
        },
        fail: function (xhr, editor, result) {
          // 图片上传并返回结果，但图片插入错误时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
        },
        error: function (xhr, editor) {
          // 图片上传出错时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
        },
        // 如果服务器端返回的不是 {errno:0, data: [...]} 这种格式，可使用该配置
        // （但是，服务器端返回的必须是一个 JSON 格式字符串！！！否则会报错）
        customInsert: function (insertImg, result, editor) {
          // 图片上传并返回结果，自定义插入图片的事件（而不是编辑器自动插入图片！！！）
          // insertImg 是插入图片的函数，参数editor 是编辑器对象，result 是服务器端返回的结果
          // 举例：假如上传图片成功后，服务器端返回的是 {url:'....'} 这种格式，即可这样插入图片：
          let ishttps = 'https:' == document.location.protocol ? true : false;
          let reg = new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
          let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
          //console.log(result);
          let url;
          if (result.code == 1) {
            // if(ishttps){

            //   let ip=url.match(reg)[0]+'/api';
            //   url = result.data.fileUrl.replace(spat,ip)
            // }else{

            // }
            url = result.data.fileUrl;
            insertImg(url);
          }

          // result 必须是一个 JSON 格式字符串！！！否则报错
        },
      };
    },
    //确定添加或修改文档
    addDocFile() {
      let data = {
        id: this.addContent.id,
        title: this.addContent.title,
        content: this.addContent.desc,
        createName: this.addContent.createName,
        jsonList: JSON.stringify(this.addContent.jsonList),
      };
      console.log(data);
      addHelp(data)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            if (this.btnType == 'addFile') {
              this.$message({
                message: '添加成功',
                type: 'success',
              });
            } else if (this.btnType == 'editFile') {
              this.$message({
                message: '修改成功',
                type: 'success',
              });
            }

            this.clearEditor();
            this.getData();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    clearEditor() {
      this.docAddDialogVisible = false;
      this.addContent.id = '';
      this.addContent.title = '';
      this.addContent.desc = '';
      this.addContent.createName = '';
      this.editor.txt.clear();
      this.addContent.jsonList = [];
      this.editor.destroy();
      this.btnType = '';
    },
    handleClose() {
      this.clearEditor();
    },

    //刷新
    handleRefresh() {
      this.getData();
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let data = {
        keyWord: this.keyWord,
      };
      helpList(data)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            setTimeout(() => {
              this.listLoading.close();
            }, 200);
            this.tableData = res.data.rows;
            this.total = res.data.total_rows;
            this.currentPage = res.data.page;
            if (res.data.total_rows == 0) {
              this.totalPage = 1;
            } else {
              this.totalPage = Math.ceil(this.total / this.listQuery.limit);
            }
          }
        })
        .catch((error) => {
          console.log(error);
          this.listLoading.close();
        });
    },
    handleClick(row) {
      // console.log(row);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
    //上传附件
    uploadAttachment() {
      this.uploadDialogVisible = true;
      this.fileList = [];
    },

    //删除
    handleDel(row) {
      console.log(row);
      let data = {
        id: row.id,
        userName: this.name,
      };
      console.log(data);
      debugger;
      this.$confirm('确认要删除文档' + row.title + '吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e == 'confirm') {
            delHelpList(data)
              .then((res) => {
                console.log(res);
                if (res.code == 1) {
                  this.$message({
                    message: '删除成功',
                    type: 'success',
                  });
                  this.getData();
                }
              })
              .catch((error) => {
                console.log(error);
                this.$message.error({
                  message: '删除失败',
                });
              });
          }
        })
        .catch((e) => {});
    },

    openDoc(row) {
      // console.log(row);
      this.info.docTitle = row.title;
      this.info.docContent = row.content;
      if (row.annex != '') {
        this.info.fileArray = JSON.parse(row.annex);
      }
      this.docDialogVisible = true;
    },
    //上传文件
    handleChange(file, fileList) {
      this.fileList = fileList;
      //去除文件名相同的情况（上传列表中多次出现同一个文件）
      for (var i = 0; i < this.fileList.length - 1; i++) {
        for (var j = 1; j < this.fileList.length; j++) {
          if (i != j) {
            if (this.fileList[i].name == this.fileList[j].name) {
              this.fileList.splice(j, 1);
            }
          }
        }
      }
    },
    beforeRemove(file, fileList) {
      // console.log(file);
      this.$confirm(`确定移除 ${file.name}？`);
      this.fileList = fileList;
      // console.log(fileList);
    },
    handleFileBefore(file) {},
    //删除文件
    delFiles(index) {
      this.addContent.jsonList.splice(index, 1);
    },
    submitFile() {
      // console.log(this.fileList);
      this.fileList.forEach((item) => {
        let types = [
          'jpeg',
          'jpg',
          'gif',
          'bmp',
          'png',
          'doc',
          'txt',
          'pdf',
          'docx',
          'xls',
          'xlsx',
          'JPG',
          'JPEG',
          'PNG',
          'BMP',
          'ppt',
          'pptx',
        ];
        //文件路径
        var filePath = item.name;
        //获取最后一个.的位置
        var index = filePath.lastIndexOf('.');
        //获取后缀
        var ext = filePath.substr(index + 1);
        console.log(ext);
        const isImage = types.includes(ext);
        const isLtSize = item.size / 1024 / 1024 < 10;
        if (!isImage) {
          this.$message.error('上传文件不支持该文件格式!');
          return false;
        }
        if (!isLtSize) {
          this.$message.error('上传文件大小不能超过 10MB!');
          return false;
        }
        let form = new FormData();
        let file = item.raw;
        // console.log(file);
        form.append('file', file);
        uploadFiles(form)
          .then((res) => {
            console.log(res);
            if (res.code == 1) {
              this.addContent.jsonList.push(res.data);
              this.uploadDialogVisible = false;
            }
          })
          .catch((error) => {
            console.log(error);
          });
      });
    },
    //下载文件
    //下载文件
    downloadFileBtn(item) {
      console.log(item);
      // fileDown()
      //   .then(res => {
      //     console.log(res);
      //     if (res.code == 1) {
      //     }
      //   })
      //   .catch(error => {
      //     console.log(error);
      //   });
      let url = item.fileUrl;
      let name = item.fileName;
      this.downLoadFile(url, name);
    },
    //下载文件
    downLoadFile(url, name) {
      console.log(url);
      if (name == '附件') {
        var first = url.lastIndexOf('.'); //取到文件名开始到最后一个点的长度
        var namelength = url.length; //取到文件名长度
        var filesuffix = url.substring(first + 1, namelength); //截取获得后缀名
        name = name + '.' + filesuffix;
      }
      // 下载文件
      this.download(url, name); // OSS可下载的文件url，你想要改的名字
    },
    getBlob(url, cb) {
      // 获取文件流
      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';
      xhr.onload = function () {
        if (xhr.status === 200) {
          cb(xhr.response);
        }
      };
      xhr.send();
    },

    saveAs(blob, filename) {
      // 改名字
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename);
      } else {
        var link = document.createElement('a');
        var body = document.querySelector('body');

        link.href = window.URL.createObjectURL(blob);
        link.download = filename;

        // fix Firefox
        link.style.display = 'none';
        body.appendChild(link);

        link.click();
        body.removeChild(link);

        window.URL.revokeObjectURL(link.href);
      }
    },
    download(url, filename) {
      let _this = this;
      // 执行
      this.getBlob(url, function (blob) {
        _this.saveAs(blob, filename);
      });
    },
    //预览文件
    preVirwFile(item) {
      this.file = item;
      this.fileName = item.fileName;
      let fileUrl = item.fileUrl;
      console.log(fileUrl);
      if (
        !(
          fileUrl.includes('.png') ||
          fileUrl.includes('.jpg') ||
          fileUrl.includes('.jpeg') ||
          fileUrl.includes('.bmp') ||
          fileUrl.includes('.JPG') ||
          fileUrl.includes('.PNG') ||
          fileUrl.includes('.JPEG') ||
          fileUrl.includes('.BMP') ||
          fileUrl.includes('.pdf') ||
          fileUrl.includes('.txt') ||
          fileUrl.includes('.xls') ||
          // fileUrl.includes(".doc") ||
          fileUrl.includes('.docx') ||
          fileUrl.includes('.xlsx') ||
          fileUrl.includes('.ppt') ||
          fileUrl.includes('.pptx')
        )
      ) {
        this.$message.error('文件类型不支持预览');
        return false;
      }
      //文件路径
      var filePath = item.filePath;
      //获取最后一个.的位置
      var index = filePath.lastIndexOf('.');
      //获取后缀
      var ext = filePath.substr(index + 1);
      console.log(ext);
      let ishttps = 'https:' == document.location.protocol ? true : false;
      let reg = new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
      let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
      // if(ishttps){
      //     let ip=fileUrl.match(reg)[0]+'/api';
      //     fileUrl=fileUrl.replace(spat,ip)
      // }else{

      // }
      console.log(fileUrl);
      // console.log(this.isAssetTypeAnImage(ext));
      if (ext == 'pdf' || ext == 'txt') {
        this.fileType = 'pdf';
        //pdf预览
        this.src = pdf.createLoadingTask({
          url: fileUrl,
        });
        this.src.promise.then((pdf) => {
          this.docPreviewVisible = true;
        });
      } else if (ext == 'doc' || ext == 'docx') {
        this.fileType = 'word';
        this.wordHtml = '';
        this.readWordFromRemoteFile(fileUrl);
        console.log(fileUrl);
        this.docPreviewVisible = true;
      } else if (ext == 'xls' || ext === 'xlsx') {
        this.fileType = 'excel';
        this.readWorkbookFormRemoteFile(fileUrl);
        this.docPreviewVisible = true;
      } else if (this.isAssetTypeAnImage(ext) == true) {
        this.fileType = 'image';
        this.imgUrl = fileUrl;
        this.docPreviewVisible = true;
      } else if (ext == 'ppt' || ext === 'pptx') {
        this.fileType = 'ppt';
        this.pptUrl = fileUrl;
        this.docPreviewVisible = true;
      } else {
        this.fileType = '';
        this.imgUrl = '';
        this.pptUrl = '';
      }
    },
    //在线查看word文件
    readWordFromRemoteFile(url) {
      let vm = this;
      var xhr = new XMLHttpRequest();
      xhr.open('get', url, true);
      xhr.responseType = 'arraybuffer';
      xhr.onload = function () {
        if (xhr.status == 200) {
          mammoth
            .convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) })
            .then(function (resultObject) {
              vm.$nextTick(() => {
                vm.wordHtml = resultObject.value;
              });
            });
        }
      };
      xhr.send();
    },
    //在线预览excel 文件
    readWorkbookFormRemoteFile(url) {
      var xhr = new XMLHttpRequest();
      xhr.open('get', url, true);
      xhr.responseType = 'arraybuffer';
      let _this = this;
      xhr.onload = function (e) {
        if (xhr.status === 200) {
          var data = new Uint8Array(xhr.response);
          var workbook = XLSX.read(data, { type: 'array' });
          console.log('workbook', workbook);

          var sheetNames = workbook.SheetNames; // 工作表名称集合
          _this.workbook = workbook;
          _this.getTable(sheetNames[0]);
        }
      };
      xhr.send();
    },
    getTable(sheetName) {
      console.log(sheetName);
      var worksheet = this.workbook.Sheets[sheetName];
      this.excelData = XLSX.utils.sheet_to_json(worksheet);
      console.log(this.excelData);
    },
    //图片类型
    isAssetTypeAnImage(ext) {
      return (
        [
          'bmp',
          'jpg',
          'png',
          'tif',
          'gif',
          'pcx',
          'tga',
          'exif',
          'fpx',
          // "svg",
          'psd',
          'cdr',
          'pcd',
          'dxf',
          'ufo',
          'eps',
          'ai',
          'raw,wmf',
          'jpeg',
        ].indexOf(ext.toLowerCase()) !== -1
      );
    },
    closePreview() {
      this.pdfPage = 1;
    },
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pageCount;
      this.pdfPage = p;
    },
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pageCount ? p + 1 : 1;
      this.pdfPage = p;
    },
  },
};
</script>
<style lang="scss" scoped>
.mainWrapper {
  .mainBox {
    height: 100%;

    .text {
      display: inline-block;
      color: #db2e43;
      margin-right: 5px;
      cursor: pointer;
      .svg-icon {
        display: inline-block;
        margin-right: 5px;
        color: #db2e43;
      }
    }
  }
}
.el-scrollbar {
  height: calc(100vh - 140px);
}
.doc-box {
  -moz-user-select: none; /* Firefox私有属性 */
  -webkit-user-select: none; /* WebKit内核私有属性 */
  -ms-user-select: none; /* IE私有属性(IE10及以后) */
  -khtml-user-select: none; /* KHTML内核私有属性 */
  -o-user-select: none; /* Opera私有属性 */
  user-select: none; /* CSS3属性 */
  p {
    text-indent: 2em;
    line-height: 26px;
    margin-bottom: 10px;
  }
  h3 {
    margin-bottom: 10px;
  }
}
.dialog-footer {
  text-align: right;
}
.doc-content {
  width: 100%;
  height: 23px;
}
.file-list-box {
  li {
    color: #005bd4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    max-height: 120px;
    overflow: auto;
    &:hover {
      background: #eee;
      .del {
        display: inline-block;
      }
    }
    .name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .svg-icon {
        display: inline-block;
        margin-right: 5px;
      }
    }
    .del {
      display: none;
      cursor: pointer;
    }
  }
}
.info-box {
  p {
    line-height: 1.5;
  }
  .info-list-box {
    h3 {
      font-weight: 400;
      margin: 16px 0 10px 0;
    }
    li {
      display: flex;
      align-items: center;
      padding: 5px;
      color: #005bd4;
      .text {
        display: inline-block;
        margin-right: 16px;
        cursor: pointer;
      }
      .download {
        cursor: pointer;
      }
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>
