<template>
  <div :id="id" :style="style"></div>
</template>

<script>
export default {
  name: 'PolycyclicGraph',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
      options: {}
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },

  mounted() {
    console.log(this.chartData, 'chartData');
    this.init();
    // setTimeout(() => {
    //   this.init();
    //   console.log(111111111111111);
    // }, 1000)

  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      // this.setOption();
      window.addEventListener('resize', this.chart.resize);
      let options = {

        tooltip: {
          formatter: function (params) {
            // console.log(params);
            var result = '';
            result += params.marker + params.name + '使用率：' + Number(params.value).toFixed(2) + '%';
            return result;
          },
        },
        polar: {
          radius: ['36%', '72%'],
          center: ['50%', '50%'],
        },
        angleAxis: {
          max: 100,
          startAngle: 90,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        radiusAxis: {
          type: 'category',
          data: props.chartData.xData,
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: {
          type: 'bar',
          showBackground: true, // 展示背景阴影
          color: ['#32D16D', '#FF8723', '#2251F8',],
          colorBy: 'data',
          barWidth: 10,
          barCategoryGap: "20%", // 柱形间距
          data: props.chartData.yData,
          roundCap: true, //是否在环形柱条两侧使用圆弧效果
          coordinateSystem: 'polar',
        },
      }
      this.chart.setOption(options, true);
    },

    // setOption() {
    //   const that = this;

    //   let option = {};
    //   option = {
    //     tooltip: {
    //       formatter: function (params) {
    //         // console.log(params);
    //         var result = '';
    //         result += params.marker + params.name + '利用率：' + params.value + '%';
    //         return result;
    //       },
    //     },
    //     polar: {
    //       radius: ['36%', '72%'],
    //       center: ['50%', '50%'],
    //     },
    //     angleAxis: {
    //       max: 100,
    //       startAngle: 90,
    //       axisLine: {
    //         show: false,
    //       },
    //       axisTick: {
    //         show: false,
    //       },
    //       axisLabel: {
    //         show: false,
    //       },
    //       splitLine: {
    //         show: false,
    //       },
    //     },
    //     radiusAxis: {
    //       type: 'category',
    //       // data: that.chartData.xData,
    //       data: ['11', '22', '44'],
    //       axisLabel: {
    //         show: false,
    //       },
    //       axisTick: {
    //         show: false,
    //       },
    //       splitLine: {
    //         show: false,
    //       },
    //       axisLine: {
    //         show: false,
    //       },
    //     },
    //     series: {
    //       type: 'bar',
    //       showBackground: true, // 展示背景阴影
    //       color: ['#108ff4', '#6e3cfc', '#ffc700'],
    //       colorBy: 'data',
    //       barCategoryGap: 6, // 柱形间距
    //       data: [
    //         {
    //           value: 3,
    //           // value: that.chartData.yData[0],
    //           itemStyle: {
    //             color: '#108ff4',
    //           },
    //         },
    //         {
    //           // value: that.chartData.yData[1],
    //           value: 2,
    //           itemStyle: {
    //             color: '#6e3cfc',
    //           },
    //         },
    //         {
    //           value: 6,
    //           // value: that.chartData.yData[2],
    //           itemStyle: {
    //             color: '#ffc700',
    //           },
    //         },
    //       ],
    //       roundCap: true, //是否在环形柱条两侧使用圆弧效果
    //       coordinateSystem: 'polar',
    //     },
    //   };

    //   this.chart.setOption(option, true);
    // },
  },
};
</script>
