<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">
            <span
              class="el-icon-arrow-left back-icon"
              @click="$router.back(-1)"
            ></span
            >添加账号
          </h3>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <el-form
                  :model="accountForm"
                  ref="accountForm"
                  :rules="accountRules"
                >
                  <el-form-item
                    label="账号"
                    :label-width="formLabelWidth"
                    prop="loginName"
                  >
                    <el-input
                      v-model="accountForm.loginName"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="密码"
                    :label-width="formLabelWidth"
                    prop="loginPwd"
                  >
                    <el-input
                      type="password"
                      v-model="accountForm.loginPwd"
                      autocomplete="off"
                      :class="forminputWidth"
                      @input="checkPwd"
                      @blur="blurP"
                    ></el-input>
                  </el-form-item>
                  <ul class="password-strength" v-show="passeordFocus">
                    <li>
                      <span id="one"></span>
                      <p>弱</p>
                    </li>
                    <li>
                      <span id="two"></span>
                      <p>中</p>
                    </li>
                    <li>
                      <span id="three"></span>
                      <p>强</p>
                    </li>
                  </ul>
                  <el-form-item
                    label="联系人"
                    :label-width="formLabelWidth"
                    prop="userName"
                  >
                    <el-input
                      v-model="accountForm.userName"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>

                  <el-form-item
                    label="联系电话"
                    :label-width="formLabelWidth"
                    prop="mobile"
                  >
                    <el-input
                      v-model="accountForm.mobile"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="角色"
                    :label-width="formLabelWidth"
                    prop="roles"
                  >
                    <el-select
                      v-model="accountForm.roles"
                      placeholder="请选择"
                      multiple
                      :class="forminputWidth"
                    >
                      <el-option
                        v-for="item in roleList"
                        :key="item.id"
                        :label="item.remark"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div class="form-foot">
      <el-button type="primary" @click="handleAddClick('accountForm')"
        >确 定</el-button
      >
    </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { tenant, roleList, addAccount, resetPwd } from "@/api/account.js";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    let validatePassword = (rule, value, callback) => {
      var reg1 = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*.])[\da-zA-Z~!@#$%^&*.]{8,}$/; //密码必须是8位以上、必须含有字母、数字、特殊符号
      var reg2 = /(123|234|345|456|567|678|789|012)/; //不能有3个连续数字
      if (!reg1.test(value)) {
        callback(new Error("密码必须是8位以上、必须含有字母、数字、特殊符号"));
      } else if (reg2.test(value)) {
        callback(new Error("不能有3个连续数字"));
      } else {
        callback();
      }
    }

    return {
       passeordFocus:false,
       msgText: "",
      account: "",
      listLoading: false,
      downloadLoading: false,
      userId: "",
      userType: "",
      tenantId: "", //账号id
      formLabelWidth: "120px",
      forminputWidth: "forminputWidth",
      accountForm: {
        loginName: "",
        tenantId: "",
        loginPwd: "",
        userName: "",
        accountType: 2,
        mobile: "",
        roles: [],
        userId: "",
      }, //添加账号表单

      roleList: [], //租户角色
      accountRules: {
        loginName: [
          { required: true, message: "账号不能为空", trigger: "blur" },
        ],
        tenantId: [
          { required: true, message: "所属租户不能为空", trigger: "blur" },
        ],
        loginPwd: [
          { required: true, message: "密码不能为空", trigger: "blur" },
           {
          required: true,
          trigger: "blur",
          validator: validatePassword
        }
        ],
        userName: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        accountType: [
          { required: true, message: "账号类型不能为空", trigger: "blur" },
        ],
        mobile: [{ message: "请输入手机号", trigger: "blur" }],
        roles: [],
      },
    };
  },
  created() {
    this.getData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
      blurP(){
      //  this.passeordFocus=false;
    },
     checkPwd(value) {
        this.passeordFocus=true;
      this.msgText = this.checkStrong(value);
      // console.log( this.msgText);
      if (this.msgText > 1 || this.msgText == 1) {
        document.getElementById("one").style.background = "red";
      } else {
        document.getElementById("one").style.background = "#eee";
      }
      if (this.msgText > 2 || this.msgText == 2) {
        document.getElementById("two").style.background = "orange";
      } else {
        document.getElementById("two").style.background = "#eee";
      }
      if (this.msgText == 4) {
        document.getElementById("three").style.background = "#00D1B2";
      } else {
        document.getElementById("three").style.background = "#eee";
      }
    },

    //密码强度
    checkStrong(sValue) {
      var modes = 0;
      //正则表达式验证符合要求的
      if (sValue.length < 1) return modes;
      if (/\d/.test(sValue)) modes++; //数字
      if (/[a-z]/.test(sValue)) modes++; //小写
      if (/[A-Z]/.test(sValue)) modes++; //大写
      if (/\W/.test(sValue)) modes++; //特殊字符

      //逻辑处理
      switch (modes) {
        case 1:
          return 1;
          break;
        case 2:
          return 2;
          break;
        case 3:
        case 4:
          return sValue.length < 4 ? 3 : 4;
          break;
      }
      return modes;
    },
    getData() {
      // userType=1  tenantId不传为空 平台
      // ueserType=2  tenantId传
      this.userId = this.userid;
      this.userType = this.usertype;

      //租户角色
      // console.log(this.tenantid);
      let roleData = {
        userId: this.userId,
        tenantId: this.tenantid,
      };
      roleList(roleData)
        .then((res) => {
          // console.log(res);
          this.roleList = res.obj;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //账号添加
    handleAddClick(accountForm) {
      this.$refs[accountForm].validate((valid) => {
        if (valid) {
          let addAccountArry = {
            loginName: this.accountForm.loginName,
            tenantId: this.tenantid,
            loginPwd: this.accountForm.loginPwd,
            userName: this.accountForm.userName,
            accountType: this.accountForm.accountType,
            mobile: this.accountForm.mobile,
            roles: this.accountForm.roles.join(","),
            userId: this.userId,
          };
          // console.log(addAccountArry);
          addAccount(addAccountArry)
            .then((res) => {
               console.log(res);
              if (res.ok == true) {
                 this.$message({
                  message: "添加成功",
                  type: "success",
                });
                 this.$router.push({ path: `/nested/account` });
              } else {
                this.$message({
                  showClose: true,
                  message: res.errorMsg,
                  type: "warning",
                });
              }
            })
            .catch((error) => {
              console.log(error);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
    background: #f1f6fa;
  }
}
.password-strength {
  width: 300px;
  margin-left: 120px;
}
</style>

