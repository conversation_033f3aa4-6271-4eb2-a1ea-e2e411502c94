<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">许可管理</h3>
        </div>
        <div class="license-box">
          <div class="license-cell">
            <span class="label">授权对象：</span>
            <span class="text">{{ licenseData.licenseTo }}</span>
          </div>
          <div class="license-cell">
            <span class="label">授权序列号：</span>
            <span class="text">{{ licenseData.mac }}</span>
          </div>
          <div class="license-cell">
            <span class="label">授权类型：</span>
            <span class="text">{{
              licenseData.code == 1 ? '测试版' : '商用版'
            }}</span>
          </div>
          <div class="license-cell">
            <span class="label">申请时间：</span>
            <span class="text">{{
              licenseData.applicationTime | formatDate
            }}</span>
          </div>
          <div class="license-cell">
            <span class="label">到期时间：</span>
            <span class="text">{{ licenseData.expireDate | formatDate }}</span>
          </div>
          <el-button type="primary" @click="updatAuth">更新</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      top="0"
      width="640px"
      :visible.sync="dialogAuthVisible"
      :append-to-body="true"
    >
      <div slot="title">
        <span class="el-dialog__title">
          <img src="@/assets/auth-vcode.png" alt="" class="auth-img" />授权更新
        </span>
      </div>
      <el-form
        :model="authForm"
        :rules="accountRules"
        :label-position="labelPosition"
        style="padding: 0 30px"
      >
        <el-form-item label="申请码" :label-width="formLabelWidth">
          <!-- <el-input
                v-model="authForm.license"
                autocomplete="off"
              ></el-input> -->
          <div style="padding-right: 30px; display: block; position: relative">
            {{ authForm.license }}
            <img
              src="@/assets/copy.png"
              alt=""
              class="copy-img"
              :data-clipboard-text="authForm.license"
              title="复制"
              @click="copyLicence"
            />
          </div>
        </el-form-item>
        <el-form-item label="授权码" :label-width="formLabelWidth" prop="code">
          <el-input
            v-model="authForm.code"
            autocomplete="off"
            style="padding -right: 30px;width:100%;"
          ></el-input>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth">
          <!-- <el-input
                v-model="authForm.license"
                autocomplete="off"
              ></el-input> -->
          <div style="display: block; position: relative; color: #8c97a8">
            请联系管理员提供授权码进行更新 !
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confimUpDate">确定</el-button>
        <el-button type="default" @click="dialogAuthVisible = false"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { formatDate } from '@/utils/formatDate';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { getLicense } from '@/api/system.js';
import { Loading } from 'element-ui';
import Clipboard from 'clipboard';
import { isAuth, license } from '@/api/user.js';
export default {
  inject: ['reload'],
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      licenseData: {}, //使用许可
      dialogAuthVisible: false,
      formLabelWidth: '90px',
      labelPosition: 'left',
      authForm: {
        license: '',
        code: '',
      },
      accountRules: {
        code: [{ required: true, message: '授权码不能为空', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.getData();
  },
  filters: {
    formatDate(time) {
      var date = new Date(time);
      return formatDate(date, 'yyyy-MM-dd ');
    },
  },
  methods: {
    //更新授权
    updatAuth() {
      this.authForm.code = '';
      this.auth();
      this.dialogAuthVisible = true;
    },
    //确认更新授权
    confimUpDate() {
      this.handleAuth();
    },
    //授权
    handleAuth() {
      license(this.authForm.code)
        .then((res) => {
          // console.log(res);
          if (res.success == true) {
            this.dialogAuthVisible = false;
            this.$message.success('授权成功');
            // this.getData();
            this.reload();
          } else {
            this.$message.error('授权码错误');
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    auth() {
      let data = {
        type: 2,
      };
      isAuth(data)
        .then((res) => {
          // console.log(res);
          if (res.success == false) {
            this.authForm.license = res.license;
          } else if (res.success == true) {
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //复制验证码
    copyLicence() {
      var clipboard = new Clipboard('.copy-img');
      clipboard.on('success', (e) => {
        // console.log("复制成功");
        this.$message.success('复制成功');
        //  释放内存
        clipboard.destroy();
      });
      clipboard.on('error', (e) => {
        // 不支持复制
        // console.log("该浏览器不支持复制");
        this.$message.error('该浏览器不支持复制');
        // 释放内存
        clipboard.destroy();
      });
    },
    //菜单列表
    getData() {
      getLicense()
        .then((res) => {
          // console.log(res.data);
          this.licenseData = res.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //时间戳转换
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .license-box {
      padding: 20px 30px;
      .license-cell {
        margin-bottom: 20px;
        .label {
          width: 100px;
          display: inline-block;
          margin-right: 5px;
          color: #606977;
        }
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>
