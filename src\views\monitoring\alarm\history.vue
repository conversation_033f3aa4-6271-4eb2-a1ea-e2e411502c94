<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">设备历史告警信息</h3>
      </div>
      <div class="serch-box clearfix">
        <div class="filter-container">
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            @click="handleRefresh()"
          >
            <svg-icon icon-class="refresh" />
          </el-button>
        </div>
        <div class="page-box">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="sizes, prev,slot, next,total"
            :total="total"
          >
            <span class="pageNum">
              {{ this.listQuery.page }}
              <i class="divider">/</i>
              {{ totalPage }}
            </span>
          </el-pagination>
        </div>
      </div>
      <div class="table-box">
        <el-table-bar>
          <el-table
            :data="tableData"
            style="width: 100%; margin-bottom: 20px"
            ref="table"
            row-key="id"
            highlight-current-row
            v-loading="listLoading"
          >
            <el-table-column
              prop="hostName"
              label="名称"
              sortable
              show-overflow-tooltip
            ></el-table-column>

            <!-- <el-table-column label="CPU利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.cpu_rate != null ||
                    scope.row.cpu_rate != undefined
                  "
                  >{{ scope.row.cpu_rate }}%</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.cpuWaringType == 1"
                />
              </template>
            </el-table-column>

            <el-table-column label="内存利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.memory_usage != null ||
                    scope.row.memory_usage != undefined
                  "
                  >{{ scope.row.memory_usage }}%</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.memoryWaringType == 1"
                />
              </template>
            </el-table-column>
            <el-table-column label="磁盘利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.disk_usage != null ||
                    scope.row.disk_usage != undefined
                  "
                  >{{ scope.row.disk_usage }}%</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.diskWaringType == 1"
                />
              </template>
            </el-table-column> -->

            <el-table-column
              prop="warnThreshold"
              label="告警阈值"
              width="110"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="warningDetails"
              label="告警信息"
              min-width="150"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="告警状态" sortable prop="warnType">
              <template slot-scope="scope">
                <span v-if="scope.row.warnType == 1">健康</span>
                <span v-else-if="scope.row.warnType == 2">警告</span>
                <span v-else-if="scope.row.warnType == 3">严重</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="告警来源" sortable prop="warningKey">
              <template slot-scope="scope">
                <span v-if="scope.row.warningKey == 'memory'">内存</span>
                <span v-else-if="scope.row.warningKey == 'disk'">磁盘</span>
                <span v-else>CPU</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间"
              sortable
              show-overflow-tooltip
            ></el-table-column>
          </el-table>
        </el-table-bar>
      </div>
    </div>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination';

import { warningLogList } from '@/api/modules/monitor';
export default {
  components: {
    Pagination,
  },
  directives: { waves },
  data() {
    return {
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        hostId: '',
      },
      currentPage: 1,
      totalPage: 1,
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = true;
      let data = {
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        hostId: this.$route.query.hostId,
      };
      console.log(data);
      warningLogList(data)
        .then((res) => {
          console.log(res);

          if (res.code == 1) {
            this.listLoading = false;
            this.tableData = res.data.list;
            this.total = res.data.total;
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },

    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 200px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
    .color-red {
      color: #f95b6c;
    }
    .color-green {
      color: #00a700;
    }
    .alarm-tips-img {
      display: inline-block;
      margin-left: 3px;
      width: 18px;
      vertical-align: middle;
    }
  }
}
</style>
