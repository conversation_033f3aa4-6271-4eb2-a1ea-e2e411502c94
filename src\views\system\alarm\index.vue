<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">告警设置</h3>
      </div>
      <div class="serch-box clearfix">
        <div class="filter-container">
          <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
            <svg-icon icon-class="refresh" />
          </el-button>

          <div class="search-container">
            <el-input
              v-model="listQuery.title"
              placeholder="名称"
              style="width: 200px"
              class="filter-item"
              v-on:input="search"
            />
            <span class="el-icon-search search-btn" @click="handleSearch()"></span>
          </div>
        </div>
        <div class="page-box">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="sizes, prev,slot, next,total"
            :total="total"
          >
            <span class="pageNum">
              {{ this.listQuery.page }}
              <i class="divider">/</i>
              {{ totalPage }}
            </span>
          </el-pagination>
        </div>
      </div>
      <div class="table-box">
        <el-table-bar>
          <el-table
            :data="tableData"
            style="width: 100%; margin-bottom: 20px"
            ref="table"
            row-key="id"
            highlight-current-row
            v-loading="listLoading"
          >
            <el-table-column
              prop="NAME"
              label="名称"
              sortable
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="警告阈值" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  >{{ scope.row.waring_threshold }}%~{{
                    scope.row.severe_threshold
                  }}%</span
                >
              </template>
            </el-table-column>
            <el-table-column label="严重阈值" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  >{{ scope.row.severe_threshold }}%
                  <span class="color-red">+</span></span
                >
              </template>
            </el-table-column>

            <!-- <el-table-column label="告警状态" sortable>
              <template slot-scope="scope">
                <span v-if="scope.row.waring_type == 1">健康</span>
                <span v-else-if="scope.row.waring_type == 2">警告</span>
                <span v-else>严重</span>
              </template>
            </el-table-column> -->

            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="scope">
                <div class="opt" @click="addOrUpdateHandle(scope.$index, scope.row)">
                  修改
                </div>
                <!-- <el-dropdown>
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                   
                    <div class="opt">修改</div>
                  </el-dropdown-menu>
                </el-dropdown> -->
              </template>
            </el-table-column>
          </el-table>
        </el-table-bar>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getData"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination';
import AddOrUpdate from './alarm-add-update.vue';
import { waringList } from '@/api/modules/monitor';
export default {
  components: {
    Pagination,
    AddOrUpdate,
  },
  directives: { waves },
  data() {
    return {
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
      },
      currentPage: 1,
      totalPage: 1,
      dialogFormVisible: false,
      formLabelWidth: '120px',
      addOrUpdateVisible: false,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = true;
      let data = {
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        keyWord: this.listQuery.title,
      };
      // console.log(data);
      waringList(data)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading = false;
            this.tableData = res.data.rows;
            this.total = res.data.total_rows;
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    // 新增 / 修改
    addOrUpdateHandle(index, row) {
      // console.log(row);
      this.addOrUpdateVisible = true;
      let dataForm = [];
      this.$nextTick(() => {
        dataForm = this.$set(dataForm, index, row);
        // console.log(dataForm);
        this.$refs.addOrUpdate.dataForm = JSON.parse(JSON.stringify(dataForm));
        this.$refs.addOrUpdate.init();
      });
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 200px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
    .color-red {
      color: #f95b6c;
    }
  }
}
</style>
