<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">订阅配置</h3>
      </div>

      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="form-box">
          <el-form
            :model="dataForm"
            ref="dataForm"
            @keyup.enter.native="dataFormSubmitHandle()"
          >
            <div
              class="item"
              v-for="(item, index) in dataForm.data"
              :key="item.id"
            >
              <div class="title">{{ item.name }}</div>
              <el-form-item
                :label="
                  item.msgName == 'email'
                    ? '邮箱账号'
                    : item.msgName == 'weChat'
                    ? '企业ID'
                    : '应用唯一标识'
                "
                :label-width="formLabelWidth"
                :prop="`data[${index}].key`"
              >
                <el-input v-model="item.key" autocomplete="off"></el-input>
              </el-form-item>
              <el-form-item
                :label="
                  item.msgName == 'email'
                    ? '授权码'
                    : item.msgName == 'weChat'
                    ? '应用凭证密钥'
                    : '应用密钥'
                "
                :label-width="formLabelWidth"
                :prop="`data[${index}].secret`"
              >
                <el-input v-model="item.secret" autocomplete="off"></el-input>
              </el-form-item>
              <el-form-item
                :label="
                  item.msgName == 'email'
                    ? '邮箱服务器'
                    : item.msgName == 'weChat'
                    ? '企业应用id'
                    : '微应用AgentID'
                "
                :label-width="formLabelWidth"
                :prop="`data[${index}].agentid`"
              >
                <el-input v-model="item.agentid" autocomplete="off"></el-input>
              </el-form-item>
              <el-form-item
                label="服务器端口"
                :label-width="formLabelWidth"
                :prop="`data[${index}].port`"
                v-if="item.msgName == 'email'"
              >
                <el-input v-model="item.port" autocomplete="off"></el-input>
              </el-form-item>
            </div>

            <el-form-item :label-width="formLabelWidth">
              <el-button
                type="primary"
                class="submit-btn"
                @click="dataFormSubmitHandle()"
                >确定配置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { msgDisposeList, updateMsgDispose } from '@/api/modules/subscription';
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      formLabelWidth: '120px',
      dataForm: {},
      // dataFormRules: {
      //   key: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
      // },
    };
  },

  mounted() {
    this.getData();
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'name']),
  },

  methods: {
    getData() {
      msgDisposeList()
        .then((res) => {
          console.log(res);

          if (res.code !== 1) {
            return this.$message.error(res.msg);
          }
          this.dataForm = res;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    validateEmail(rule, value, callback) {
      if (!value) {
        return callback(new Error('邮箱不能为空！'));
      } else {
        const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
        if (reg.test(value)) {
          callback();
        } else {
          return callback(new Error('邮箱格式不正确！'));
        }
      }
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false;
          }
          // this.dataForm.snmpPort = Number(this.dataForm.snmpPort);
          console.log(this.dataForm);
          let data = {
            userId: this.userid,
            userName: this.name,
            disposeStr: JSON.stringify(this.dataForm.data),
          };
          console.log(data);
          updateMsgDispose(data)
            .then((res) => {
              if (res.code !== 1) {
                return this.$message.error(res.msg);
              }
              this.$message({
                message: '订阅配置成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getData();
                },
              });
            })
            .catch(() => { });
        });
      },
      1000,
      { leading: true, trailing: false }
    ),
  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .form-box {
      margin-top: 16px;
      .title {
        width: 100px;
        text-align: right;
        margin-bottom: 20px;
        color: #108ff4;
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.el-textarea::v-deep textarea {
  width: 320px !important;
}
</style>
