<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box">
       
        <div class="auth-item">
          <h2 class="title">云堡垒机(云安保)</h2>
          <el-table
            :data="fortressData"
            style="width: 100%;margin-bottom:20px;"
            row-key="id"
            border
          >
            <el-table-column prop="cust" label="客户信息" sortable width="120"></el-table-column>
            <el-table-column prop="type" label="类型" sortable width="120"></el-table-column>
            <el-table-column prop="status" label="状态" width="120"></el-table-column>
            <el-table-column prop="productID" label="产品ID"></el-table-column>
            <el-table-column prop="module" label="授权模块" width="90"></el-table-column>
            <el-table-column prop="resourcesNum" label="授权资源数" width="90"></el-table-column>
            <el-table-column prop="connectionsNum" label="授权资源并发连接数"></el-table-column>
            <el-table-column prop="time" label="过期时间"></el-table-column>
          </el-table>
        </div>
       
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
     
      fortressData: [
        {
          cust: "-",
          type: "正式版",
          status: "未激活",
          productID: "736010b0e728428db294b6ac7b1ec188",
          module: "基础模块",
          resourcesNum: "50",
          connectionsNum: "20",
          time: "2020-07-17 03:11:10"
        }
      ],
     
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
    
    }
  }
}
</style>
