<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">版本更新</h3>
      </div>
      <div class="license-box">
        <div class="license-cell">
          <span class="label">产品型号：</span>
          <span class="text">{{ versionInfo.fileName }}</span>
        </div>
        <div class="license-cell">
          <span class="label">版本：</span>
          <span class="text">{{ versionInfo.version }}</span>
        </div>
        <!-- <div class="license-cell">
          <span class="label">状态：</span>
          <span class="text color-blue"
            >V2.0版本可下载最新版本（发行说明）<i class="el-icon-warning"></i
          ></span>
        </div> -->

        <!-- <el-button type="primary" class="btn">下载</el-button> -->
        <el-button type="primary" class="btn" @click="uploadHandle"
          >手动更新</el-button
        >
      </div>
    </div>
    <!-- 上传附件 -->
    <el-dialog
      title="上传附件"
      :visible.sync="uploadDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <div class="file-box" style="min-height: 320px">
        <el-upload
          class="upload-demo"
          ref="upload"
          drag
          :file-list="fileList"
          :show-file-list="false"
          :action="uploadUrl"
          :before-upload="handleFileBefore"
          :limit="1"
          accept="application/x-zip-compressed"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-change="handleChange"
          :on-exceed="handleExceed"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只能上传zip文件</div>
        </el-upload>
        <div class="uploadFileList">
          <div class="upload_list_ul">
            <div
              class="upload_list_ul_li"
              v-for="(item, index) in fileList"
              :key="index"
              @mouseover="mouseoverHandle(index)"
              @mouseout="mouseoutHandle"
            >
              <div class="content_wrap">
                <div class="fileName">
                  <div class="fileNameTop">
                    <span class="file_name">
                      <i class="el-icon-document"></i> {{ item.name }}
                    </span>
                    <i
                      class="el-icon-circle-check"
                      v-if="ins !== index && item.progress === 100"
                    ></i>
                    <i
                      class="el-icon-circle-close"
                      v-if="ins == index"
                      @click="delFile(index, item)"
                    ></i>
                  </div>
                  <el-progress
                    :percentage="item.progress"
                    :stroke-width="18"
                    :text-inside="true"
                  ></el-progress>
                </div>
                <!-- <i class="el-icon-circle-close" @click="deleteItem(index)" v-show="ins==index">
                </i> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFile" v-dbClick
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getVersionInfo, newVersion } from '@/api/modules/version';
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      userId: '',
      userName: '',
      versionInfo: {},
      uploadDialogVisible: false,
      fileList: [],
      fileProgress: '',
      // 上传的地址
      uploadUrl: '/api/version/uploadFile',
      ins: -1,
      fileData: [],
    };
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'name']),
  },
  mounted() {
    this.userId = this.userid;
    this.userName = this.name;
    console.log(process.env.VUE_APP_BASE_API);
    this.getInfo();
  },
  methods: {
    getInfo() {
      getVersionInfo()
        .then((res) => {
          console.log(res);

          if (res.code == 1) {
            this.versionInfo = res.data;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    uploadHandle() {
      this.uploadDialogVisible = true;
      this.fileList = [];
    },
    handleProgress(event, file, fileList) {
      if (this.fileList.length > 0) {
        this.fileList.forEach((element, index) => {
          if (element.uid === file.uid) {
            // 更新这个uid下的进度
            const progress = Math.floor(event.percent);
            // 防止上传完接口还没有返回成功值，所以此处给定progress的最大值为99，成功的钩子中再置为100
            element.progress = progress === 100 ? 99 : progress;
            this.$set(this.fileList, index, element);
          }
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },

    //文件上传之前
    handleFileBefore(file) {
      // const isJPG = file.type === 'image/jpeg';
      // const isLt2M = file.size / 1024 / 1024 < 2;
      console.log(file);
      let fileName = file.name;
      let pos = fileName.lastIndexOf('.');
      let lastName = fileName.substring(pos, fileName.length);
      if (lastName.toLowerCase() !== '.zip') {
        this.$message.error('文件必须为.zip');
        // this.resetCompressData()
        return;
      }
      // 限制上传文件的大小
      // const isLt = file.size / 1024 / 5 >= 1 && file.size / 1024 / 1024 / 100 <= 1;
      // if (!isLt) {
      //   this.$message.error('上传文件大小不得小于5KB,不得大于100MB!');
      // }
      // return isLt;
    },
    handleSuccess(response, file, fileList) {
      console.log(this.fileList);
      this.fileList.forEach((element, index) => {
        if (element.uid === file.uid) {
          element.progress = 100;
          this.$message.success('文件上传成功');
          this.$set(this.fileList, index, element);
        }
      });
      this.fileData = response.data;
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    handleError(err, file, fileList) {
      this.fileList.forEach((element, index) => {
        if (element.uid === file.uid) {
          this.fileList.splice(index, 1); // 上传失败删除该记录
          this.$message.error('文件上传失败');
        }
      });
    },
    mouseoutHandle() {
      this.ins = -1;
    },

    mouseoverHandle(index) {
      this.ins = index;
    },
    delFile(index, item) {
      let that = this;
      console.log(item);
      this.$confirm(`确认要删除${item.name}吗？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            that.fileList.splice(index, 1);
            this.$message.success('删除成功');
          }
        })
        .catch((e) => { });
    },

    submitFile() {
      let data = {
        version: this.fileData.fileVersion, //{版本号}
        fileUrl: this.fileData.fileUrl, //(文件下载地址)
        fileType: '', //(文件状态)
        fileName: this.fileData.fileName, //(文件名称)
        userName: this.userName,
        userId: this.userId,
      };
      console.log(data);
      newVersion(data)
        .then((res) => {
          console.log(res);

          if (res.code == 1) {
            this.$message.success('上传完成');
            this.uploadDialogVisible = false;
            this.getInfo();
          } else {
            this.$message.error('上传失败');
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.mainWrapper {
  height: calc(100vh - 48px);

  .mainBox {
    .license-box {
      padding: 20px 30px;
      .license-cell {
        margin-bottom: 20px;
        .label {
          width: 100px;
          display: inline-block;
          margin-right: 5px;
          color: #606977;
        }
        .color-blue {
          color: #2a82e4;
        }
      }
      .btn {
        margin-top: 20px;
        & + .btn {
          margin-left: 20px;
        }
      }
    }
  }
}
.upload-demo {
  display: flex;
  align-items: flex-end;
  .el-upload__tip {
    margin-left: 6px;
  }
}
.uploadFileList {
  margin-top: 30px;
}
.content_wrap {
  display: flex;
  align-items: center;
  .el-icon-document {
    display: inline-block;
    margin-right: 3px;
    color: #2a82e4;
  }
  .el-icon-circle-close {
    color: #f00;
    margin-left: 10px;
    cursor: pointer;
  }
  .el-icon-circle-check {
    color: #2a82e4;
    margin-left: 10px;
  }
  .fileName {
    width: 100%;
    display: flex;
    flex-direction: column;
    .fileNameTop {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .el-progress {
      width: 70%;
    }
  }
}
</style>
