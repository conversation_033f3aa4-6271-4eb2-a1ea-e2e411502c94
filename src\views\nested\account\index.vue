<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">账号管理</h3>
        </div>
        <div class="serch-box clearfix">
          <div class="filter-container">
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              @click="handleRefresh()"
            >
              <svg-icon icon-class="refresh" />
            </el-button>
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddBtn()"
              >添加</el-button
            >
            <div class="search-container">
              <el-input
                v-model="listQuery.title"
                placeholder="账号名/租户"
                style="width: 200px"
                class="filter-item"
                v-on:input="search"
              />
              <span
                class="el-icon-search search-btn"
                @click="handleSearch()"
              ></span>
            </div>
          </div>
          <div class="page-box">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="10"
              layout="sizes, prev,slot, next,total"
              :total="total"
            >
              <span class="pageNum">
                {{ this.listQuery.page }}
                <i class="divider">/</i>
                {{ totalPage }}
              </span>
            </el-pagination>
          </div>
        </div>
        <div class="table-box">
          <el-table-bar>
            <el-table
              :data="tableData"
              style="width: 100%; margin-bottom: 20px"
            >
              <el-table-column
                prop="loginName"
                label="账号"
                width="120"
                sortable
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="tenantLoginName"
                label="所属租户"
                sortable
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="roleNames"
                label="角色"
                sortable
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column label="账号状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.userState == 1">激活</span>
                  <span v-if="scope.row.userState == 2">未激活</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" sortable show-overflow-tooltip>
                <template slot-scope="scope">{{
                  scope.row.createTime | dateformat('YYYY-MM-DD')
                }}</template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="80">
                <template slot-scope="scope">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <div @click="handleUpdate(scope.row)" class="opt">
                        修改
                      </div>
                      <div @click="handleDel(scope.row)" class="opt">删除</div>
                      <div @click="handleResetPwd(scope.row)" class="opt">
                        重置密码
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </el-table-bar>
        </div>
      </div>
    </div>

    <el-dialog
      title="修改账号"
      :visible.sync="dialogFormVisible"
      top="0"
      :close-on-click-modal="false"
    >
      <el-form :model="dataForm" ref="dataForm" :rules="accountRules">
        <el-form-item
          label="联系人"
          :label-width="formLabelWidth"
          prop="userName"
        >
          <el-input v-model="dataForm.userName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="联系电话"
          :label-width="formLabelWidth"
          prop="mobile"
        >
          <el-input v-model="dataForm.mobile" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="角色" :label-width="formLabelWidth" prop="roles">
          <el-select v-model="dataForm.roleIds" placeholder="请选择" multiple>
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.remark"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="role-permission-box">
        <h2 class="title">角色权限</h2>
        <div v-for="(role, index) in roleList" :key="index">
          <h3 class="role-name">{{ role.remark }}</h3>
          <ul class="clearfix">
            <li
              v-for="(per, index) in role.menuList"
              :key="index"
              class="role-item"
            >
              <span class="role-label">
                <i class="el-icon-arrow-right" />
              </span>
              <span>{{ per }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveClick('dataForm')" v-dbClick
          >确定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="重置密码"
      :visible.sync="dialogResetVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="resetForm" ref="resetForm" :rules="accountRules">
        <!-- <el-form-item label="账号" :label-width="formLabelWidth" prop="loginName">
          <el-input v-model="resetForm.loginName" autocomplete="off"></el-input>
        </el-form-item>-->
        <el-form-item
          label="密码"
          :label-width="formLabelWidth"
          prop="loginPwd"
        >
          <el-input
            type="password"
            v-model="resetForm.loginPwd"
            autocomplete="off"
            @input="checkPwd"
            @blur="blurP"
          ></el-input>

          <ul class="password-strength" v-show="passeordFocus">
            <li>
              <span id="one"></span>
              <p>弱</p>
            </li>
            <li>
              <span id="two"></span>
              <p>中</p>
            </li>
            <li>
              <span id="three"></span>
              <p>强</p>
            </li>
          </ul>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogResetVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleResetClick('resetForm')"
          v-dbClick
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  tenant,
  roleList,
  addAccount,
  resetPwd,
  editAccount,
  delTenant,
} from '@/api/account.js';
import { mapGetters } from 'vuex';
import { Loading } from 'element-ui';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      passeordFocus: false,
      msgText: '',
      account: '',
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
      },
      currentPage: 1,
      totalPage: 1,
      downloadLoading: false,
      userId: '',
      userType: '', //用户类型
      tenantId: '', //账号id
      dialogFormVisible: false,
      dialogResetVisible: false,
      dataForm: {
        id: '',
        tenantId: '',
        userName: '',
        mobile: '',
        roleIds: '',
      },
      formLabelWidth: '120px',
      roleList: [], //租户角色
      accountRules: {
        userName: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],

        mobile: [{ message: '请输入手机号', trigger: 'blur' }],
        roles: [],
        loginPwd: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
      },
      resetForm: {
        userId: '',
        loginName: '',
        loginPwd: '',
      },
    };
  },
  created() {
    this.getData();
  },
  activated() { },
  computed: {
    ...mapGetters(['userid', 'usertype', 'tenantid']),
  },
  methods: {
    blurP() {
      //  this.passeordFocus=false;
    },
    checkPwd(value) {
      this.passeordFocus = true;
      this.msgText = this.checkStrong(value);
      // console.log( this.msgText);
      if (this.msgText > 1 || this.msgText == 1) {
        document.getElementById('one').style.background = 'red';
      } else {
        document.getElementById('one').style.background = '#eee';
      }
      if (this.msgText > 2 || this.msgText == 2) {
        document.getElementById('two').style.background = 'orange';
      } else {
        document.getElementById('two').style.background = '#eee';
      }
      if (this.msgText == 4) {
        document.getElementById('three').style.background = '#00D1B2';
      } else {
        document.getElementById('three').style.background = '#eee';
      }
    },

    //密码强度
    checkStrong(sValue) {
      var modes = 0;
      //正则表达式验证符合要求的
      if (sValue.length < 1) return modes;
      if (/\d/.test(sValue)) modes++; //数字
      if (/[a-z]/.test(sValue)) modes++; //小写
      if (/[A-Z]/.test(sValue)) modes++; //大写
      if (/\W/.test(sValue)) modes++; //特殊字符

      //逻辑处理
      switch (modes) {
        case 1:
          return 1;
          break;
        case 2:
          return 2;
          break;
        case 3:
        case 4:
          return sValue.length < 4 ? 3 : 4;
          break;
      }
      return modes;
    },

    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      // userType=1  tenantId不传为空 平台
      // ueserType=2  tenantId传
      this.userId = this.userid;
      this.userType = this.usertype;
      // console.log(this.userType)
      if (this.userType == 1) {
        this.tenantId = '';
      } else if (this.userType == 2) {
        this.tenantId = this.tenantid;
      }
      // console.log(this.tenantId);
      let data = {
        keyWord: this.listQuery.title,
        userId: this.userId,
        tenantId: this.tenantId,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
      };
      console.log(data);
      //账号管理列表
      tenant(data)
        .then((res) => {
          // console.log(res);
          this.tableData = res.dataList;
          this.total = res.totals;
          setTimeout(() => {
            this.listLoading.close();
          }, 200);
          if (res.totals == 0) {
            this.totalPage = 1;
          } else {
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
            // console.log(this.totalPage);
          }
        })
        .catch((error) => {
          this.listLoading.close();
        });
      //租户角色
      // console.log(this.tenantid);
      let roleData = {
        userId: this.userId,
        tenantId: this.tenantid,
      };
      roleList(roleData)
        .then((res) => {
          // console.log(res);
          this.roleList = res.obj;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //刷新
    handleRefresh() {
      this.getData();
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    //添加按钮
    handleAddBtn() {
      this.$router.push({
        path: `/nested/account/create`,
        query: {},
      });
    },
    handleClick(row) {
      // console.log(row);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },

    //账号修改
    handleUpdate(row) {
      // console.log(row);
      this.dialogFormVisible = true;
      this.dataForm.id = row.userId; //接收参数
      this.dataForm.tenantId = row.tenantId;
      this.dataForm.userName = row.userName;
      this.dataForm.mobile = row.mobile;
      this.dataForm.roleIds = row.roleIds.split(',');
    },
    //确定修改
    handleSaveClick(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          let addAccountArry = {
            tenantId: this.dataForm.tenantId,
            userName: this.dataForm.userName,
            mobile: this.dataForm.mobile,
            roles: this.dataForm.roleIds.join(','),
            userId: this.userId,
            id: this.dataForm.id,
          };
          // console.log(addAccountArry);

          editAccount(addAccountArry)
            .then((res) => {
              console.log(res);
              if (res.ok == true) {
                this.dialogFormVisible = false;
                this.getData();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                });
              }
            })
            .catch((error) => {
              // console.log(error);
              this.dialogFormVisible = false;
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //账号删除
    handleDel(row) {
      // console.log(row);
      let id = row.userId;
      this.$confirm('确认要删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            delTenant(id)
              .then((res) => {
                // console.log(res);
                if (res.ok == true) {
                  this.getData();
                }
              })
              .catch((error) => {
                console.log(error);
              });
          }
        })
        .catch((e) => { });
    },
    //重置密码按钮
    handleResetPwd(row) {
      this.dialogResetVisible = true;
      // console.log(row);
      this.resetForm.loginName = row.loginName;
      this.resetForm.userId = row.userId;
    },
    //重置密码
    handleResetClick(resetForm) {
      this.$refs[resetForm].validate((valid) => {
        if (valid) {
          let data = {
            userId: this.resetForm.userId,
            passWord: this.resetForm.loginPwd,
          };
          // console.log(data);
          resetPwd(data)
            .then((res) => {
              // console.log(res);
              this.getData();
            })
            .catch((error) => {
              console.log(error);
            });
          this.dialogResetVisible = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.password-strength {
  margin-top: 6px;
}
.elTableBar {
  height: calc(100vh - 204px);
}

.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }

    .opt {
      display: inline-block;
      padding: 0 5px;
      color: #409efd;
      cursor: pointer;
    }
    .line {
      color: #409efd;
    }
  }
}
.role-permission-box {
  background: rgba(245, 248, 251, 1);
  border: 1px dashed #d8dadb;
  padding: 16px 20px;
  margin: 0 30px;
  .title {
    font-size: 14px;
    font-weight: normal;
    margin-bottom: 10px;
  }

  .role-name {
    font-weight: normal;
    font-size: 14px;
    color: #005ea4;
    padding-left: 5px;
    border-left: 2px solid #005ea4;
    margin-bottom: 10px;
  }
  .role-item {
    float: left;
    margin-bottom: 10px;
    margin-right: 16px;
    .role-checkbox {
      margin-right: 5px;
    }
  }
}
.el-select::v-deep {
  width: 100%;
}
.el-form-item__error::v-deep {
  top: 0 !important;
  left: 460px !important;
}
</style>
