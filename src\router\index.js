import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/example/unified',
    // children: [{
    //   path: 'main',
    //   name: 'main',
    //   component: () => import('@/views/main/index'),
    //   meta: { title: '首页', icon: 'home' }
    // }],
    hidden:true
  },
  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/main',
  //   children: [{
  //     path: 'main',
  //     name: 'main',
  //     component: () => import('@/views/main/index'),
  //     meta: { title: '首页', icon: 'home' }
  //   }],
  // },
  {
    path: '/redirect/:path*', // 重定向路由
    // component: () => import('@/views/layout/components/Sidebar/redirect'), hidden: true
    component: Layout,
    hidden: true,
    children: [{
      path: '',
      component: () => import('@/layout/components/Sidebar/redirect')
    }]
  },

  {
    path: '/security/network/index',
    name: 'network',
    component: () => import('@/views/security/network/index'),
    meta: { title: '网络安全态势', icon: 'safe' },
    hidden: true

  },
  {
    path: '/security/password/index',
    name: 'password',
    component: () => import('@/views/security/password/index'),
    meta: { title: '密码安全态势', icon: 'safe' },
    hidden: true

  },
  {
    path: '/security/rotation/index',
    name: 'rotation',
    component: () => import('@/views/security/rotation/index'),
    meta: { title: '轮播态势', icon: 'safe' },
    hidden: true

  },



  // 404 page must be placed at the end !!!


]
//异步挂载的路由
// 动态需要根据权限加载的路由表
// 这个路由链，根据数据库中的一一对应，也就是说这是一个最完整的路由链，
// 根据登录的用户权限的不同，然后从中提取出对应当前用户的路由添加到vue router中
// meta:属性中resources属性最为重要，用meta.resources和我们获取用户信息中menus.resources匹配
export const asyncRoutes=[{ path: '*', redirect: '/404', hidden: true }]
const createRouter = () => new Router({
  //mode: 'history', // require service support
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})


const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
router.selfaddRoutes = function (params){
  const newRouter = createRouter()
  router.matcher = newRouter.matcher;
  router.addRoutes(params)

}

export default router
