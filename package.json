{"name": "vue-safe-template", "version": "1.0.0", "description": "", "author": "", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"axios": "0.18.1", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.8", "core-js": "^2.6.12", "default-passive-events": "^2.0.0", "echarts": "^4.8.0", "echarts-liquidfill": "^2.0.6", "el-table-bar-base": "^2.1.4", "element-ui": "2.13.2", "es6-promise": "^4.2.8", "file-saver": "^2.0.5", "iconv-lite": "^0.6.3", "js-base64": "^2.6.3", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.21", "mammoth": "^1.4.21", "mathjs": "^11.7.0", "moment": "^2.27.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pdfjs-dist": "^2.5.207", "qs": "^6.11.0", "regenerator-runtime": "^0.13.11", "screenfull": "^4.2.0", "swiper": "5.x", "vue": "2.6.10", "vue-awesome-swiper": "4.1.1", "vue-pdf": "^4.2.0", "vue-router": "3.0.6", "vuex": "3.1.0", "vuex-persistedstate": "^3.0.1", "wangeditor": "^4.7.15", "xlsx": "^0.16.0"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.21.0", "@vue/cli-plugin-babel": "^3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-cli": "^6.26.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-env": "^1.7.0", "chalk": "2.4.2", "connect": "3.6.6", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "5.15.3", "eslint-plugin-compat": "^4.1.2", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "^4.3.2", "sass": "^1.26.8", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}