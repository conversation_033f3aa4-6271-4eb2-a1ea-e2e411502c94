import request from '@/utils/request'
//系统设置
//版本更新---查看详情
export function getVersionInfo() {
  // debugger
  return request({
    url: '/api/version/getVersionInfo',
    method: 'post',
    
  })
}
//版本更新-上传文件
export function uploadFile(form) {
  // debugger
  return request({
    url: '/api/version/uploadFile',
    method: 'post',
    headers: {
      "Content-Type": "multipart/form-data"
    },
    data:form,
    
  })
}
//版本更新-----添加
export function newVersion(data) {
  // debugger
  return request({
    url: '/api/version/newVersion',
    method: 'post',
    params:data
    
  })
}
