<template>
  <div :id="id" :style="style"></div>
</template>

<script>

export default {
  name: 'Radar',
  props: {
    id: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    chartData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      chart: ''
    }
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height
      }
    }
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal
        } else {
          this.init()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose()
      }
      this.init()
    })
    window.addEventListener('resize', this.chart.resize)
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize)
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose()
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id))
      this.$nextTick(() => {
        this.setOption()
        // console.log(this.chartData)
      })
    },
    setOption() {
      const that = this
      let option = {}
      option = {
        tooltip: {
          trigger: "item",
          padding: 10,
        },
        radar: [
          {
            indicator: (function () {
              var res = [];
              for (var i = 0; i < that.chartData.radarNames.length; i++) {
                res.push({
                  name: that.chartData.radarNames[i],
                  max: 100,
                  color: "#DEF3FF",
                });
              }
              return res;
            })(),
            center: ["50%", "50%"],
            radius: 150,
            startAngle: 90,
            shape: "circle",
            triggerEvent: true,
            name: {
              fontSize: 14,
            },

            axisLine: {
              lineStyle: {
                color: "#4BB7FF",
                width: 2,
              },
            },
            splitLine: {
              lineStyle: {
                color: "#0091FF",
                width: 2,
              },
            },

            splitArea: {
              show: true,
              areaStyle: {
                color: [
                  "rgba(0, 34, 255, 0.6)",
                  "rgba(0, 34, 255, 0.5)",
                  "rgba(0, 34, 255, 0.4)",
                  "rgba(0, 34, 255, 0.32)",
                  "rgba(0, 34, 255, 0.2)",
                ],
              },
            },
          },
        ],
        series: [
          {
            type: "radar",
            emphasis: {
              lineStyle: {
                color: "#00E5FF",
                width: 3,
              },
              areaStyle: {
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0,
                      color: " rgba(57, 57, 255, 0)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#00E5FF", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            data: [
              {
                name: "雷达",
                value: that.chartData.radarNums,
                symbol: "none",
                lineStyle: {
                  color: "#00E5FF",
                  width: 4,
                },
                itemStyle: {
                  borderColor: "#00E5FF",
                  borderWidth: 0,
                },
                areaStyle: {
                  color: {
                    type: "radial",
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                      {
                        offset: 0,
                        color: " rgba(57, 57, 255, 0)", // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "#00E5FF", // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  },
                },
              },
            ],
          },
        ],
      }


      this.chart.setOption(option, true)
    }
  }
}
</script>
