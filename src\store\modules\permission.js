import { asyncRoutes, constantRoutes } from '@/router'
import Layout from '@/layout'


/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

const loadView = (view) => {
  return (resolve) => require([`@/views${view}/index`], resolve)
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routers) {
  const accessedRoutes = []

  for (var i in routers) {
    const router = routers[i]
    // console.log(router);
    const newRouter = {
      path: router.url,
      component: router.pid === null || router.pid === '' ? Layout : loadView(router.url) ,
      hidden: false,
      children: [],
      name: router.name,
      meta: { title: router.name, icon: router.icon }

    }
    if (router.pid === '' || router.pid === null) {
      newRouter['redirect'] = 'noRedirect'
    }
    if( router.children == null || router.children.length==0 ){
      let child={
        path: router.url,
        name: router.name,
        component: loadView(router.url),
        meta: { title: router.name, icon: router.icon }
      };

      newRouter['children'].push(child);
    }


    // if (router.name === '安全日志') {
    //   newRouter = {
    //     path: '/log',
    //     component: Layout,
    //     children: [
    //       {
    //         path: router.url,
    //         name: router.name,
    //         component: () => import('@/views/log/index'),
    //         meta: { title: router.name, icon: router.icon }
    //       }
    //     ]
    //   }
    // }

    if (router.children && router.children.length) {
      newRouter.children = filterAsyncRoutes(router.children)
    }
    if (router.url == '/nested') {
      newRouter.children.push(
      //   {
      //   path: '/nested/tenant/auth/:userId(\\d+)',
      //   component: () => import('@/views/nested/tenant/auth/index'), // Parent router-view
      //   name: 'auth',
      //   meta: {
      //     title: '授权周期',
      //     icon: '',
      //     guidePath: true,
      //     jumpPath: '/nested/tenant'
      //   },
      //   hidden: true,
      // },
        // {
        //   path: '/nested/tenant/create',
        //   component: () => import('@/views/nested/tenant/create/index'), // Parent router-view
        //   name: 'tenantCreate',
        //   meta: {
        //     title: '添加租户',
        //     icon: '',
        //     guidePath: true,
        //     jumpPath: '/nested/tenant'
        //   },
        //   hidden: true,
        // },
        {
          path: '/nested/account/create',
          component: () => import('@/views/nested/account/create/index'), // Parent router-view
          name: 'accountCreate',
          meta: {
            title: '添加账号',
            icon: '',
            guidePath: true,
            jumpPath: '/nested/account'
          },
          hidden: true,
        },

      )
      
    }
    // if (router.name === '镜像管理') {
    //   newRouter.children.push(

    //     {
    //       path: '/mirror/create',
    //       component: () => import('@/views/mirror/create/index'), // Parent router-view
    //       name: 'mirrorCreate',
    //       meta: {
    //         title: '添加镜像',
    //         icon: '',
    //         guidePath: true,
    //         jumpPath: '/mirror/index'
    //       },
    //       hidden: true,
    //     })
    // }

    if (router.url=== '/monitoring') {
      newRouter.children.push(

        {
          path: 'echarts',
          component: () => import('@/views/monitoring/echarts/index'), // Parent router-view
          name: 'virtualmEcharts',
          meta: {
            title: '虚拟机监控',
            icon: 'virtualm',
            guidePath: true,
            jumpPath: '/monitoring/index'
          },
          hidden: true,
        },
        {
          path: '/monitoring/alarm/history',
          component: () => import('@/views/monitoring/alarm/history'), // Parent router-view
          name: 'historyAlarm',
          meta: {
            title: '历史告警信息',
            icon: '',
            guidePath: true,
            jumpPath: '/monitoring/alarm/index'
          },
          hidden: true,
        })
    }
    if (router.url === '/system') {
      newRouter.children.push(
        {
          path: '/system/role/create',
          component: () => import('@/views/system/role/create/index'), // Parent router-view
          name: 'roleCreate',
          meta: {
            title: '添加角色',
            icon: '',
            guidePath: true,
            jumpPath: '/system/role'
          },
          hidden: true,
        },
        {
          path: '/system/menu/create',
          component: () => import('@/views/system/menu/create/index'), // Parent router-view
          name: 'menuCreate',
          meta: {
            title: '添加菜单',
            icon: '',
            guidePath: true,
            jumpPath: '/system/menu'
          },
          hidden: true,
        },
        {
          path: '/system/product/create',
          component: () => import('@/views/system/product/create/index'), // Parent router-view
          name: 'productCreate',
          meta: {
            title: '添加产品',
            icon: '',
            guidePath: true,
            jumpPath: '/system/product'
          },
          hidden: true,
        },
        {
          path: '/system/productInfo/create',
          component: () => import('@/views/system/productInfo/create/index'), // Parent router-view
          name: 'productInfoCreate',
          meta: {
            title: '添加产品',
            icon: '',
            guidePath: true,
            jumpPath: '/system/productInfo'
          },
          hidden: true,
        }
        )
    }



    accessedRoutes.push(newRouter)

    //console.log(accessedRoutes)
    //debugger
  }
  return accessedRoutes


}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    //debugger
    state.routes = constantRoutes.concat(routes)
  },
  RESET_ROUTES: (state) => {
    state.routes = []
    state.addRoutes = []
  }
}

const actions = {
  generateRoutes({ commit }, data) {
    return new Promise(resolve => {
      // console.log(data)
      var roles = ['操作员', '审计员', '管理员']
      commit('user/SET_ROLES', roles, { root: true })


      const asyncRouter = filterAsyncRoutes(data)
      //console.log(asyncRouter)
      // debugger
      // 把权限路由表保存起来
      commit('SET_ROUTES', asyncRouter)
      resolve(asyncRouter)

    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
