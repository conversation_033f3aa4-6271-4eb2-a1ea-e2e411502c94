<template>
  <div class="navbar" :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <!-- <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" /> -->

    <div class="right-menu">
      <!-- <template v-if="device!=='mobile'">
        <screenfull id="screenfull" cl/xass="right-menu-item hover-effect" />
      </template> -->

      <router-link target="_blank" to="/security/network/index">
        <span class="safe"
          ><span class="svg-container"><svg-icon icon-class="safe" /></span
          >云安全态势</span
        >
      </router-link>
      <!-- <router-link target="_blank" to="/security/password/index">
        <span class="safe"
          ><span class="svg-container"><svg-icon icon-class="safe-password" /></span
          >云密码态势</span
        >
      </router-link> -->
      <!-- <router-link target="_blank" to="/security/rotation/index">
        <span class="safe"
          ><span class="svg-container"><svg-icon icon-class="safe-rotation" /></span
          >轮播态势</span
        >
      </router-link> -->
      <el-dropdown
        class="message-container"
        trigger="click"
        @visible-change="handleChange"
      >
        <span class="safe mr_10" style="background: none" title="最近消息"
          ><span class="el-icon-bell mr_0"></span
          ><el-badge is-dot class="badge-item" v-if="status == 1"></el-badge
        ></span>
        <el-dropdown-menu
          slot="dropdown"
          class="message-dropdown"
          ref="messageDrop"
        >
          <el-tabs
            v-model="activeName"
            @tab-click="handleClick"
            :stretch="true"
            ref="tabs"
            v-if="tabsVisible"
          >
            <el-tab-pane label="登录日志" name="first">
              <el-scrollbar wrap-class="scrollbar-wrapper">
                <ul>
                  <li v-for="(item, index) in logData">
                    <div class="msg-container">
                      <div class="msg-left">
                        <div>
                          <!-- <i class="dot"></i> -->
                          {{ item.ip }}
                        </div>
                        <div>{{ item.user_name }}</div>
                      </div>
                      <div class="time">{{ item.create_time }}</div>
                    </div>
                  </li>
                </ul></el-scrollbar
              >

              <div class="checkAll" @click="toLog">查看全部</div>
            </el-tab-pane>
            <el-tab-pane label="授权消息" name="second">
              <div v-if="status == 1">
                <div class="expire">
                  <div class="expire-title">
                    <img src="@/assets/tips.png" alt="" class="tips-img" />
                    <strong>授权码即将过期</strong>
                  </div>
                  <p class="expire-tip">还有{{ expireDate }}天即将过期</p>

                  <el-button
                    type="primary"
                    @click.prevent="toUpdate"
                    style="margin-top: 10px"
                    >更新</el-button
                  >
                </div>
              </div>
              <div v-else class="null-body">
                <img src="../../assets/data_null.png" alt="" />
                <p>暂无消息</p>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <!-- <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar"> -->
          <span class="user-name">{{ name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <!-- <router-link to="/">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link> -->
          <!-- <el-dropdown-item @click.native="cerLogin">
            <div class="itemContainer">
              <span><i class="el-icon-document"></i>管理证书登录</span>
              <span v-if="ukeyState == '' || ukeyState == -1">未开启</span>
              <span v-else>已开启</span>
            </div>
          </el-dropdown-item> -->
          <el-dropdown-item @click.native="logout">
            <div class="itemContainer">
              <span style="display: block"
                ><svg-icon icon-class="logout"></svg-icon>退出</span
              >
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!-- <el-dialog
      top="0"
      title="即将到期"
      width="640px"
      :visible.sync="dialogAuthVisible"
      :append-to-body="true"
    >
      <div class="expire">
        <div class="expire-title">
          <img src="@/assets/tips.png" alt="" class="tips-img" />
          <strong>授权码即将过期</strong>
        </div>
        <p class="expire-tip">
          您的授权码，还有{{
            expireDate
          }}天即将过期，为不影响您的正常使用，请尽快联系管理员进行授权更新，以继续使用本产品
        </p>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="toUpdate">去更新</el-button>
        <el-button type="default" @click="dialogAuthVisible = false">取消</el-button>
      </div>
    </el-dialog> -->
    <!-- <ukey-login
      :title="ukeyTitle"
      :dialogUkeyVisible="dialogUkeyVisible"
      :formData="ukeyFormData"
      :formRules="ukeyFormRules"
      ref="ukeyForm"
      @cancle="closeUkey"
      @uKeyConfirm="confirmUkey"
      v-if="dialogUkeyVisible == true"
    /> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import {
  getNews,
  getExpireDate,
  getCerInfo,
  addTCertificat,
  updateStateByUserId,
} from "@/api/user.js";
import { loginLog } from "@/api/system.js";
import Logo from "./Sidebar/Logo";
import UkeyLogin from "@/components/UkeyLogin/index.vue";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    Logo,
    UkeyLogin,
    UkeyLogin,
  },
  data() {
    return {
      dialogAuthVisible: false,
      status: "", //到期状态
      expireDate: "", //到期天数
      timer: null,
      ukeyTitle: "管理证书登录",
      dialogUkeyVisible: false,
      ukeyState: null,
      activeName: "first",
      logData: [],
      resetUkeyForm: {},
      ukeyFormData: {},
      ukeyFormRules: {
        state: [{ required: true, message: "请选择证书登录状态", trigger: "blur" }],
        certificate: [{ required: true, message: "请输入证书", trigger: "blur" }],
        ukeyPin: [{ required: true, message: "请输入PIN", trigger: "blur" }],
      },
      tabsVisible: false,
    };
  },
  created() { },
  mounted() {
    this.getData();
  },

  computed: {
    ...mapGetters(["sidebar", "avatar", "device", "name", "userid"]),
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },

  watch: {
    currentLang() {
      resetTabActivePosition(this.$refs.tabs.$el);
    },
  },
  methods: {
    handleChange(val) {
      // console.log(val);
      if (val == true) {
        this.tabsVisible = true;
      } else {
        this.tabsVisible = false;
      }
    },
    resetTabActivePosition($el) {
      this.$nextTick(() => {
        const activeEl = $el.querySelector(".el-tabs__item.is-active");
        const lineEl = $el.querySelector(".el-tabs__active-bar");
        const style = getComputedStyle(activeEl);
        const pl = style.paddingLeft.match(/\d+/)[0] * 1;
        const pr = style.paddingRight.match(/\d+/)[0] * 1;
        const w = style.width.match(/\d+/)[0] * 1;
        lineEl.style.transform = "translateX(" + (activeEl.offsetLeft + pl) + "px)";
        lineEl.style.width = w - pl - pr + "px";
      });
    },
    getData() {
      this.getNewsPort();
      this.getExpire();
      // this.getUkeyInfo();
      this.timerCheck();
      this.getLoginInfo();
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    // cerLogin() {
    //   this.dialogUkeyVisible = true;
    // },
    // closeUkey() {
    //   this.$refs.ukeyForm.$refs.ruleForm.resetFields();
    //   this.dialogUkeyVisible = false;
    // },
    // confirmUkey(data) {
    //   console.log(data);
    //   if (data.id == "") {
    //     //新增addTCertificat,updateStateByUserId
    //     addTCertificat(data)
    //       .then((res) => {
    //         console.log(res);
    //         // debugger;
    //         if (res.code == 1) {
    //           this.logout();
    //         }
    //       })
    //       .catch((error) => {
    //         console.log(error);
    //       });
    //   } else {
    //     //更新
    //     updateStateByUserId(data)
    //       .then((res) => {
    //         console.log(res);
    //         if (res.code == 1) {
    //           this.logout();
    //         }
    //       })
    //       .catch((error) => {
    //         console.log(error);
    //       });
    //   }
    // },
    getLoginInfo() {
      let data = {
        keyWord: "",
        page: 1,
        limit: 10,
      };
      loginLog(data)
        .then((res) => {
          console.log(res);
          this.logData = res.data.rows;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // getUkeyInfo() {
    //   let data = {
    //     userId: this.userid,
    //   };
    //   console.log(data);
    //   getCerInfo(data)
    //     .then((res) => {
    //       console.log(res);
    //       this.ukeyState = res.data.state;
    //       // this.resetUkeyForm=res.data;
    //       this.ukeyFormData = res.data;
    //       this.ukeyFormData.userId = this.userid;
    //       console.log("ukey证书");
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    // },
    getNewsPort() {
      getNews()
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.status = res.data.status;
            this.expireDate = res.data.expireDate;
            if (this.status == 1) {
              this.dialogAuthVisible = true;

              // this.handleLogin();
            } else {
              // this.$message.error("授权码错误");
              this.dialogAuthVisible = false;
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //授权校验
    getExpire() {
      getExpireDate()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            //验证状态 1为已过期，强制退出登录
            if (res.data.status == 1) {
              this.$message.error("授权已过期");
              this.logout();
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //定时授权校验
    timerCheck() {
      if (this.timer != null) {
        return;
      }
      this.timer = setInterval(() => {
        setTimeout(this.getExpire, 0);
      }, 3600000);
    },
    toUpdate() {
      this.$router.push({ path: `/system/license` });
      this.dialogAuthVisible = false;
    },
    toLog() {
      this.$router.push({ path: `/system/log` });
      // console.log(this.$refs.messageDrop);
      this.$refs.messageDrop.showPopper = false;
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
  beforeDestroy() {
    // 离开页面的时候清除

    clearInterval(this.timer);
    this.timer = null;
  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: 260px;
}

.itemContainer {
  width: 180px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .svg-icon {
    display: inline-block;
    margin-right: 8px;
  }
}
.message-dropdown {
  min-width: 260px;
  padding-bottom: 0;
  ul {
    padding-top: 8px;
    li {
      padding: 10px 16px;
    }
  }

  .msg-container {
    .msg-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #ff3f46;
        margin-right: 7px;
      }
    }

    .time {
      color: #96989b;
      margin: 8px 0 0 3px;
      font-size: 12px;
    }
  }
}
.null-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  img {
    width: auto;
    height: 80px;
  }
  p {
    color: #999;
  }
}
.checkAll {
  text-align: center;
  padding: 10px;
  cursor: pointer;
  border-top: 1px solid #f5f5f5;
}
.message-dropdown::v-deep .el-tabs__nav-wrap {
  padding: 0;
}
.safe::v-deep .badge-item {
  vertical-align: top;
  .el-badge__content {
    border: none;
  }
}
</style>
