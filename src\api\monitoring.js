import request from '@/utils/request'
//监控中心
//监控中心列表
export function getmonitorList(data) {
  // debugger
  return request({
    url: '/api/monitor/monitorList',
    method: 'post',
    params:data 
  })
}
//监控云主机数量
export function operatingStatus(data) {
    // debugger
    return request({
      url: '/api/monitor/operatingStatus',
      method: 'post',
      params:data 
    })
  }
  //监控图表
export function echartsList(data) {
  // debugger
  return request({
    url: '/api/monitor/monitoringInformation',
    method: 'post',
    params:data 
  })
}


//监控中心-加密机监控
export function jmjMonitor() {
  // debugger
  return request({
    url: '/api/hx/jmjMonitor',
    method: 'get',
    // params:data
  })
}

//监控中心-密码服务实例监控
export function insMonitor() {
  // debugger
  return request({
    url: '/api/hx/insMonitor',
    method: 'get',
    // params:data
  })
}

//监控中心-应用服务器监控
export function serviceMonitor() {
  // debugger
  return request({
    url: '/api/hx/serviceMonitor',
    method: 'get',
    // params:data
  })
}

//监控中心-应用服务器监控
export function jmjInfo() {
  // debugger
  return request({
    url: '/api/hx/jmjInfo',
    method: 'get',
    // params:data
  })
}

//监控中心-签章服务器信息
export function qzfwqInfo() {
  // debugger
  return request({
    url: '/api/hx/qzfwqInfo',
    method: 'get',
    // params:data
  })
}

//监控中心-签名服务器信息
export function qmfwqInfo() {
  // debugger
  return request({
    url: '/api/hx/qmfwqInfo',
    method: 'get',
    // params:data
  })
}

//监控中心-应用服务器信息
export function yyfwqInfo() {
  // debugger
  return request({
    url: '/api/hx/yyfwqInfo',
    method: 'get',
    // params:data
  })
}