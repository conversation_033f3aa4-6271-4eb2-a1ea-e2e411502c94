<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">安全设备监控</h3>
      </div>
      <div class="physical-box1">
        <el-row :gutter="15">
          <el-col :xs="24" :sm="24" :md="8" :lg="8">
            <div class="grid-content">
              <div class="img-box">
                <img src="@/assets/offline.png" alt class="virtualm-img" />
              </div>

              <div class="text-box">
                <span class="title">离线</span>
                <span class="number">{{ operatingData.stop }}</span>

              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8">
            <div class="grid-content">
               <div class="img-box">
                   <img
                src="@/assets/virtual-machine.png"
                alt
                class="virtualm-img"
              />
               </div>

              <div class="text-box">
                <span class="title">虚拟机总数</span>
                <span class="number">{{ operatingData.total }}</span>

              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8">
            <div class="grid-content">
               <div class="img-box">
                  <img src="@/assets/alarm.png" alt class="virtualm-img" />
               </div>

              <div class="text-box">
                 <span class="title">运行</span>
                <span class="number">{{ operatingData.running }}</span>

              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="physical-box2">
        <div class="container">
          <div class="serch-box clearfix">
            <div class="filter-container">
              <el-button
                v-waves
                class="filter-item"
                type="primary"
                @click="handleRefresh()"
              >
                <svg-icon icon-class="refresh" />
              </el-button>

              <!-- <div class="search-container">
                <el-input
                  v-model="listQuery.title"
                  placeholder="产品/租户名"
                  style="width: 200px;"
                  class="filter-item"
                />
                <span class="el-icon-search search-btn"></span>
              </div> -->
            </div>
            <div class="page-box">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="sizes, prev,slot, next,total"
                :total="total"
              >
                <span class="pageNum">
                  {{ this.listQuery.page }}
                  <i class="divider">/</i>
                  {{ totalPage }}
                </span>
              </el-pagination>
            </div>
          </div>
          <div class="table-box">
            <el-table-bar>
              <el-table
                :data="tableData"
                style="width: 100%; margin-bottom: 20px"
                row-key="uuid"
              >
                <el-table-column
                  prop="name"
                  label="名称"
                  sortable
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                  prop="vmNics[0].ip"
                  label="地址"
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                  prop="cpuNum"
                  label="CPU"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="内存" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.memorySize | diskSize }} </span>
                  </template>
                </el-table-column>
                <el-table-column label="云盘真实容量" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span
                      >{{ scope.row.allVolumes[0].actualSize | diskSize }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="platform"
                  label="操作系统"
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column
                  label="启用状态"
                  show-overflow-tooltip
                  sortable
                >
                  <template slot-scope="scope">
                    <div>
                      <span v-if="scope.row.state == 'Running'" class="normal">
                        <i class="dot"></i>运行中
                      </span>
                      <span
                        v-if="scope.row.state == 'Stopped'"
                        class="abnormal"
                      >
                        <i class="dot"></i>已停止
                      </span>
                      <span v-if="scope.row.state == 'Paused'" class="paused">
                        <i class="dot"></i>已暂停
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                  <template slot-scope="scope">
                    <el-dropdown>
                      <span class="el-dropdown-link">
                        <i
                          class="el-icon-more"

                        ></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <div @click="handleClick(scope.row)" class="opt">
                          查看图表
                        </div>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </el-table-bar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import { getmonitorList, operatingStatus } from "@/api/monitoring.js";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { Loading } from "element-ui";

export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        importance: undefined,
        title: undefined,
        type: undefined,
      },
      currentPage: 1,
      totalPage: 1,
      downloadLoading: false,
      dialogFormVisible: false,
      formLabelWidth: "120px",
      dialogEditVisible: false,
      operatingData: [
        {
          running: 0,
          total: 0,
          other: 0,
          stop: 0,
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  filters: {
    //容量转换
    diskSize(num) {
      if (num == 0) return "0 B";
      var k = 1024; //设定基础容量大小
      var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  所以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },
  },
  methods: {
    //列表
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = {
        page: this.listQuery.page,
        limit: this.listQuery.page * this.listQuery.limit,
      };
      // console.log(data);

      getmonitorList(data)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
           setTimeout(() => {
            this.listLoading.close();
          }, 200);
            if (JSON.stringify(res.data) != "{}") {
              this.tableData = res.data.inventories;
              this.total = res.data.total;
              if (res.data.total == 0) {
                this.totalPage = 1;
              } else {
                this.totalPage = Math.ceil(this.total / this.listQuery.limit);
              }
            }
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
      operatingStatus()
        .then((res) => {
           console.log(res);
          if (res.code == 1) {
            this.operatingData = res.data;
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //刷新
    handleRefresh() {
      this.getData();
    },

    //查看图表
    handleClick(row) {
      // console.log(row);

      this.$router.push({
        path: `/monitoring/echarts`,
        name: "virtualmEcharts",
        query: {
          id: row.uuid,
        },
      });
    },

    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
</script>
<style lang="scss" scoped>
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
}
.elTableBar {
  height: calc(100vh - 420px);
}

.physical-box1 {
  padding: 20px 20px 20px 30px;
  background-color: #fff;
  .grid-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 160px;
    padding: 20px 30px;
    background: #E9F6FE;
    border-radius: 3px;
    color: #333;
    margin-right: 10px;
    .img-box{
      flex:1;
      text-align: right;
      .virtualm-img {
        width: 56px;
        height: auto;
        margin-right: 30px;
      }
    }

    .text-box {
      flex: 1;
      display: flex;
      align-items: center;
      .title{
        display: inline-block;
        position: relative;
        font-size: 16px;
        padding:  8px 10px;
        &::after{
          content: '';
          position: absolute;
          top:0;
          right: 0;
          bottom: 0;
          width: 1px ;
          height: 32px;
         background: #BABBBE;
        }

      }
      .number {
        font-size: 28px;
        padding-left: 10px;

      }
    }
  }
}
.physical-box2 {
  padding: 0 30px;
  background-color: #fff;
  .container {
    background: #fff;
    border-radius: 3px;
    height: auto;
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
    .dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 5px;
    }

    .normal {
      color: #45ba79;
      .dot {
        background-color: #45ba79;
      }
    }
    .abnormal {
      color: #f84b4b;
      .dot {
        background-color: #f84b4b;
      }
    }
    .paused {
      color: #97a4b6;
      .dot {
        background-color: #97a4b6;
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>

