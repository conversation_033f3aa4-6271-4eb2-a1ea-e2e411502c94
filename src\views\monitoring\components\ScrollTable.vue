<template>
  <el-table
    :data="tableData"
    height="100%"
    style="width: 100%"
    :ref="tabRef ? tabRef : null"
    :id="id"
    :show-header="false"
    @mouseenter.native="stopScroll"
    @mouseleave.native="startScroll"
    @row-click="rowClick"
  >
    <el-table-column prop="id" width="36" show-overflow-tooltip>
      <template slot-scope="scope">
        <div class="cell-hd">
          <img
            src="@/assets/monitor/tip-danger.png"
            alt=""
            v-if="scope.row.warnType == 3"
          />
          <img
            src="@/assets/monitor/tip-warm.png"
            alt=""
            v-else-if="scope.row.warnType == 2"
          />
          <img src="@/assets/monitor/tip-normal.png" alt="" v-else />
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="type" show-overflow-tooltip>
      <template slot-scope="scope">
        <div class="cell-hd">
          <span
            class="text"
            :class="
              scope.row.warnType == 3
                ? 'danger-row'
                : scope.row.warnType == 2
                ? 'warm-row'
                : 'normal-row'
            "
            >{{ scope.row.warningDetails }}</span
          >
        </div>
      </template>
    </el-table-column>
    <template slot="empty">
      <img src="@/assets/monitor/null.png" alt class="empty-img" />
      <p class="empty-text">当前无告警信息，设备运行健康！</p>
    </template>
  </el-table>
</template>

<script>
export default {
  name: 'ScrollTable',
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    tabRef: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      rollTimer: null,
    };
  },

  watch: {
    tableData: {
      handler(val) {
        this.tableData = val;
      },
      deep: true,
    },
    rollTimer: {
      handler(val) {
        this.rollTimer = val;
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.tableScroll(false);
    });
  },
  methods: {
    rowClick(row) {
      // console.log(row);
      this.tableScroll(true);
      this.$emit('send', row);
    },
    startScroll() {
      this.tableScroll(false);
    },
    stopScroll() {
      this.tableScroll(true);
    },
    //滚动方法
    tableScroll(stop) {
      if (stop) {
        clearInterval(this.rollTimer);
        return;
      }
      const tab = this.tabRef;
      // console.log(tab);
      const table = this.$refs[`${tab}`];
      // console.log(table);
      const divData = table.bodyWrapper;
      this.rollTimer = setInterval(() => {
        divData.scrollTop += 1;
        if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
          divData.scrollTop = 0;
        }
      }, 50);
    },
  },
};
</script>

<style lang="scss" scoped>
.cell-hd {
  display: flex;
  align-items: center;
  img {
    width: 32px;
  }
  .text {
    width: 100%;
    display: block;
    padding: 5px 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.danger-row {
      background: rgba(249, 91, 108, 0.1);
      color: rgba(249, 91, 108, 1);
    }
    &.warm-row {
      background: rgba(255, 199, 0, 0.1);
      color: rgba(255, 199, 0, 1);
    }
    &.normal-row {
      background: rgba(16, 143, 244, 0.1);
      color: rgba(16, 143, 244, 1);
    }
  }
}
.el-table::v-deep {
  overflow: hidden;
  background: none;
  border-spacing: 0;
  &::before {
    height: 0;
  }
  tr,
  td {
    &:hover {
      background: none !important;
    }
  }
  tr {
    cursor: pointer;
    &:hover {
      td {
        background: none;
      }
      img {
        width: 38px;
      }
      .text {
        padding: 8px;
      }
    }
  }
  th,
  td {
    background: none;
    border: none;
    padding: 12px 0;
  }
  .cell {
    padding: 0;
  }
  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: none;
      border-radius: 3px;
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #ddd;
      }
    }
  }
  .el-table__empty-text {
    width: 100%;
    line-height: normal;
  }
}
.el-table--scrollable-x::v-deep {
  .el-table__body-wrapper {
    overflow-x: hidden;
  }
}

.empty-img {
  width: 160px;
}
.empty-text {
  line-height: 1.2;
}
</style>
