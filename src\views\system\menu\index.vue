<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">菜单管理</h3>
        </div>
        <div class="serch-box clearfix">
          <div class="filter-container">
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              @click="handleRefresh()"
            >
              <svg-icon icon-class="refresh" />
            </el-button>
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="handleAddBtn()"
              >添加</el-button
            >
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-arrow-down"
              @click="openAll()"
              >全部展开</el-button
            >
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-arrow-up"
              @click="foldAll()"
              >全部折叠</el-button
            >
            <!-- <div class="search-container">
              <el-input
                v-model="listQuery.title"
                placeholder="菜单名称"
                style="width: 200px"
                class="filter-item"
                v-on:input="search"
              />
              <span
                class="el-icon-search search-btn"
                @click="handleSearch()"
              ></span>
            </div> -->
          </div>
        </div>
        <div class="table-box">
          <el-table-bar>
            <el-table
              :data="tableData"
              style="width: 100%; margin-bottom: 20px"
              row-key="id"
              default-expand-all
              ref="theTable"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column
                prop="name"
                label="名称"
                sortable
              ></el-table-column>
              <el-table-column prop="url" label="菜单地址"></el-table-column>
              <el-table-column prop="icon" label="图片地址"></el-table-column>
              <el-table-column
                prop="orderNum"
                label="排序"
                sortable
              ></el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <div @click="handleUpdate(scope.row)" class="opt">
                        修改
                      </div>
                      <div @click="handleDel(scope.row)" class="opt">删除</div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </el-table-bar>
        </div>
      </div>
    </div>

    <el-dialog
      title="修改菜单"
      :visible.sync="dialogEditVisible"
      width="30%"
      top="0"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" ref="editForm" :rules="accountRules">
        <el-form-item label="名称" :label-width="formLabelWidth" prop="name">
          <el-input v-model="editForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="父级编号"
          :label-width="formLabelWidth"
          prop="pName"
        >
          <el-input
            v-model="editForm.pName"
            autocomplete="off"
            @click.stop.native="selectTree"
          ></el-input>
          <div class="treedata-box" v-show="showTree">
            <el-scrollbar wrap-class="scrollbar-wrapper">
              <div class="treedata-content">
                <el-input
                  placeholder="输入关键字搜索"
                  v-model="filterText"
                  @click.stop.native="showTree = true"
                ></el-input>

                <el-tree
                  class="filter-tree"
                  :data="data"
                  :props="defaultProps"
                  default-expand-all
                  :filter-node-method="filterNode"
                  @node-click="handleNodeClick"
                  ref="tree"
                ></el-tree>
              </div>
            </el-scrollbar>
          </div>
        </el-form-item>
        <el-form-item label="请求地址" :label-width="formLabelWidth" prop="url">
          <el-input v-model="editForm.url" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="图标" :label-width="formLabelWidth" prop="icon">
          <el-input v-model="editForm.icon" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item
          label="排序"
          :label-width="formLabelWidth"
          prop="orderNum"
        >
          <el-input v-model="editForm.orderNum" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit('editForm')" v-dbClick
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { menuList, deleteMenuById, editorUpdateMenu } from '@/api/system.js';
import { Loading } from 'element-ui';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      pName: '',
      radio: 1,
      account: '',
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
        type: undefined,
        sort: '+id',
      },
      currentPage: 1,
      downloadLoading: false,
      formLabelWidth: '120px',
      dialogEditVisible: false,
      showTree: false,
      filterText: '',
      data: [],
      defaultProps: {
        children: 'children',
        label: (data, node) => {
          //console.log(data, node);
          return data.name;
        },
      },
      editForm: {
        id: '',
        name: '',
        pName: '',
        pId: '',
        url: '',
        icon: '',
        orderNum: '',
        menuType: '',
      },
      accountRules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        // pName: [{ required: true, message: "父级编号错误", trigger: "change" }],
        url: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
        icon: [],
        orderNum: [],
        radio: [{ required: true, message: '菜单类型不能为空', trigger: 'change' }],
      },
      expand: true,
    };
  },
  created() {
    this.getData();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      menuList()
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.tableData = res.data;
            this.data = res.data;
            // console.log(this.data);
            setTimeout(() => {
              this.listLoading.close();
            }, 200);
          }
        })
        .catch((error) => {
          console.log(error);
          this.listLoading.close();
        });
    },
    selectTree() {
      this.showTree = true;
      this.filterText = '';
    },
    hideTree() {
      this.showTree = false;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      // console.log(data);
      this.editForm.pId = data.id;
      this.editForm.pName = data.name;
      this.showTree = false;
      // console.log(this.editForm.pId);
    },

    //刷新
    handleRefresh() {
      this.getData();
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },

    //菜单修改按钮
    handleUpdate(row) {
      // console.log(row);
      this.dialogEditVisible = true;
      this.editForm.id = row.id;
      this.editForm.name = row.name;
      this.editForm.pId = row.pid;
      this.editForm.url = row.url;
      this.editForm.icon = row.icon;
      this.editForm.orderNum = row.orderNum;
      this.editForm.menuType = row.menuType;
      this.editForm.pName = this.pNameFilter(row.pid);
    },
    pNameFilter(pId) {
      // console.log(pId);
      let pName;
      // console.log(this.data);
      // console.log( this.lookForAllId(this.data));
      let arrIds = this.lookForAllId(this.data).arrIds;
      let treeNames = this.lookForAllId(this.data).treeNames;
      for (let i = 0; i < arrIds.length; i++) {
        if (pId == arrIds[i]) {
          pName = treeNames[i];
          return pName;
        }
      }
    },
    lookForAllId(data = [], arrIds = [], treeNames = []) {
      for (let item of data) {
        arrIds.push(item.id);
        treeNames.push(item.name);
        if (item.children && item.children.length)
          this.lookForAllId(item.children, arrIds, treeNames);
      }

      return { arrIds, treeNames };
    },
    // 菜单修改
    handleSaveEdit(editForm) {
      this.$refs[editForm].validate((valid) => {
        if (valid) {
          // console.log(this.editForm);
          editorUpdateMenu(this.editForm)
            .then((res) => {
              // console.log(res);
              if (res.code == 1) {
                this.dialogEditVisible = false;
                this.getData();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                });
              }
            })
            .catch((error) => {
              this.dialogEditVisible = false;
              console.log(error);
            });
        } else {
          this.$message.error({
            message: '修改失败',
          });
          return false;
        }
      });
    },

    //菜单添加按钮
    handleAddBtn() {
      this.$router.push({
        path: `/system/menu/create`,
        query: {},
      });
    },

    //菜单删除
    handleDel(row) {
      // console.log(row);
      let id = row.id;
      this.$confirm('确认要删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            deleteMenuById(id)
              .then((res) => {
                // console.log(res);
                if (res.code == 1) {
                  this.getData();
                  this.$message({
                    message: '刪除成功',
                    type: 'success',
                  });
                }
              })
              .catch((error) => {
                this.$message.error({
                  message: '删除失败',
                });
              });
          }
        })
        .catch((e) => { });
    },
    // 退出清空数据
    quit(form) {
      this.form.name = '';
      this.form.pid = '';
      this.form.url = '';
      this.form.icon = '';
      this.form.orderNum = '';
      this.radio = 1;
    },
    forArr(arr, isExpand) {
      arr.forEach((i) => {
        this.$refs.theTable.toggleRowExpansion(i, isExpand);
        if (i.children) {
          this.forArr(i.children, isExpand);
        }
      });
    },
    //全部展开
    openAll() {
      this.forArr(this.tableData, true);
    },
    //全部折叠
    foldAll() {
      this.forArr(this.tableData, false);
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 204px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
  }
}

.el-select::v-deep {
  width: 300px;
}
.treedata-box {
  width: 100%;
  height: 200px;

  box-sizing: border-box;
  border: 1px solid #dae0e6;
  border-radius: 2px;
  position: absolute;
  top: 36px;
  left: 0px;
  z-index: 888;
  background: #fff;
  .el-scrollbar {
    height: 100%;
    .treedata-content {
      padding: 15px;
    }
    .el-input {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}
</style>
