import request from '@/utils/request'
//监控中心
//告警信息---资产信息预览
export function monitoringDetails() {
  // debugger
  return request({
    url: '/api/snmp/monitoringDetails',
    method: 'post',
    
  })
}
//告警信息--告警列表
export function warningLog() {
  // debugger
  return request({
    url: '/api/snmp/warningLog',
    method: 'post',
    
  })
}
//设备信息--设备列表
export function deviceList(data) {
  return request({
    url: '/api/snmp/snmpList',
    method: 'post',
    params:data
    
  })
}
//设备信息--添加-设备检测

export function detect(data) {
  return request({
    url: '/api/snmp/detect',
    method: 'post',
    params:data
    
  })
}
//设备信息--添加/修改
export function modifySnmpDevice(data) {
  return request({
    url: '/api/snmp/modifySnmpDevice',
    method: 'post',
    params:data
    
  })
}
//设备信息--删除
export function deleteSnmpDevice(data) {
  return request({
    url: '/api/snmp/deleteSnmpDevice',
    method: 'post',
    params:data
    
  })
}
//设备信息--阈值修改
export function modifyTheHostRate(data) {
  return request({
    url: '/api/snmp/modifyTheHostRate',
    method: 'post',
    params:data
    
  })
}
//设备信息--id 获取设备信息
export function getDeviceInfo(data) {
  return request({
    url: '/api/snmp/inquireSnmpInfo',
    method: 'post',
    params:data
    
  })
}
//设备信息--下载模板
export function exportTemplate() {
  return request({
    url: '/api/snmp/exportTemplate',
    method: 'get',
    responseType: 'blob',
    
   
  })
}
//设备信息--导入
export function deviceImport(file) {
  return request({
    url: '/api/snmp/import',
    method: 'post',
    headers: {
      "Content-Type": "multipart/form-data",
    },
    params:file
  })
}
//设备信息--导出
export function deviceExport() {
  return request({
    url: '/api/snmp/export',
    method: 'get',
    responseType: 'blob',
  })
}
//告警信息--id 获取历史告警
export function warningLogList(data) {
  return request({
    url: '/api/snmp/warningLogById',
    method: 'post',
    params:data
    
  })
}



//系统设置--全局告警设置
export function waringList(data) {
  return request({
    url: '/api/snmpWarning/waringList',
    method: 'post',
    params:data
    
  })
}
//系统设置--全局告警设置修改
export function updateThreshold(data) {
  return request({
    url: '/api/snmpWarning/updateThreshold',
    method: 'post',
    params:data
    
  })
}