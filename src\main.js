// import "core-js";
// import 'regenerator-runtime/runtime';//浏览器兼容性IE
require('babel-polyfill') ;//es6 //浏览器兼容性IE
require('es6-promise').polyfill(); //IE 不支持 promise，所有需要单独引入 polyfill
import Vue from "vue";
import "normalize.css/normalize.css"; // A modern alternative to CSS resets

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
//import locale from 'element-ui/lib/locale/lang/en' // lang i18n 引入英文
import locale from "element-ui/lib/locale/lang/zh-CN"; // lang i18n 引入中文

import "@/styles/index.scss"; // global css

import App from "./App";
import store from "./store";
import router from "./router";
import qs from "qs";
import request from "@/utils/request";
import "@/icons"; // icon
import "@/permission"; // permission control
import ElTableBar from "el-table-bar-base";
import "el-table-bar-base/lib/ElTableBar.css";
// import "default-passive-events"; //添加事件管理者'passive'，来阻止'touchstart'事件，让页面更加流畅。 解决chrome下的warning问题
import moment from "moment"; //时间格式转化
import md5 from "js-md5"; //md5加密方式
let Base64 = require("js-base64").Base64; //base64加密方式
var echarts = require("echarts"); //echarts
import 'echarts-liquidfill' ;//水球
Vue.prototype.$echarts = echarts;
Vue.prototype.$md5 = md5;
Vue.prototype.$moment = "moment";
Vue.filter("dateformat", function(dataStr, pattern = "YYYY-MM-DD ") {
  return moment(dataStr).format(pattern);
});

Vue.use(ElTableBar);
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';
import '../node_modules/echarts/map/js/world'; // 引入世界地图
import { preventReClick } from './utils/preventReClick';




 
Vue.use(preventReClick);

Vue.use(VueAwesomeSwiper);
/* 延迟按钮点击指令 */
Vue.directive("dbClick", {
  inserted(el, binding) {
    // console.log(el);
    el.addEventListener("click", e => {
      if (!el.disabled) {
        el.disabled = true;
        el.style.cursor = "not-allowed";
        el.style.backgroundColor = "#eee";
        el.style.color = "#333";
        setTimeout(() => {
          el.style.cursor = "pointer";
          el.disabled = false;
        }, 3000);
      }
    });
  }
});


/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === "production") {
};

// set ElementUI lang to EN
//Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI);

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  render: h => h(App)
});
