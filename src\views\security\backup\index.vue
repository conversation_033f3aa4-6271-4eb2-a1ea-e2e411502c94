<template>
  <div class="box">
    <div class="mainWrapper">
      <div
        class="wrap"
        :style="{
          transform: `scale(${scalseNum},${scalseNum}) translateX(-50%)`
        }"
      >
        <div class="head" :style="{ backgroundImage: 'url(' + headBg + ')' }">
          <span class="title">{{ screenTitle }} </span>
          <div class="datebox">
            <img :src="screenImg" alt="" style="margin-right: 10px" />
            <!-- 
             -->
            <div class="time">{{ nowTime }}</div>
            <div class="content">
              <p class="week">{{ nowWeek }}</p>
              <p class="date">{{ nowDate }}</p>
            </div>
          </div>
        </div>

        <div class="mainbox">
          <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <div
                class="grid-content grid-content-md2"
                v-if="versionSreen == 1"
              >
                <div class="all-title">
                  <span class="name">安全雷达</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="attack-box">
                  <div
                    id="radar"
                    :style="{
                      width: '100%',
                      height: '380px',
                      backgroundImage:
                        'url(' +
                        require('@/assets/security/radaar-bg.png') +
                        ')'
                    }"
                  ></div>
                </div>
              </div>
              <div
                class="grid-content grid-content-lg"
                v-if="versionSreen == 2"
              >
                <div class="all-title">
                  <span class="name">外部威胁</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="attack-box">
                  <img src="@/assets/security/out_alarm.png" alt />
                  <div class="attack-hd clearfix">
                    <div class="attack-title">
                      <p>今日</p>
                      <p>总攻击源数量</p>
                    </div>
                    <div class="attack-number">
                      <ul>
                        <li
                          v-for="(num, index) in attackList"
                          :key="index"
                          class="number"
                        >
                          {{ num }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="attack-source">
                  <h3 class="title">攻击源 TOP5</h3>
                  <ul>
                    <li
                      class="attack-source-item"
                      v-for="(attack, index) in attackSourceList"
                      :key="index"
                    >
                      <span class="ranking">{{ index + 1 }}</span>
                      <span class="ip">{{ attack.source.ip }}</span>
                      <!-- <span class="city">无</span>  -->
                      <span class="line"></span>
                      <span class="number">{{ attack.attackCount }}</span>
                    </li>
                  </ul>
                  <ul>
                    <li
                      class="attack-source-item"
                      v-for="(item, index) in attackSourceNullList"
                      :key="index"
                    >
                      <span class="ranking">{{ item }}</span>
                      <span class="ip">无</span>
                      <!-- <span class="city">无</span> -->
                      <span class="line"></span>
                      <span class="number">-</span>
                    </li>
                  </ul>
                </div>
                <div class="attack-source">
                  <h3 class="title">攻击类型 TOP5</h3>

                  <div
                    id="attackTypes_out"
                    :style="{ width: '100%', height: '190px' }"
                    v-if="outThreatTypeList.length > 0"
                  ></div>
                  <div class="no-attack-box" v-else>
                    <img src="@/assets/security/noData.png" alt />
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>

              <div
                class="grid-content"
                :class="
                  versionSreen == 1 ? 'grid-content-lg2' : 'grid-content-md'
                "
              >
                <div class="all-title">
                  <span class="name">流量统计</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="attack-box">
                  <div
                    id="bandWidth"
                    :style="{
                      width: '100%',
                      height: versionSreen == 1 ? '480px' : '310px'
                    }"
                    v-if="totalBwShow == true"
                  ></div>
                  <div
                    class="no-attack-box"
                    v-else
                    :style="{
                      width: '100%',
                      height: versionSreen == 1 ? '480px' : '310px'
                    }"
                  >
                    <img src="@/assets/security/noData.png" alt />
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <div class="grid-content grid-content-lg" style="padding: 0 20px">
                <div class="nav-box">
                  <img src="@/assets/security/nav.png" alt />
                </div>
                <div class="dot1-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot2-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot3-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot4-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot5-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot6-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="nav2-box">
                  <img src="@/assets/security/nav2.png" alt />
                </div>
                <div class="admin-box">
                  <img src="@/assets/security/admin.png" alt />
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="管理员"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/admin_icon.png"
                      alt
                      class="icon hoverTip"
                    />
                  </el-tooltip>
                  <img
                    src="@/assets/security/admin_tit.png"
                    alt
                    class="title"
                  />
                </div>

                <div class="safety-box">
                  <img src="@/assets/security/safety-bg.png" alt />
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云下一代防火墙"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/firewall.png"
                      alt
                      class="firewall-icon hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云WAF"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/waf.png"
                      alt
                      class="waf-icon hoverTip"
                    />
                  </el-tooltip>
                  <img
                    src="@/assets/security/safety-tit.png"
                    alt
                    class="title"
                  />
                </div>
                <div class="product-box">
                  <img src="@/assets/security/product-bg.png" alt />
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="SSL VPN"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/product-1.png"
                      alt
                      class="vpn-icon hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机安全"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/product-2.png"
                      alt
                      class="host-icon hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云数据库审计"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/product-3.png"
                      alt
                      class="database-icon hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云日志审计"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/product-4.png"
                      alt
                      class="log-icon hoverTip"
                    />
                  </el-tooltip>
                </div>
                <div class="host-box">
                  <img src="@/assets/security/host-bg.png" alt />

                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon1 hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon2 hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon3 hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon4 hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon5 hoverTip"
                    />
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云主机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/host-icon.png"
                      alt
                      class="host-icon6 hoverTip"
                    />
                  </el-tooltip>
                  <img src="@/assets/security/host-tit.png" alt class="title" />
                </div>
                <div class="fortress-box">
                  <img src="@/assets/security/fortress-bg.png" alt />
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="云堡垒机"
                    placement="top"
                  >
                    <img
                      src="@/assets/security/fortress-icon.png"
                      alt
                      class="icon hoverTip"
                    />
                  </el-tooltip>
                </div>
                <div class="vpc-box">
                  <img src="@/assets/security/vpc.png" alt />
                </div>
              </div>
              <div class="grid-content grid-content-md" style="padding: 0 20px">
                <div class="all-title">
                  <span class="name">攻击日志</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div>
                  <el-table
                    :data="tableAttack"
                    style="width: 100%"
                    tooltip-effect="light"
                    ref="rw_table"
                    @mouseenter.native="mouseEnter"
                    @mouseleave.native="mouseLeave"
                    :row-class-name="tableRowClassName"
                  >
                    <el-table-column label="严重性" width="80">
                      <template slot-scope="scope">
                        <div>
                          <span
                            class="bg-level"
                            :class="
                              scope.row[7] == '严重' ? 'bg-warn' : 'bg-normal'
                            "
                            >{{ scope.row[7] }}</span
                          >
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="1"
                      label="	虚拟机/终端"
                    ></el-table-column>

                    <el-table-column
                      prop="0"
                      label="攻击时间"
                      :formatter="dateFormat"
                      show-overflow-tooltip
                    ></el-table-column>

                    <el-table-column
                      prop="8"
                      label="源区域"
                      width="130"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="9"
                      label="目的区域"
                      width="120"
                      show-overflow-tooltip
                    ></el-table-column>

                    <el-table-column
                      prop="5"
                      label="攻击类型"
                      show-overflow-tooltip
                    >
                    </el-table-column>
                    <div slot="empty" style="line-height: normal">
                      <p>
                        <img src="@/assets/security/noData.png" alt />
                      </p>

                      <span
                        style="
                          display: inline-block;
                          color: #52c4ff;
                          margin-top: 5px;
                        "
                        >暂无数据</span
                      >
                    </div>
                  </el-table>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <div class="grid-content">
                <div class="all-title">
                  <span class="name">内部负载</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <!-- <div class="tab-city">
                  <div
                    class="module-list-wrap"
                    ref="modulewrap"
                    @mouseover="mouseOver"
                    @mouseleave="mouseLeave"
                  >
                    <i
                      name="xiangzuojiantou"
                      class="btn-icon prev el-icon-arrow-left"
                      @click="leftmove"
                      v-show="btnShow"
                    ></i>

                    <ul
                      class="module-list clearfix"
                      ref="modulelist"
                      :style="{
                        width: widthnum + 'px',
                        'margin-left': ulNum + 'px'
                      }"
                    >
                      <li
                        v-for="(item, index) in itemlist"
                        :key="index"
                        :class="{ active: num == index }"
                        @click="getNum(index)"
                      >
                        {{ item }}
                      </li>
                    </ul>

                    <i
                      name="xiangyoujiantou"
                      class="btn-icon next el-icon-arrow-right"
                      @click="rightmove"
                      v-show="btnShow"
                    ></i>
                  </div>
                </div>-->
              </div>
              <div>
                <div class="grid-content grid-content-ms">
                  <div class="title">
                    云主机流量 TOP5
                    <div class="sort-nav">
                      <span class="upstream"><i class="dot"></i> 上行流量</span>
                      <span class="downstream"
                        ><i class="dot"></i> 下行流量</span
                      >
                    </div>
                  </div>
                  <div class="grid-body">
                    <ul class="grid-list" v-if="userFlowVisible == true">
                      <li v-for="(item, index) in userStreamsData" :key="index">
                        <div class="rank">{{ index + 1 }}</div>
                        <div class="name">{{ item.user }}</div>
                        <div class="streams-box">
                          <span
                            class="down"
                            v-if="item.user != '无'"
                            :style="{
                              width:
                                (Number(
                                  item.upStream / item.totalStream
                                ).toFixed(4) *
                                  10000) /
                                  100 +
                                '%'
                            }"
                          ></span>
                          <span
                            class="up"
                            v-if="item.user != '无'"
                            :style="{
                              width:
                                (Number(
                                  item.downStream / item.totalStream
                                ).toFixed(4) *
                                  10000) /
                                  100 +
                                '%'
                            }"
                          ></span>
                          <span class="text" v-if="item.user != '无'"
                            >上 {{ item.upStream | diskSize }} / 下
                            {{ item.downStream | diskSize }}</span
                          >
                        </div>
                      </li>
                    </ul>

                    <!-- <div
                      id="userFlow"
                      :style="{ width: '100%', height: '200px' }"
                      v-if="userFlowVisible == true"
                    ></div>-->
                    <div
                      v-else
                      class="no-attack-box"
                      :style="{ width: '100%', height: '200px' }"
                    >
                      <img src="@/assets/security/noData.png" alt="" />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
                <div class="grid-content grid-content-ms">
                  <div class="title">应用流量 TOP5</div>
                  <div class="grid-body">
                    <ul class="grid-list" v-if="appFlowVisible == true">
                      <li v-for="(item, index) in appStreamsData" :key="index">
                        <div class="rank">{{ index + 1 }}</div>
                        <div class="name">{{ item.appName }}</div>
                        <div class="streams-box">
                          <span
                            class="up"
                            v-if="item.appName != '无' && index == 0"
                            :style="{ width: '80%' }"
                          ></span>
                          <span
                            class="up"
                            v-if="item.appName != '无' && index == 1"
                            :style="{ width: '70%' }"
                          ></span>
                          <span
                            class="up"
                            v-if="item.appName != '无' && index == 2"
                            :style="{ width: '60%' }"
                          ></span>
                          <span
                            class="up"
                            v-if="item.appName != '无' && index == 3"
                            :style="{ width: '50%' }"
                          ></span>
                          <span
                            class="up"
                            v-if="item.appName != '无' && index == 4"
                            :style="{ width: '30%' }"
                          ></span>

                          <span class="text" v-if="item.appName != '无'"
                            >{{ item.totalStream | diskSize }}
                          </span>
                          <span class="text" v-else>无 </span>
                        </div>
                      </li>
                    </ul>
                    <!-- <div
                      id="applicationFlow"
                      :style="{ width: '100%', height: '200px' }"
                      v-if="appFlowVisible == true"
                    ></div>-->
                    <div
                      v-else
                      class="no-attack-box"
                      :style="{ width: '100%', height: '200px' }"
                    >
                      <img src="@/assets/security/noData.png" alt="" />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
                <div class="grid-content grid-content-ms grid-content-ms2">
                  <div class="title clearfix">云主机并发连接 TOP5</div>
                  <div class="grid-body">
                    <!-- <div
                      id="userBf"
                      :style="{ width: '100%', height: '200px' }"
                      v-if="userBfVisible == true"
                    ></div> -->
                    <bar-vertical
                      v-if="userBfVisible == true"
                      id="userBf"
                      height="200px"
                      :chart-data="userBfChartData"
                    />
                    <div
                      v-else
                      class="no-attack-box"
                      :style="{ width: '100%', height: '200px' }"
                    >
                      <img src="@/assets/security/noData.png" alt="" />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
                <div class="grid-content grid-content-ms grid-content-ms2">
                  <div class="title">应用并发连接 TOP5</div>
                  <div class="grid-body">
                    <!-- <div
                      id="applicationBf"
                      :style="{ width: '100%', height: '200px' }"
                      v-if="appBfVisible == true"
                    ></div> -->
                    <bar-vertical
                      v-if="appBfVisible == true"
                      id="applicationBf"
                      height="200px"
                      :chart-data="applicationChartData"
                    />
                    <div
                      v-else
                      class="no-attack-box"
                      :style="{ width: '100%', height: '200px' }"
                    >
                      <img src="@/assets/security/noData.png" alt="" />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div id="prompt" ref="prompt" class>
      为获得最佳体验效果，浏览器最小窗口宽度需大于1300px
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import headBg2 from "@/assets/security/head_bg.png";
import BarVertical from '@/components/BarVertical'
import {
  //  wafAttackip,
  // wafProtect,
  threatRanking,
  attackLog,
  loadCpu,
  loadRam,
  getDisk,
  getNetworkCard,
  getNetWorkOut,
  getDiskReadAndOut,
  radar,
  bandWidth,
} from "@/api/security.js";
import { device, appRankingTopTen, userRankingTopTen } from "@/api/flow.js";
import moment from "moment";
import { getExpireDate } from "@/api/user.js";
import json from "body-parser/lib/types/json";

let rolltimer = ""; // 自动滚动的定时任务
export default {
  name: "Dashboard",
  components: {
    BarVertical
  },
  data() {
    return {
      rollTime: 5,
      rollPx: 1,
      screenTitle: "", //大屏标题
      screenImg: "", //大屏图片
      scalseNum: 1,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight, // 屏幕尺寸高度
      marginTop: 0,
      timer: null,
      timerZ: null,
      num: 0,
      leftDis: 9,
      headBg: headBg2,
      nowDate: "", // 当前日期
      nowTime: "", // 当前时间
      nowWeek: "", // 当前星期
      bodyBgImage: "",
      internalThreatTotal: 0, //内部威胁总量
      internalThreatData: [], //内部威胁数组数值
      internalThreatNames: [], //内部威胁数组数值
      internalThreatNums: [], //内部威胁数组数值
      internalattackList: [],
      outThreatTotal: 0, //外部威胁总量
      attackList: [], //外部威胁总量
      mapBox: require("@/assets/security/lbx.png"),
      itemlist: [
        "上海数据中心",
        "吉林-长春数据中心",
        "杭州数据中心",
        "宁波数据中心",
        "合肥数据中心",
        "南京数据中心",
      ],
      widthnum: "100%",
      defartIndex: 0,
      ulNum: 0,
      btnShow: false,
      attackSourceList: [], //外部威胁攻击源ip列表
      outThreatTypeList: [], //外部威胁数组
      outThreatNames: [], //外部威胁数组数值
      outThreatNums: [], //外部威胁数组数值
      tableAttack: [],
      tabContents: [1, 2, 3, 4, 5],
      shelterList: [],
      attackSourceNullList: [],
      echartsArr: [],
      uid: [],
      uidDisk: [],
      radarData: [], //雷达数据
      radarNames: [],
      radarNums: [],
      bandWidthData: [], //流量统计
      flowTotal: [], //总流量
      flowtTime: [], //流量时间
      flowOut: [], //出站流量
      flowInner: [], //入站流量
      versionSreen: "",
      totalTimes: [], //总流量时间数组
      totalBws: [], //总流量,
      totalBwShow: false,
      appNames: [], //应用名称数组
      apptotalStreams: [], //应用流量
      userNames: [], //用户名称数组
      userUpStreams: [], //用户上行流量
      userDownStreams: [], //用户下行流量
      userTotalStreams: [], //用户总流量
      appBfNames: [], //应用并发名称数组
      apptotalSessions: [], //应用并发
      userBfNames: [], //用户并发名称数组
      userSessions: [], //用户并发
      appFlowVisible: false, //应用流量显隐
      userFlowVisible: false, //用户流量显隐
      userBfVisible: false, //用户并发显隐
      appBfVisible: false, //应用并发显隐
      userStreamsData: [],//用户流量数组
      appStreamsData: [],//应用流量数组
      userBfChartData: {
        xAxisData: [],
        yAxisData: []
      },
      applicationChartData: {
        xAxisData: [],
        yAxisData: []
      }

    };
  },
  computed: {
    ...mapGetters(["name", "userid", "version", "usertype", "tenantid"]),
  },
  filters: {
    numFilter(value) {
      // 截取当前数据到小数点后两位--四舍五入
      let realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
    //容量转换
    diskSize(num) {
      if (num == 0) return "0 B";
      var k = 1024; //设定基础容量大小
      var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  ��以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },
  },
  created() {
    this.screenTitle = localStorage.getItem("screenTitle"); //大屏标题
    this.screenImg = localStorage.getItem("screenImg"); //大屏图片
    this.versionSreen = this.version;
    // this.versionSreen=1;
    console.log(this.versionSreen);
  },
  methods: {
    //切换密码服务态势
    // changeScreen(){
    //  this.$router.push({path:`/security/password/index`})
    // },
    dateFormat(row, column) {
      var moment = require("moment");
      var date = row[column.property];
      return moment(date).format("YYYY-MM-DD hh:mm:ss");
    },
    //容量转换
    diskSize(num) {
      if (num == 0) return "0 B";
      var k = 1024; //设定基础容量大小
      var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  所以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },

    getList() {
      let that = this;
      if (this.versionSreen == 2) {
        //外部威胁--攻击源ip
        let ipData = {
          page: 1,
          limit: 5,
          date: "month",
          userId: that.userid,
          groupBy: "sourceIp",
        };
        threatRanking(ipData)
          .then((res) => {
            console.log(res);

            if (res.code == 1) {
              that.attackSourceList = res.data.result;
              //console.log(that.attackSourceList);
              that.outThreatTotal = 0;
              that.attackSourceNullList = [];
              if (that.attackSourceList == undefined) {
                //console.log(difference);
                for (let k = 1; k <= 5; k++) {
                  that.attackSourceNullList.push(k);
                  // console.log(that.attackSourceNullList);
                }
                that.attackList = [0];
              } else {
                if (that.attackSourceList.length < 5) {
                  let difference = 5 - that.attackSourceList.length;
                  //console.log(difference);
                  for (let k = 1; k <= difference; k++) {
                    that.attackSourceNullList.push(that.attackSourceList.length + k);
                  }
                  // console.log(that.attackSourceNullList);
                }
                for (var i = 0; i < that.attackSourceList.length; i++) {
                  that.outThreatTotal += that.attackSourceList[i].attackCount;
                }

                that.attackList = String(that.outThreatTotal).split("");
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
        //外部威胁--攻击类型
        let typeData = {
          page: 1,
          limit: 5,
          date: "month",
          userId: that.userid,
          groupBy: "name",
        };

        threatRanking(typeData)
          .then((res) => {
            console.log(res);
            if (res.code == 1) {
              if (JSON.stringify(res.data) == "{}") {
              } else {
                that.outThreatTypeList = res.data.result;
                // console.log(that.outThreatTypeList);
                //attackSourceNullList
                that.outThreatNames = [];
                that.outThreatNums = [];
                for (let i = 0; i < that.outThreatTypeList.length; i++) {
                  that.outThreatNums.push(that.outThreatTypeList[i].attackCount);
                  that.outThreatNames.push(that.outThreatTypeList[i].name);
                }
                if (that.outThreatTypeList.length < 5) {
                  let difference = 5 - that.outThreatTypeList.length;

                  for (let k = 1; k <= difference; k++) {
                    that.outThreatNums.push(0); //内部威胁数组数值
                    that.outThreatNames.push("无"); //内部威胁数组数值
                  }
                }
                // console.log(this.outThreatNums);
                // console.log(this.outThreatNames);
                that.$nextTick(() => {
                  that.initChartsOutAttck();
                });
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
      } else if (this.versionSreen == 1) {
        //安全雷达
        let radarD = {
          page: "",
          limit: "",
          userId: that.userid,
          date: "",
        };
        //
        radar(radarD)
          .then((res) => {
            // console.log(res);
            if (res.code == 1) {
              that.radarNames = [];
              that.radarNums = [];
              that.radarData = res.data;

              for (var i = 0; i < that.radarData.length; i++) {
                that.radarNames.push(that.radarData[i][1]);
                that.radarNums.push(that.radarData[i][2]);
                // console.log(that.radarNums);

                // console.log( that.radarNames);
              }
              that.$nextTick(function () {
                that.initRadar();
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }

      //流量统计
      // let bandWidthD = {
      //   userId: that.userid,
      // };
      // bandWidth(bandWidthD)
      //   .then((res) => {
      //     // console.log(res);
      //     // console.log(111);
      //     that.flowTotal = []; //总流量
      //     that.flowtTime = []; //流量时间
      //     that.flowOut = []; //出站流量
      //     that.flowInner = []; //入站流量
      //     if (res.code == 1) {
      //       that.bandWidthData = res.data;
      //       if (that.bandWidthData.length > 0) {
      //         for (let i = 0; i < that.bandWidthData.length; i++) {
      //           that.flowTotal.push(that.bandWidthData[i][3]); //总流量
      //           that.flowtTime.push(that.bandWidthData[i][0] * 1000); //流量时间
      //           that.flowOut.push(that.bandWidthData[i][2]); //出站流量
      //           that.flowInner.push(that.bandWidthData[i][1]); //入站流量
      //         }
      //        // console.log(that.flowtTime);
      //        // console.log(that.flowInner);
      //         that.$nextTick(() => {
      //           that.initChartsBand();
      //         });
      //       }
      //     }
      //   })
      //   .catch((error) => {
      //     console.log(error);
      //   });
      let totalData = {
        page: "",
        limit: "",
        date: "month",
        serverAddress: "",
        userId: this.userid,
      };
      console.log(totalData);
      device(totalData)
        .then((res) => {
          console.log(res);
          console.log("流量统计");
          this.totalTimes = []; //总流量时间数组
          this.totalBws = []; //总流量
          if (res.code == 1) {
            if (JSON.stringify(res.data) != "{}" && res.data.result.length > 0) {
              this.totalBwShow = true;
              res.data.result.forEach((item) => {
                this.totalTimes.push(moment(item.time).format("YYYY-MM-DD HH:mm")); //总流量时间数组
                this.totalBws.push(item.totalBw); //总流量
              });
              that.$nextTick(function () {
                that.initTotalFlow();
              });
              // console.log('总流量')
              // console.log(this.totalTimes);
            }

          } else {
            this.totalBwShow = false;

          }
        })
        .catch((error) => {
          console.log(error);
        });

      //攻击日志
      let logData = {
        page: 1,
        limit: 20,
        userId: that.userid,
        date: "",
      };
      attackLog(logData)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            clearInterval(this.timer);
            that.tableAttack = res.data.results;
            // console.log( that.tableAttack );
            // console.log('攻击日志');
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getZstack() {
      let that = this;
      //云主机用户流量
      //用户top5接口
      let userData = {
        page: "",
        limit: "",
        date: "month",
        serverAddress: "",
        userId: this.userid,
        orderBy: "totalStream",
      };
      // console.log(userData)
      userRankingTopTen(userData)
        .then((res) => {
          console.log('用户流量');
          console.log(res);
          // this.userNames = [];
          // this.userTotalStreams = []
          // this.userUpStreams = [];
          // this.userDownStreams = [];
          this.userStreamsData = [];
          if (res.code == 1) {
            if (JSON.stringify(res.data) != "{}" && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                // this.userNames.push(item.user);
                // this.userTotalStreams.push(item.totalStream);
                // this.userUpStreams.push(item.upStream);
                // this.userDownStreams.push(item.downStream);
                let obj = {
                  user: item.user,
                  upStream: item.upStream,
                  downStream: item.downStream,
                  totalStream: item.totalStream
                }
                this.userStreamsData.push(obj);
              });

              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  // this.userNames.push('无');
                  // this.userTotalStreams.push(0);
                  // this.userUpStreams.push(0);
                  // this.userDownStreams.push(0);
                  let obj = {
                    user: "无",
                    upStream: 0,
                    downStream: 0,
                    totalStream: 0
                  }
                  this.userStreamsData.push(obj);
                }
              }
              console.log('数据')
              console.log(this.userStreamsData)
              this.userFlowVisible = true;
              // that.$nextTick(function () {
              //   that.initUserFlow();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });

      //应用流量top5
      let appData = {
        page: "",
        limit: "",
        date: "month",
        userId: this.userid,
        orderBy: "totalStream",
      };
      // console.log(appData)
      appRankingTopTen(appData)
        .then((res) => {
          console.log(res);
          // this.appNames = []; //应用名称数组
          // this.apptotalStreams = []; //应用流量
          this.appStreamsData = [];
          if (res.code == 1) {
            if (JSON.stringify(res.data) != "{}" && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                // this.appNames.push(item.appName);
                // this.apptotalStreams.push(item.totalStream);
                let obj = {
                  appName: item.appName,
                  totalStream: item.totalStream
                }
                this.appStreamsData.push(obj);
              });

              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  // this.appNames.push('无');
                  // this.appStreamsData.push(0);
                  let obj = {
                    appName: '无',
                    totalStream: 0
                  }
                  this.appStreamsData.push(obj);
                }
              }
              console.log('应用流量')
              console.log(this.appStreamsData)
              this.appFlowVisible = true;
              // that.$nextTick(function () {
              //   that.initApplyFlow();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
      // 用户并发连接top5
      let userBfData = {
        page: "",
        limit: "",
        date: "month",
        serverAddress: "",
        userId: this.userid,
        orderBy: "sessions",
      };

      userRankingTopTen(userBfData)
        .then((res) => {
          console.log("用户并发连接");
          console.log(res);
          this.userBfNames = [];
          this.userSessions = [];
          if (res.code == 1) {
            if (JSON.stringify(res.data) != "{}" && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                this.userBfNames.push(item.user);
                this.userSessions.push(item.sessions);
              });
              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  this.userBfNames.push('无');
                  this.userSessions.push(0);
                }
              }
              this.userBfVisible = true;
              this.userBfChartData.xAxisData = this.userBfNames;
              this.userBfChartData.yAxisData = this.userSessions;
              // that.$nextTick(function () {
              //   that.initUserBf();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });

      //应用并发连接top5
      let appBfData = {
        page: "",
        limit: "",
        date: "month",
        userId: this.userid,
        orderBy: "sessions",
      };

      appRankingTopTen(appBfData)
        .then((res) => {
          console.log("应用并发");
          console.log(res);
          this.appBfNames = []; //应用名称数组
          this.apptotalSessions = []; //应用并发
          if (res.code == 1) {
            if (JSON.stringify(res.data) != "{}" && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                this.appBfNames.push(item.appName);
                this.apptotalSessions.push(item.sessions);
              });
              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  this.appBfNames.push('无');
                  this.apptotalSessions.push(0);
                }
              }
              this.appBfVisible = true;
              this.applicationChartData.xAxisData = this.appBfNames;
              this.applicationChartData.yAxisData = this.apptotalSessions;
              // that.$nextTick(function () {
              //   that.initApplyBf();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },

    autoRoll(stop) {
      if (stop) {
        clearInterval(rolltimer);
        return;
      }
      // 拿到表格挂载后的真实DOM
      const table = this.$refs.rw_table;
      // 拿到表格中承载数据的div元素
      const divData = table.bodyWrapper;
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果
      rolltimer = setInterval(() => {
        // 元素自增距离顶部像素
        divData.scrollTop += this.rollPx;
        // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
        if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
          // 重置table距离顶部距离
          divData.scrollTop = 0;
        }
      }, this.rollTime * 10);
    },
    // 鼠标进入
    mouseEnter(time) {
      // 鼠标进入停止滚动和切换的定时任务
      this.autoRoll(true);
    },
    // 鼠标离开
    mouseLeave() {
      // 开启
      this.autoRoll();
    },

    currentTime() {
      setInterval(this.getDate, 500);
    },
    // timerList() {
    //   setInterval(this.getList, 600000);
    // },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
    //授权校验
    getExpire() {
      getExpireDate()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            //验证状态 1为已过期，强制退出登录，0为未过期
            if (res.data.status == 1) {
              this.$message.error("授权已过期");
              this.logout();
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    timerZstack() {
      // console.log(this.timerZ);
      if (this.timerZ != null) {
        return;
      }
      this.timerZ = setInterval(() => {
        setTimeout(this.getZstack, 0);
        setTimeout(this.getList, 0);
        setTimeout(this.getExpire, 0); //定时校验
      }, 300000);
    },
    getDate() {
      var _this = this;
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let week = new Date().getDay();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? "0" + new Date().getMinutes()
          : new Date().getMinutes();
      let ms =
        new Date().getSeconds() < 10
          ? "0" + new Date().getSeconds()
          : new Date().getSeconds();
      if (week == 1) {
        this.nowWeek = "周一";
      } else if (week == 2) {
        this.nowWeek = "周二";
      } else if (week == 3) {
        this.nowWeek = "周三";
      } else if (week == 4) {
        this.nowWeek = "周四";
      } else if (week == 5) {
        this.nowWeek = "周五";
      } else if (week == 6) {
        this.nowWeek = "周六";
      } else {
        this.nowWeek = "周日";
      }
      _this.nowTime = hh + ":" + mf + ":" + ms;
      _this.nowDate = yy + "年" + mm + "月" + dd + "日";
    },
    // 添加body图片
    setBodyBackGround() {
      document.body.style.backgroundSize = "100%";
      document.body.style.backgroundImage = this.bodyBgImage;
    },
    // 清除背景图
    clearBodyBackGround() {
      document.body.style.backgroundImage = "";
    },
    //外部威胁
    initChartsOutAttck() {
      this.chart_attackTypes_out = this.$echarts.init(
        document.getElementById("attackTypes_out")
      );

      this.echartsArr.push(this.chart_attackTypes_out);

      this.setOptionsOutAttck();
    },
    //雷达
    initRadar() {
      this.chart_radar = this.$echarts.init(document.getElementById("radar"));

      this.echartsArr.push(this.chart_radar);

      this.setOptionsRadar();
    },
    //流量
    initTotalFlow() {
      this.chart_total = this.$echarts.init(document.getElementById("bandWidth"));

      this.echartsArr.push(this.chart_total);

      this.setOptionsTotal();
    },
    //用户流量top5
    // initUserFlow() {
    //   this.chart_user = this.$echarts.init(document.getElementById("userFlow"));

    //   this.echartsArr.push(this.chart_user);

    //   this.setOptionsUser();
    // },
    //应用流量top5
    // initApplyFlow() {
    //   this.chart_apply = this.$echarts.init(document.getElementById("applicationFlow"));

    //   this.echartsArr.push(this.chart_apply);

    //   this.setOptionsApply();
    // },
    // initChartsBand() {
    //   this.chart_bandWidth = this.$echarts.init(
    //     document.getElementById("bandWidth")
    //   );

    //   this.echartsArr.push(this.chart_bandWidth);

    //   this.setOptionsBandWidth();
    // },

    //总流量
    //总流量
    setOptionsTotal() {
      let that = this;
      this.chart_total.setOption({
        color: "#1389E1",
        grid: {
          top: "30px",
          left: "30px",
          right: "20px",
          bottom: "20px",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            // console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.axisValueLabel).format("YYYY/MM/DD HH:mm") +
                "</br>" +
                item.marker +
                " " +
                item.seriesName +
                " : " +
                that.diskSize(item.data) +
                "bps" +
                "</br>";
            });
            return result;
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.totalTimes,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            showMaxLabel: true,
            color: "#97A4B6",
            fontSize: 16,
            align: "center",
            interval: 6,
            formatter: function (value, index) {
              // console.log(value);
              let time;

              time = moment(value).format("MM/DD ");

              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
            lineStyle: {
              color: "#003660",
            },
          },

          axisTick: {
            show: true,
            lineStyle: {
              color: "#D9D9D9",
            },
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#97A4B6",
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = that.diskSize(value);
              return capacity;
            },
          },
          smooth: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: "solid", //设置网格线类型 dotted：虚线   solid:实线
              color: "#003660",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.totalBws,
            symbol: "none",
            type: "line",
            name: "总流量",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(117, 177, 255, 1)" },

                  { offset: 1, color: " rgba(229, 244, 255, 1)" },
                ]),
              },
            },
          },
        ],
      });
    },

    //外部威胁类型
    setOptionsOutAttck() {
      this.chart_attackTypes_out.setOption({
        grid: {
          top: "20px",
          left: "0",
          right: "8%",
          bottom: "20px",
          containLabel: true,
        },
        tooltip: {},
        xAxis: {
          type: "category",
          data: this.outThreatNames,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#fff",
            interval: 0,
            rotate: -45,
            formatter: function (value) {
              if (value.length > 8) {
                return `${value.slice(0, 6)}...`;
              }
              return value;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "solid", //设置网格线类型 dotted：虚线   solid:实线
              color: "#003660",
            },
          }, //去除网格线
          nameTextStyle: {
            color: "#003660",
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.outThreatNums,
            barWidth: 16, //柱图宽度
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "#005EA4", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#0091FF", // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            type: "bar",
            showBackground: false,
            label: {
              show: true,
              position: "top",
              color: "#fff",
            },
            backgroundStyle: {
              color: "rgba(220, 220, 220, 0.8)",
            },
          },
        ],
      });
    },
    setOptionsRadar() {
      let that = this;
      //雷达数据
      this.chart_radar.setOption({
        tooltip: {
          trigger: "item",
          padding: 10,
        },
        radar: [
          {
            indicator: (function () {
              var res = [];
              for (var i = 0; i < that.radarNames.length; i++) {
                res.push({
                  name: that.radarNames[i],
                  max: 100,
                  color: "#DEF3FF",
                });
              }
              return res;
            })(),
            center: ["50%", "50%"],
            radius: 150,
            startAngle: 90,
            shape: "circle",
            triggerEvent: true,
            name: {
              fontSize: 14,
            },

            axisLine: {
              lineStyle: {
                color: "#4BB7FF",
                width: 2,
              },
            },
            splitLine: {
              lineStyle: {
                color: "#0091FF",
                width: 2,
              },
            },

            splitArea: {
              show: true,
              areaStyle: {
                color: [
                  "rgba(0, 34, 255, 0.6)",
                  "rgba(0, 34, 255, 0.5)",
                  "rgba(0, 34, 255, 0.4)",
                  "rgba(0, 34, 255, 0.32)",
                  "rgba(0, 34, 255, 0.2)",
                ],
              },
            },
          },
        ],
        series: [
          {
            type: "radar",
            emphasis: {
              lineStyle: {
                color: "#00E5FF",
                width: 3,
              },
              areaStyle: {
                color: {
                  type: "radial",
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0,
                      color: " rgba(57, 57, 255, 0)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#00E5FF", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            data: [
              {
                name: "雷达",
                value: that.radarNums,
                symbol: "none",
                lineStyle: {
                  color: "#00E5FF",
                  width: 4,
                },
                itemStyle: {
                  borderColor: "#00E5FF",
                  borderWidth: 0,
                },
                areaStyle: {
                  color: {
                    type: "radial",
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                      {
                        offset: 0,
                        color: " rgba(57, 57, 255, 0)", // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "#00E5FF", // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  },
                },
              },
            ],
          },
        ],
      });
    },
    //流量数据
    setOptionsBandWidth() {
      let that = this;
      this.chart_bandWidth.setOption({
        color: ["#35AA47", "#005DFF", "#E5DE62"],
        grid: {
          top: "20px",
          left: "0",
          right: "20px",
          bottom: "20px",
          containLabel: true,
        },
        legend: {
          bottom: 0,
          textStyle: {
            color: "#DEF3FF",
          },
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var result = "";
            // console.log(params);
            result +=
              moment(Number(params[0].axisValueLabel)).format("YYYY-MM-DD HH:mm:ss") +
              "</br>";
            params.forEach(function (item) {
              // console.log(item);
              result +=
                item.marker +
                " " +
                item.seriesName +
                " : " +
                that.diskSize(item.value) +
                "/s" +
                "</br>";
            });
            return result;
          },
        },

        xAxis: {
          type: "category",
          data: that.flowtTime,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: "#CBEDFF",
            fontSize: 14,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(Number(value)).format("MM-DD");
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: true,

            lineStyle: {
              color: "#003660", // x轴坐标轴颜色
            },
          },

          axisTick: {
            show: true,

            lineStyle: {
              color: "#DEF3FF",
            },
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "#CBEDFF",
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = that.diskSize(value);
              return capacity + "/s";
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "solid", //设置网格线类型 dotted：虚线   solid:实线
              color: "#003660",
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: that.flowInner,
            symbol: "none",
            name: "入站流量",
            type: "line",

            symbol: "circle",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(37, 161, 255, 0.07)" },

                  { offset: 1, color: " rgba(37, 161, 255, 0)" },
                ]),
              },
            },
          },
          {
            data: that.flowOut,
            symbol: "none",
            name: "出站流量",
            symbol: "circle",
            type: "line",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(0, 153, 255, 0.97)" },

                  { offset: 1, color: " rgba(0, 93, 255, 0.29)" },
                ]),
              },
            },
          },
          {
            data: that.flowTotal,
            symbol: "none",
            name: "总流量",
            type: "line",
            symbol: "circle",
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: " rgba(255, 239, 151, 0.19 )" },

                  { offset: 1, color: " rgba(255, 239, 151, 0.2)" },
                ]),
              },
            },
          },
        ],
      });
    },



    //容量转换
    diskSize(num) {
      if (num == 0) return "0 B";
      var k = 1024; //设定基础容量大小
      var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  ��以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },
    //攻击日志
    tableRowClassName({ row, rowIndex }) {
      if (row.severity === 4) {
        return "warning-row";
      } else if (row.severity === 3) {
        return "danger-row";
      } else if (row.severity === 2) {
        return "warm-row";
      } else if (row.severity === 1) {
        return "normal-row";
      }
      return "";
    },
    // getloupanInfo() {
    //   const params = {
    //     l_id: this.lid
    //   };
    //   loupanInfo(params).then(res => {
    //     this.infobj = res.data;
    //     this.likeList = this.infobj.liked;
    //     //...
    //     this.huxingW();
    //   });
    // },
    // huxingW() {
    //   this.$nextTick(() => {
    //     let liSunW = 0;
    //     for (var i = 0; i < this.itemlist.length; i++) {
    //       // console.log(this.itemlist.length);
    //       let liw = this.$refs.modulelist.children[i].offsetWidth;
    //       // console.log(liw)
    //       liSunW += liw;
    //     }
    //     //  let liw = this.$refs.modulelist.children[0].offsetWidth;
    //     let len = this.itemlist.length;
    //     this.widthnum = liSunW + 10 * len;
    //     //  console.log(this.widthnum);
    //   });
    // },
    mouseOver() {
      if (this.$refs.modulewrap.offsetWidth < this.widthnum) {
        this.btnShow = true;
      } else {
        this.btnShow = false;
      }
    },
    mouseLeave() {
      this.btnShow = false;
    },
    leftmove() {
      if (this.defartIndex < 1) {
        this.$message({
          message: "已经是第一条数据哦！",
        });
      } else if (this.defartIndex <= this.itemlist.length - 1) {
        this.defartIndex--;
        this.ulNum = this.ulNum + 90;
      } else {
        this.defartIndex--;
        this.ulNum = this.ulNum + 90;
      }
    },
    rightmove() {
      if (this.defartIndex >= this.itemlist.length - 3) {
        this.$message({
          message: "没有更多了",
        });
      } else if (this.defartIndex < this.itemlist.length - 1) {
        this.defartIndex++;
        this.ulNum = this.ulNum - 90;

        // $(".tablist").animate({left:fixLeft},300);
        // //顶部添加样式
        // $(".tab .tabItem").eq(defartIndex).addClass("on").siblings().removeClass("on");
        // //底部添加样式
        // $(".tabContent .tbox").eq(defartIndex).addClass("active").siblings().removeClass("active");
      } else {
        this.defartIndex++;
        this.ulNum = this.ulNum - 150;
        //往左移动距离
      }
    },
    getNum(index) {
      this.num = index;
    },
    diver() {
      // let bodyW =this.$refs.lineBody[0].offsetWidth;
      let bodyW = 228;
      //onsole.log(bodyW);
      let diverLen = Math.ceil(bodyW / 6);
      //console.log(diverLen);
      for (let i = 0; i < diverLen; i++) {
        this.shelterList.push(i);
      }
      //console.log(this.shelterList);
    },
    resizeWin() {
      window.screenWidth = document.body.clientWidth;
      window.screenHeight = document.body.clientHeight;
      this.screenWidth = window.screenWidth;
      this.screenHeight = window.screenHeight;
      // console.log( this.screenWidth);
      //  console.log( this.screenHeight);
      let scalW = this.screenWidth / 1920;
      let scalH = this.screenHeight / 1080;
      // console.log(  this.scalseNum);
      if (this.screenWidth < 1300) {
        this.$refs.prompt.setAttribute("class", "active");
      } else {
        this.$refs.prompt.removeAttribute("class", "active");
      }

      if (scalW >= scalH) {
        this.scalseNum = scalH;
      } else {
        this.scalseNum = scalW;
      }
      //console.log(  this.scalseNum);
    },
  },
  mounted() {
    // 进来的时候调用添加
    this.getExpire();
    this.getList();
    this.getZstack();

    this.setBodyBackGround();
    this.currentTime();
    // this.timerList();
    this.timerZstack();
    // this.huxingW();
    this.resizeWin();
    const that = this;
    window.onresize = () => {
      return (() => {
        that.echartsArr.forEach((item) => {
          item.resize();
        });
        that.resizeWin();
      })();
    };
  },
  beforeDestroy() {
    // 离开页面的时候清除
    this.clearBodyBackGround();
    clearInterval(this.timer);
    this.timer = null;
    if (this.getDate) {
      //console.log("销毁定时器");
      clearInterval(this.getDate); // 在Vue实例销毁前，清除时间定时器
    }
    clearInterval(this.timerZ);
    this.timerZ == null;
  },
};
</script>

<style lang="scss" scoped>
.box {
  height: 100%;
}
.mainWrapper {
  position: relative;
  flex: 1 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: linear-gradient(180deg, #030e43 0%, #00213a 100%);
  .wrap {
    flex: 1 1;
    position: relative;
    left: 50%;
    transform-origin: left top 0;
    width: 1920px;
  }
  .head {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    .title {
      font-weight: normal;
      margin-left: 60px;
      font-size: 40px;
      color: #fff;
      white-space: pre;
      letter-spacing: 1.74px;

      text-shadow: 0 0 6px #0089f3;
      /* .switch-btn{
        display: inline-block;
        width: 28px;
        height: 28px;
        margin-left: 20px;
        cursor: pointer;
      } */
    }
    .datebox {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      border-radius: 2px;
      margin-right: 60px;
      .time {
        font-family: Roboto, PingFang SC, Noto Sans CJK, Microsoft YaHei;
        cursor: pointer;
        font-size: 48px;
        letter-spacing: 0;
        height: 48px;
        margin-right: 28px;
        color: rgba(82, 196, 255, 1);
      }
      .content {
        font-size: 16px;
        font-family: PingFangSC-Semibold;

        color: rgba(82, 196, 255, 1);
        .week {
          line-height: 24px;
        }
        .date {
          letter-spacing: 1px;

          line-height: 24px;
        }
      }
    }
  }
  .mainbox {
    margin: 20px 60px 0;
    .grid-content {
      // padding-bottom: 20px;
      position: relative;
      z-index: 10;
      box-sizing: border-box;
      .no-attack-box {
        display: flex;

        align-items: center;
        justify-content: center;
        color: #52c4ff;
        flex-direction: column;
        p {
          margin-top: 10px;
        }
      }
      .title {
        font-size: 16px;
        font-weight: 400;
        color: #52c4ff;
        line-height: 22px;
        margin-bottom: 15px;
      }
      .all-title {
        width: 100%;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: left;
        margin-bottom: 20px;
        .name {
          font-family: PingFangSC-Semibold;
          font-weight: bold;
          font-size: 24px;
          color: #0091ff;
          letter-spacing: 0;
          text-shadow: 0 0 6px rgba(0, 145, 255, 0.3);
        }
        .icon {
          margin-left: 8px;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: space-between;
          i {
            display: inline-block;
            margin: 0 3px;
            width: 8px;
            height: 12px;
            background-color: #0091ff;
            &:first-child {
              animation: icon_animation 1.2s linear 0s infinite;
            }
            &:nth-child(2) {
              animation: icon_animation 1.2s linear 0.4s infinite;
            }
            &:last-child {
              margin: 0 0 0 3px;
              background-color: #52c4ff;
            }
            &:nth-child(3) {
              animation: icon_animation 1.2s linear 0.8s infinite;
            }
          }
        }
        .line {
          height: 2px;
          flex-grow: 1;
          background: linear-gradient(90deg, #52c4ff 0, #0091ff 30%);
        }
      }
      .attack-box {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        margin-bottom: 20px;
        img {
          float: left;
          margin-right: 20px;
        }
        .attack-hd {
          flex: 1;
          height: 54px;
          padding: 3px 0;
          box-sizing: border-box;
          border-top: 1px solid #52c4ff;
          border-bottom: 1px solid #52c4ff;
          .attack-title {
            float: left;
            color: #52c4ff;
            font-size: 14px;
            p {
              line-height: 22px;
            }
          }
          .attack-number {
            float: right;
            .number {
              width: 40px;
              height: 46px;
              float: left;
              background: #005bd4;
              text-align: center;
              line-height: 46px;
              color: #fff;
              font-family: DINAlternate-Bold;
              font-size: 40px;
              margin-left: 5px;
              position: relative;
              &::before {
                clear: both;
                content: '';
                position: absolute;
                width: 5px;
                height: 2px;
                background: rgba(0, 42, 74, 0.5);
                left: 0;
                top: 50%;
                transform: translateY(-50%);
              }
              &::after {
                clear: both;
                content: '';
                position: absolute;
                width: 5px;
                height: 2px;
                background: rgba(0, 42, 74, 0.5);
                right: 0;
                top: 50%;
                transform: translateY(-50%);
              }
            }
          }
        }
      }
      .attack-source {
        margin-bottom: 20px;
        .no-attack-box {
          height: 190px;
        }
        .attack-source-nullitem {
          margin-top: 10px;
          .null {
            display: inline-block;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            color: #999;
          }
        }

        .attack-source-item {
          position: relative;
          display: flex;
          margin-top: 10px;
          box-sizing: border-box;
          color: #cbedff;
          .ranking {
            display: inline-block;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            background: rgba(0, 54, 96, 1);
            color: #52c4ff;
            margin-right: 20px;
          }
          .ip {
            flex: 1;
            height: 24px;
            line-height: 24px;
          }
          .city {
            display: inline-block;
            width: 80px;
            height: 24px;

            line-height: 24px;
          }
          .line {
            display: inline-block;
            // width: 112px;
            width: 160px;
            height: 1px;
            margin-top: 10px;
            margin-right: 20px;
            background: #003660;
          }
          .number {
            display: inline-block;
            width: 80px;
            height: 24px;
            line-height: 24px;
            text-align: right;
            background: #0042a4;
            color: #fff;
            box-sizing: border-box;
            padding: 0 5px;
          }
        }
      }
      .module-list-wrap {
        width: 100%;
        height: 28px;
        overflow: hidden;
        position: relative;
        color: #fff;
        margin-bottom: 25px;
        font-family: PingFangSC-Medium;
        .btn-icon {
          display: inline-block;
          width: 16px;
          height: 28px;
          text-align: center;
          background: rgba(0, 33, 58, 0.6);
          position: absolute;
          top: 0;
          line-height: 28px;
          cursor: pointer;
        }
        .prev {
          left: 0;
        }
        .next {
          right: 0;
        }
        .module-list {
          height: 28px;
          white-space: nowrap;
          li {
            float: left;
            padding: 0 6px;
            height: 28px;
            line-height: 28px;
            background: rgba(0, 94, 164, 0.6);
            border: 1px solid rgba(82, 196, 255, 0.6);
            margin-right: 5px;
            color: #999;

            &.active {
              background: rgba(0, 94, 164, 1);
              border: 1px solid rgba(82, 196, 255, 1);
              color: #fff;
            }
          }
        }
      }
      .grid-body {
        width: 100%;
        .grid-item {
          width: 100%;
          display: flex;
          .name {
            display: inline-block;
            width: 120px;
            font-size: 14px;
            color: #e0f3ff;
            letter-spacing: 0;
            line-height: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 20px;
            white-space: nowrap;
            margin-bottom: 15px;
          }
          .line-ratio {
            flex: 1;
            //background: #f00;
            .line-container2 {
              width: 240px;
              position: relative;
              margin-bottom: 15px;
              height: 16px;
              display: inline-block;
              overflow: hidden;
              .line-body {
                width: calc(100% - 10px);
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                background: #052e4e;
                border-radius: 5px;
              }
              .line-value {
                position: absolute;
                top: 0;
                left: 0;
                height: 6px;
                transition: all 0.5s;
                border-radius: 5px;
              }
            }
            .line-container {
              width: 240px;
              border: 1px solid #1b3b5f;
              position: relative;
              margin-bottom: 15px;
              height: 16px;
              display: inline-block;
              overflow: hidden;
              .line-body {
                width: calc(100% - 10px);
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                background: rgba(0, 54, 96, 1);
              }
              .line-value {
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                transition: all 0.5s;
              }
              .line-shelter {
                position: absolute;
                top: 0;
                background-color: #0d1c37;
                width: 2px;
                height: 6px;
                top: 4px;
                left: 9px;
              }
            }
            .value {
              font-weight: 700;
              font-size: 14px;
              color: #ffb412;
              letter-spacing: 0;
              text-align: right;
              line-height: 12px;
              float: right;
              position: relative;
              top: 2px;
            }
          }
        }
      }
    }
    .grid-content-md2 {
      height: 440px;
    }
    .grid-content-lg2 {
      height: 540px;
    }
    .grid-content-lg {
      height: 610px;
      //  background: #f00;
      .admin-box {
        position: absolute;
        top: 22px;
        left: 226px;
        .title {
          position: absolute;
          top: 32px;
          left: 42px;
        }
        .icon {
          position: absolute;
          top: -6px;
          left: 62px;
        }
      }
      .dot1-box {
        position: absolute;
        top: 132px;
        left: 116px;
        width: 180px;

        border: 1px dashed #00518d;
        transform: rotate(-30deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling linear infinite normal;
        }
      }
      .dot2-box {
        position: absolute;
        top: 232px;
        left: 152px;
        width: 58px;

        border: 1px dashed #00518d;
        transform: rotate(30deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling2 linear infinite normal;
        }
      }
      .dot3-box {
        position: absolute;
        top: 246px;
        left: 128px;
        width: 58px;

        border: 1px dashed #00518d;
        transform: rotate(-150deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling2 linear infinite normal;
        }
      }
      .dot4-box {
        position: absolute;
        top: 296px;
        left: 682px;
        width: 76px;
        border: 1px dashed #00518d;
        transform: rotate(32deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling3 linear infinite normal;
        }
      }
      .dot5-box {
        position: absolute;
        top: 316px;
        left: 648px;
        width: 76px;
        border: 1px dashed #00518d;
        transform: rotate(-150deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling3 linear infinite normal;
        }
      }
      .dot6-box {
        position: absolute;
        top: 260px;
        left: 388px;
        width: 116px;
        // border: 1px dashed #00518d;
        transform: rotate(-210deg);
        .dot {
          position: absolute;
          top: -8px;
          left: -8px;
          transform: rotate(30deg);
          animation: 1.2s rolling4 linear infinite normal;
        }
      }

      .safety-box {
        position: absolute;
        top: 92px;
        left: 356px;
        .title {
          position: absolute;
          top: 92px;
          left: 98px;
        }
        .firewall-icon {
          position: absolute;
          top: 26px;
          left: 92px;
        }
        .waf-icon {
          position: absolute;
          top: 98px;
          left: 202px;
        }
      }
      .product-box {
        position: absolute;
        top: 306px;
        left: 382px;
        .vpn-icon {
          position: absolute;
          top: 156px;
          left: 86px;
        }
        .host-icon {
          position: absolute;
          top: 106px;
          left: 166px;
        }
        .database-icon {
          position: absolute;
          top: 54px;
          left: 260px;
        }
        .log-icon {
          position: absolute;
          top: 4px;
          left: 346px;
        }
      }
      .host-box {
        position: absolute;
        top: 218px;
        left: 68px;
        .title {
          position: absolute;
          top: 152px;
          left: 110px;
        }
        .host-icon1 {
          position: absolute;
          top: 22px;
          left: 152px;
        }
        .host-icon2 {
          position: absolute;
          top: 69px;
          left: 230px;
        }
        .host-icon3 {
          position: absolute;
          top: 116px;
          left: 310px;
        }
        .host-icon4 {
          position: absolute;
          top: 70px;
          left: 70px;
        }
        .host-icon5 {
          position: absolute;
          top: 116px;
          left: 152px;
        }
        .host-icon6 {
          position: absolute;
          top: 162px;
          left: 232px;
        }
      }
      .fortress-box {
        position: absolute;
        top: 152px;
        left: 20px;
        .icon {
          position: absolute;
          top: -12px;
          left: 42px;
        }
      }
      .vpc-box {
        position: absolute;
        top: 192px;
        left: 192px;
      }
      .nav-box {
        position: absolute;
        top: 232px;
        left: 396px;
      }
      .nav2-box {
        position: absolute;
        top: 242px;
        left: 426px;
      }
    }
    .grid-content-md {
      height: 370px;

      // background: #0f0;
    }
    .grid-content-ms {
      height: 220px;
      box-sizing: border-box;
      // background: #00f;
      .sort-nav {
        float: right;
        display: flex;
        align-content: center;

        span {
          display: inline-block;
          margin-left: 5px;
          font-size: 12px;
          // opacity: 0.6;
          &.active {
            opacity: 1;
          }
          .dot {
            display: inline-block;
            width: 10px;
            height: 10px;
          }
        }
        .upstream {
          color: #ffb412;

          .dot {
            background: #cf9915;
          }
        }
        .downstream {
          color: #52c4ff;
          .dot {
            background: #0085f1;
          }
        }
        .read {
          color: #52c4ff;
          .dot {
          }
        }
        .write {
          // color: #8a65d4;
          color: #0091ff;
          .dot {
            // background: #8a65d4;
            background: #0091ff;
          }
        }
      }
      .speed-item {
        display: flex;
        &:last-child {
          .name,
          .speed-container {
            margin-bottom: 0;
          }
        }
        .name {
          margin-bottom: 15px;
        }
        .line-speed {
          flex: 1;
        }
        .speed-container {
          width: 100%;
          position: relative;
          margin-bottom: 15px;
          height: 18px;
          display: block;
          overflow: hidden;
          background: #00213a;
          .read-value,
          .write-value {
            height: 18px;
            display: inline-block;
            font-size: 12px;
            color: #fff;
            position: relative;
            white-space: nowrap;
          }
          .speed-shelter {
            position: absolute;
            top: 0;
            background-color: #0d1c37;
            width: 2px;
            height: 18px;
          }
          .read-value {
            float: left;
            text-align: left;
            background: rgba(82, 196, 255, 0.5);
          }
          .write-value {
            float: right;
            text-align: right;
            background: rgba(138, 101, 212, 0.5);
          }
          .speed {
            position: absolute;
            top: 0;
            font-size: 12px;
            color: #fff;
            line-height: 18px;
          }
          .readSpeed {
            left: 3px;
          }
          .writeSpeed {
            right: 3px;
          }
        }
      }
    }
    .grid-content-ms2 {
      height: 247px;
      box-sizing: border-box;
      // background: #0ff;
    }

    .alarm-box {
      margin-top: 20px;
      padding: 0 10px;
    }
  }
  .grid-list {
    li {
      display: flex;
      align-items: center;
      color: #cbedff;
      margin-bottom: 10px;
      .rank {
        display: inline-block;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        background: #003660;
        color: #52c4ff;
      }
      .name {
        display: inline-block;
        width: 120px;
        margin: 0 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .streams-box {
        flex: 1;
        background-color: rgba(0, 66, 164, 0.2);
        height: 24px;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
        span {
          display: inline-block;
          height: 100%;
        }
        .up {
          background-color: rgba(0, 66, 164, 0.7);
        }
        .down {
          background-color: rgba(207, 153, 21, 1);
        }
        .text {
          line-height: 24px;
          position: absolute;
          top: 50%;
          left: 20px;
          transform: translateY(-50%);
        }
      }
    }
  }
}
</style>
<style scoped>
#radar {
  background-repeat: no-repeat;
  background-position: center;
}
.el-table {
  overflow: hidden;
  background: none;
  border-spacing: 0;
}
.el-table::v-deep .el-table__body-wrapper {
  height: 268px;
  overflow: hidden;
}
.el-table::v-deep th,
.el-table::v-deep tr {
  background: none;
  color: #fff;
  font-size: 14px;
}
.el-table::v-deep td {
  padding: 4px 0;
}
.el-table::v-deep th {
  background-color: none !important;
  padding: 5px 0;
}

.el-table::v-deep td,
.el-table::v-deep th.is-leaf {
  border-bottom: none;
}
.el-table::v-deep tbody tr:hover > td {
  background: none !important;
}

.el-table::v-deep tbody tr:hover {
  background: none !important;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}
.el-table::v-deep::before {
  height: 0;
}
.el-table::v-deep td .cell {
  height: 36px;
  line-height: 36px;
}
.el-table::v-deep tr td:first-child .cell {
  background: none !important;
}
.el-table::v-deep .warning-row td .cell {
  background: rgba(236, 89, 96, 0.2);
}

.el-table::v-deep .warm-row td .cell {
  background: rgba(242, 174, 27, 0.2);
}
.el-table::v-deep .normal-row td .cell {
  background: rgba(0, 94, 164, 0.2);
}
.el-table::v-deep .danger-row td .cell {
  background: rgba(188, 78, 115, 0.2);
}
.el-table::v-deep .el-table_1_column_1 .cell {
  background: none !important;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 36px;
  text-align: center;
  line-height: 36px;
}
.bg-warm {
  background: rgba(242, 174, 27, 1);
}
.bg-warn {
  background: rgba(236, 89, 96, 1);
}
.bg-normal {
  background: rgba(0, 94, 164, 1);
}
.bg-danger {
  background: rgba(188, 78, 115, 1);
}
.el-table::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 0;
}
</style>
