<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">设备信息</h3>
      </div>
      <div class="serch-box clearfix">
        <div class="filter-container">
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            @click="handleRefresh()"
          >
            <svg-icon icon-class="refresh" />
          </el-button>
          <el-button
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            @click="addOrUpdateHandle()"
            >添加</el-button
          >

          <div class="search-container">
            <el-input
              v-model="listQuery.title"
              placeholder="主机名称"
              style="width: 200px"
              class="filter-item"
              v-on:input="search"
            />
            <span
              class="el-icon-search search-btn"
              @click="handleSearch()"
            ></span>
          </div>
          <el-button
            v-waves
            class="filter-item filter-item2"
            icon="el-icon-download"
            @click="uploadHandle()"
            >导入</el-button
          >
          <el-button
            v-waves
            class="filter-item"
            icon="el-icon-upload2"
            @click="downloadDevices()"
            >导出</el-button
          >
          <el-button
            v-waves
            class="filter-item"
            icon=""
            @click="downloadTemplate()"
            >下载模板</el-button
          >
        </div>
        <div class="page-box">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="sizes, prev,slot, next,total"
            :total="total"
          >
            <span class="pageNum">
              {{ this.listQuery.page }}
              <i class="divider">/</i>
              {{ totalPage }}
            </span>
          </el-pagination>
        </div>
      </div>
      <div class="table-box">
        <el-table-bar>
          <el-table
            :data="tableData"
            style="width: 100%; margin-bottom: 20px"
            ref="table"
            row-key="id"
            highlight-current-row
            v-loading="listLoading"
          >
            <el-table-column
              prop="host_name"
              label="名称"
              sortable
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="address"
              label="主机地址"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="CPU利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.cpu_rate != null ||
                    scope.row.cpu_rate != undefined
                  "
                  >{{
                    scope.row.cpu_rate != 0
                      ? scope.row.cpu_rate + '%'
                      : scope.row.cpu_rate
                  }}</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.cpuWaringType == 1"
                />
              </template>
            </el-table-column>

            <el-table-column label="内存利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.memory_usage != null ||
                    scope.row.memory_usage != undefined
                  "
                  >{{
                    scope.row.memory_usage != 0
                      ? scope.row.memory_usage + '%'
                      : scope.row.memory_usage
                  }}</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.memoryWaringType == 1"
                />
              </template>
            </el-table-column>
            <el-table-column label="磁盘利用率" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.disk_usage != null ||
                    scope.row.disk_usage != undefined
                  "
                  >{{
                    scope.row.disk_usage != 0
                      ? scope.row.disk_usage + '%'
                      : scope.row.disk_usage
                  }}</span
                >
                <span v-else>-</span>
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                  v-if="scope.row.diskWaringType == 1"
                />
              </template>
            </el-table-column>
            <el-table-column label="告警状态" sortable prop="warn_type">
              <template slot-scope="scope">
                <span v-if="scope.row.warn_type == 1">健康</span>
                <span v-else-if="scope.row.warn_type == 2">警告</span>
                <span v-else-if="scope.row.warn_type == 3">严重</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="存活状态" sortable prop="online_type">
              <template slot-scope="scope">
                <span class="color-red" v-if="scope.row.online_type == 0"
                  >离线</span
                >
                <span class="color-green" v-else-if="scope.row.online_type == 1"
                  >在线</span
                >
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="create_time"
              label="创建时间"
              sortable
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="操作" align="center" width="80">
              <template slot-scope="scope">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <div
                      class="opt"
                      @click="addOrUpdateHandle(scope.row.host_id)"
                    >
                      修改
                    </div>
                    <div class="opt" @click="handleDel(scope.row)">删除</div>
                    <div class="opt" @click="modifyRate(scope.row.host_id)">
                      设置阈值
                    </div>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-table-bar>
      </div>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getData"
    />
    <!-- 弹窗，修改阈值 -->
    <modify-rate
      v-if="modifyRateVisible"
      ref="modifyRate"
      @refreshDataList="getData"
    />
    <el-dialog
      title="设备信息导入"
      :visible.sync="dialogVisibleExport"
      width="420px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- <upload-excel-component
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
      />
      <el-table
        :data="importTableData"
        border
        highlight-current-row
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column
          v-for="item of importTableHeader"
          :key="item"
          :prop="item"
          :label="item"
        />
      </el-table> -->
      <div class="file-box" style="min-height: 320px">
        <el-upload
          class="upload-demo"
          drag
          ref="upload"
          :action="uploadUrl"
          :file-list="fileList"
          :before-upload="handleFileBefore"
          :limit="1"
          accept=".xlsx, .xls"
          :on-progress="handleProgress"
          :on-error="handleError"
          :on-change="handleChange"
          :on-exceed="handleExceed"
          :auto-upload="false"
          :on-success="handleSuccess"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            仅支持上传.xlsx、.xls后缀文件
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleExport = false">取 消</el-button>
        <el-button type="primary" @click="submitUploadList">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination';
import AddOrUpdate from '../components/device-add-update.vue';
import modifyRate from '../components/modify-hostRate.vue';
import UploadExcelComponent from '@/components/UploadExcel/index.vue';
import {
  deviceList,
  deleteSnmpDevice,
  exportTemplate,
  deviceImport,
  deviceExport,
} from '@/api/modules/monitor';
export default {
  components: {
    Pagination,
    AddOrUpdate,
    modifyRate,
    UploadExcelComponent,
  },
  directives: { waves },
  data() {
    return {
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
      },
      currentPage: 1,
      totalPage: 1,
      dialogFormVisible: false,
      formLabelWidth: '120px',
      addOrUpdateVisible: false,
      modifyRateVisible: false,
      dialogVisibleExport: false,
      importTableData: [],
      importTableHeader: [],
      fileList: [],
      fileProgress: '',
      // 上传的地址
      uploadUrl: '/api/snmp/import',
      fileData: [],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = true;
      let data = {
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        keyWord: this.listQuery.title,
      };
      console.log(data);
      deviceList(data)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading = false;
            this.tableData = res.data.rows;
            this.total = res.data.total_rows;
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
            //setCurrentRow
            // if (this.$route.query.hostId) {
            //   this.tableData.forEach((item, index) => {
            //     if (item.host_id == this.$route.query.hostId) {
            //       this.$refs.table.setCurrentRow(this.tableData[index]);
            //     }
            //   });
            // }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleRefresh() {
      this.getData();
    },
    //下载模板
    downloadTemplate() {
      exportTemplate()
        .then((res) => {
          console.log(res);
          this.downloadUrl(res, '设备信息.xlsx');
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //导出所有设备信息
    downloadDevices() {
      deviceExport()
        .then((res) => {
          console.log(res);
          this.downloadUrl(res, '设备信息.xlsx');
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //下载
    downloadUrl(res, fileName) {
      let objectUrl = URL.createObjectURL(
        new Blob([res], { type: 'application/vnd.ms-excel' })
      );
      let link = document.createElement('a');
      link.style.display = 'none';
      // link.setAttribute('download', '资产信息模板.xlsx');
      // console.log(res.headers['content-disposition']);
      // const fileName = decodeURI(res.headers['content-disposition'].split('=')[1]);
      // let iconv = require('iconv-lite');
      // iconv.skipDecodeWaring = true;
      // let name = iconv.decode(fileName, 'utf-8');
      link.download = fileName;
      link.href = objectUrl;

      document.body.appendChild(link);
      link.click();

      //下载完成后
      document.body.removeChild(link);
      window.URL.revokeObjectURL(objectUrl);
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },

    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id;
        this.$refs.addOrUpdate.init();
      });
    },
    // 阈值设置
    modifyRate(id) {
      this.modifyRateVisible = true;
      this.$nextTick(() => {
        this.$refs.modifyRate.dataForm.id = id;
        this.$refs.modifyRate.init();
      });
    },
    //删除
    handleDel(row) {
      console.log(row);
      let data = {
        id: row.host_id,
      };
      this.$confirm(`确认要删除主机${row.host_name}吗？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            deleteSnmpDevice(data)
              .then((res) => {
                // console.log(res);
                if (res.code == 1) {
                  this.$message({
                    message: '刪除成功',
                    type: 'success',
                    onClose: () => { },
                  });
                  this.getData();
                }
              })
              .catch((error) => {
                this.$message.error({
                  message: '删除失败',
                });
              });
          }
        })
        .catch((e) => { });
    },
    handleClick(row) {
      // console.log(row);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
    // beforeUpload(file) {
    //   const isLt1M = file.size / 1024 / 1024 < 10;

    //   if (isLt1M) {
    //     return true;
    //   }

    //   this.$message({
    //     message: '请不要上传大于10M的文件',
    //     type: 'warning',
    //   });
    //   return false;
    // },
    // handleSuccess({ results, header }) {
    //   this.importTableData = results;
    //   this.importTableHeader = header;
    //   console.log(results, header);
    // },
    uploadHandle() {
      this.dialogVisibleExport = true;
    },
    handleProgress(event, file, fileList) {
      if (this.fileList.length > 0) {
        this.fileList.forEach((element, index) => {
          if (element.uid === file.uid) {
            // 更新这个uid下的进度
            const progress = Math.floor(event.percent);
            // 防止上传完接口还没有返回成功值，所以此处给定progress的最大值为99，成功的钩子中再置为100
            element.progress = progress === 100 ? 99 : progress;
            this.$set(this.fileList, index, element);
          }
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },

    //文件上传之前
    handleFileBefore(file) {
      // const isJPG = file.type === 'image/jpeg';
      // const isLt2M = file.size / 1024 / 1024 < 2;
      console.log(file);
      let fileName = file.name;
      let pos = fileName.lastIndexOf('.');
      let lastName = fileName.substring(pos, fileName.length);
      console.log(lastName.toLowerCase());
      if (lastName.toLowerCase() != '.xlsx' && lastName.toLowerCase() != '.xls') {
        this.$message.error('只能上传后缀是.xlsx、.xls的文件');
        // this.resetCompressData()
        return;
      }
      // 限制上传文件的大小
      // const isLt = file.size / 1024 / 5 >= 1 && file.size / 1024 / 1024 / 100 <= 1;
      // if (!isLt) {
      //   this.$message.error('上传文件大小不得小于5KB,不得大于100MB!');
      // }
      // return isLt;
    },

    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    handleError(err, file, fileList) {
      this.fileList.forEach((element, index) => {
        if (element.uid === file.uid) {
          this.fileList.splice(index, 1); // 上传失败删除该记录
          this.$message.error('文件上传失败');
        }
      });
    },

    beforeRemove(file, fileList) {
      this.$confirm(`确认要删除${file.name}吗？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            this.$message.success('删除成功');
          }
        })
        .catch((e) => { });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handleSuccess(response, file, fileList) {
      console.log(response);
      if (response.code == 1) {
        this.$message.success('文件导入成功');
        this.dialogVisibleExport = false;
        this.fileList = [];
      } else {
        this.$message.error('文件导入失败');
        this.fileList = [];
      }
      // this.fileList.forEach((element, index) => {
      //   if (element.uid === file.uid) {
      //     element.progress = 100;
      //     this.$set(this.fileList, index, element);
      //     this.dialogVisibleExport = false;
      //   }
      // });
    },
    submitUploadList() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 200px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
        &.filter-item2 {
          margin-left: 20px;
        }
      }
    }
    .color-red {
      color: #f95b6c;
    }
    .color-green {
      color: #00a700;
    }
    .alarm-tips-img {
      display: inline-block;
      margin-left: 3px;
      width: 18px;
      vertical-align: middle;
    }
  }
}
.upload-demo::v-deep {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .el-upload-list {
    width: 100%;
    text-align: left;
  }
}
</style>
