<template>
  <div class="mainWrapper">
    <div class v-if="showTenant">
      <div class="header">
        <h3 class="title">模块管理</h3>
      </div>

      <el-row :gutter="28">
        <el-col >
          <el-card shadow="always" >
            <div class="card-img" @click="add()">
              <!-- <img :src="item.name == name2 ? '@/assets/yunshujushengji.png':item.name == name2 ? '@/assets/anhFire.png' : item.name == name3 ? '@/assets/hostSafety.png' : item.name == name4 ? '@/assets/cloudFortress.png':'@/assets/webFire.png'" alt="" /> -->
              <img src="@/assets/unified/add.png" alt="" style="width: 44px;">
            </div>
            <p > 添加应用 </p>

          </el-card>
        </el-col>
        <el-col v-for="(item, index) in tableData" :key="item.key">
          <el-card shadow="always">
            <div class="card-img">
              <img :src="(item.name == '云数据审计' ? '@/assets/yunshujushengji.png' : item.name == '安恒防火墙' ? '@/assets/anhFire.png' : item.name == '云主机安全' ? '@/assets/hostSafety.png' : item.name == '云防火墙' ? '@/assets/cloudFortress.png' : '@/assets/webFire.png')" alt="" />
              <!-- <img :src="  item.name == '云数据审计' ? '@/assets/yunshujushengji.png':item.name == '安恒防火墙' ? '@/assets/anhFire.png' : item.name == '云主机安全' ? '@/assets/hostSafety.png' : item.name == '云防火墙' ? '@/assets/cloudFortress.png' : '@/assets/webFire.png' " alt="" /> -->
             
              <!-- <img src="@/assets/unified/yunshujushengji.png" alt=""> -->
            </div>
            <p> {{ item.product_name }} </p>

          </el-card>
        </el-col>
      </el-row>
      <AddDialog ref="addDialog"/>

    </div>
  </div>
</template>

<script>
import AddDialog from './add.vue'
import { productList, modifyProduct, isFirstLogin, deleteProduct } from '@/api/system.js';
import { Loading } from 'element-ui';

export default {
  components: { AddDialog },

  data() {
    return {
      showTenant: true,
      addDialogShow:true,
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
      },
    

      list: [
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
      ]

    };
  },
  created() {

  },
  computed: {

  },
  watch: {
    // 新窗口打开页面，解决浏览器拦截问题
    // jumpUrl() {
    //   if (this.jumpUrl) {
    //     window.open(this.jumpUrl, "_blank");
    //   }
    //   this.jumpUrl = null;
    // }
  },
  mounted() {
// this.add()
this.getData()
  },

  methods: {
    add() {
      console.log('add');
      this.$refs.addDialog.open()
    },
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      let data = {
        keyWord: this.listQuery.title,

        limit: this.listQuery.limit,
        page: this.listQuery.page,
      };
      // console.log(this.userid);
      //账号管理列表
      productList()
        .then((res) => {
          console.log(res);
          setTimeout(() => {
            this.listLoading.close();
          }, 200);
          this.tableData = res.data.rows;
          this.total = res.data.total_rows;
          // if (res.data.total_rows == 0) {
          //   this.totalPage = 1;
          // } else {
          //   this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          // }
        })
        .catch((error) => {
          this.listLoading.close();
        });
    },

  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}

.mainWrapper {
  .el-row {
    margin: 20px 10px;
    padding-left: 20px;

    .card-img {
      width: 98px;
      height: 98px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 70px;
      }

    }

    p {
      margin-bottom: 20px;
    }

  }
}
</style>
<style>
.el-col {
  width: 218px;
  height: 218px;
}

.el-card__body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.product-img:focus {
  outline: none !important;
}

.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow:after {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow {
  border-left-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow:after {
  border-left-color: #004578;
}

/* 控制主题颜色 */
.el-tooltip__popper.is-light {
  background: #0043b1 !important;
}
</style>
