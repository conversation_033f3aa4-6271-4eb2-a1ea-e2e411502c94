<template>
  <div>

      <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">租户管理</h3>
          </div>
          <div class="serch-box clearfix">
            <div class="filter-container">
              <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
                <svg-icon icon-class="refresh" />
              </el-button>
              <el-button
                v-waves
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="handleAddBtn()"
              >添加</el-button>
              <div class="search-container">
                <el-input
                  v-model="listQuery.title"
                  placeholder="租户名/账号"
                  style="width: 200px;"
                  class="filter-item"
                  v-on:input="search"
                />
                <span class="el-icon-search search-btn" @click="handleSearch()"></span>
              </div>
            </div>
            <div class="page-box">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="sizes, prev,slot, next,total"
                :total="total"
              >
                <span class="pageNum">
                  {{this.listQuery.page}}
                  <i class="divider">/</i>
                  {{totalPage}}
                </span>
              </el-pagination>
            </div>
          </div>
          <div class="table-box">
             <el-table-bar>
                <el-table :data="tableData" style="width: 100%;margin-bottom:20px;">
              <el-table-column prop="loginName" label="账号" sortable></el-table-column>
              <el-table-column prop="tenant" label="租户" sortable></el-table-column>
              <el-table-column prop="userName" label="联系人"></el-table-column>
              <el-table-column prop="mobile" label="联系方式"></el-table-column>
              <el-table-column label="创建时间" sortable>
                <template>{{tableData.createTime | dateformat('YYYY-MM-DD')}}</template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <i class="el-icon-more" style="transform:rotate(90deg)"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <div @click="handleEditClick(scope.row)" class="opt">修改</div>

                      <div @click="handleDelClick(scope.row)" class="opt">删除</div>
                      <div @click="handleClick(scope.row)" class="opt">授权周期</div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
             </el-table-bar>

          </div>
        </div>
      </div>


    <el-dialog title="修改租户" :visible.sync="dialogEditVisible"  width="30%"  top="0">
      <el-form :model="editForm" ref="editForm" :rules="accountRules">
        <el-form-item label="账号" :label-width="formLabelWidth" prop="loginName">
          <el-input v-model="editForm.loginName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="租户" :label-width="formLabelWidth" prop="tenant">
          <el-input v-model="editForm.tenant" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="密码" :label-width="formLabelWidth" prop="loginPwd">
          <el-input type="password" v-model="editForm.loginPwd" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="联系人" :label-width="formLabelWidth" prop="userName">
          <el-input v-model="editForm.userName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" :label-width="formLabelWidth" prop="mobile">
          <el-input v-model="editForm.mobile" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="default" @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit('editForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { tenant, addAccount, editTenant, delTenant } from "@/api/account.js";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [],
      total: 0, //总条数
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: "",
      },
      currentPage: 1,
      totalPage: 2,
      downloadLoading: false,
      userId: "",
      tenantId: "", //账号id

      dialogEditVisible: false,

      editForm: {
        id: "",
        loginName: "",
        tenant: "",
        loginPwd: "",
        userName: "",
        accountType: 1,
        mobile: "",
      }, //修改账号表单
      formLabelWidth: "120px",
      roleList: [], //租户角色
      accountRules: {
        loginName: [
          { required: true, message: "账号不能为空", trigger: "blur" },
        ],
        tenant: [
          { required: true, message: "租户名称不能为空", trigger: "blur" },
        ],
        loginPwd: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        accountType: [
          { required: true, message: "账号类型不能为空", trigger: "blur" },
        ],
        mobile: [{ required: true, message: "请输入手机号", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let userId = this.userid;
      let data = {
        keyWord: this.listQuery.title,
        userId: userId,
        accountType: 1,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
      };
      tenant(data)
        .then((res) => {
         // console.log(this.listQuery.limit);
          setTimeout(() => {
            this.listLoading.close();
          }, 200);
          this.tableData = res.dataList;
          this.total = res.totals;
          if( res.totals==0){
            this.totalPage=1;

          }else{
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);

          }

         // console.log(this.totalPage);
        })
        .catch((error) => {
          this.listLoading.close();
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    //授权周期
    handleClick(row) {
      // console.log(row);
      this.$router.push({ path: `/nested/tenant/auth/${row.userId}` });
    },
    handleSizeChange(val) {
      //console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      //console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
    //刷新
    handleRefresh() {
      this.getData();
    },
    //添加按钮
    handleAddBtn() {
      this.$router.push({
        path: `/nested/tenant/create`,
        query: {},
      });
    },

    //租户修改按钮
    handleEditClick(row) {
      //console.log(row);
      this.dialogEditVisible = true;

      this.editForm.id = row.userId;
      this.editForm.loginName = row.loginName;
      this.editForm.tenant = row.tenant;
      this.editForm.loginPwd = row.loginPwd;
      this.editForm.userName = row.userName;
      this.editForm.accountType = row.accountType;
      this.editForm.mobile = row.mobile;
    },
    //租户修改
    handleSaveEdit(editForm) {
      this.$refs[editForm].validate((valid) => {
        if (valid) {
          // console.log(this.editForm);
          editTenant(this.editForm)
            .then((res) => {
              // console.log(res);
              if (res.ok == true) {
                this.dialogEditVisible = false;
                this.getData();
              }
            })
            .catch((error) => {
              this.dialogEditVisible = false;
              console.log(error);
            });
        } else {
          console.log("添加失败");
          return false;
        }
      });
    },
    //租户删除
    handleDelClick(row) {
      //console.log(row);
      let id = row.userId;
      this.$confirm("确认要删除吗？", {
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        type: "warning",
      })
        .then((e) => {
          // console.log(e);
          if (e === "confirm") {
            delTenant(id)
              .then((res) => {
                // console.log(res);
                if (res.ok == true) {
                  this.getData();
                }
              })
              .catch((error) => {
                console.log(error);
              });
          }
        })
        .catch((e) => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 204px);
}
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
  }
}
</style>

