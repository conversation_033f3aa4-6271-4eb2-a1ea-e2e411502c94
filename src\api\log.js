import request from '@/utils/request'
//云堡垒机---系统登录日志
export function loginLog(data) {
  // debugger
  return request({
    url: '/api/log/loginLogList',
    method: 'post',
    params:data
  })
}
//云堡垒机---系统操作日志
export function operLog(data) {
  // debugger
  return request({
    url: '/api/log/operLogList',
    method: 'post',
    params:data
  })
}
//云数据库审计
export function database(data) {
  // debugger
  return request({
    url: '/api/log/database',
    method: 'post',
    params:data
  })
}
//云主机安全---防火墙/ 网络管理
export function fireWallLog(data) {
  // debugger
  return request({
    url: '/api/log/firewall/firewallLog',
    method: 'post',
    params:data
  })
}
//云主机安全---入侵防御
export function ipsRaw(data) {
    // debugger
    return request({
      url: '/api/log/ipsRaw',
      method: 'post',
      params:data
    })
  }
  //云主机安全---防暴力破解日志
  export function preventForceCrack(data) {
    // debugger
    return request({
      url: '/api/log/forceCrackRawLog',
      method: 'post',
      params:data
    })
  }
  //云主机安全---防恶意软件
  export function webShellLog(data) {
    // debugger
    return request({
      url: '/api/log/facet',
      method: 'post',
      params:data
    })
  }
   //云防火墙---事件日志、网络日志、配置日志
   export function systemLog(data) {
    // debugger
    return request({
      url: '/api/log/systemLog',
      method: 'post',
      params:data
    })
  }
  //云防火墙---威胁日志
  export function threatLog(data) {
    // debugger
    return request({
      url: '/api/log/threatLog',
      method: 'post',
      params:data
    })
  }
   //云waf---事件日志、网络日志、配置日志
   export function wafsystemLog(data) {
    // debugger
    return request({
      url: '/api/log/waf/systemLog',
      method: 'post',
      params:data
    })
  }
   //云waf---网络安全日志
   export function wafNetworkSecurity(data) {
    // debugger
    return request({
      url: '/api/log/waf/threatLog',
      method: 'post',
      params:data
    })
  }
   //云waf---ip防护日志
   export function wafdIpReputation(data) {
    // debugger
    return request({
      url: '/api/log/waf/wafdIpReputationlog',
      method: 'post',
      params:data
    })
  }
  // 云主机安全-- 威胁日志---警报中心
  export function hostAlert(data) {
    // debugger
    return request({
      url: '/api/log/external/alert',
      method: 'post',
      params:data
    })
  }

   //密码安全日志-- 业务系统日志
   export function bussinessLog(data) {
    // debugger
    return request({
      url: '/api/hx/bussinessLog',
      method: 'get',
      params:data
    })
  }

  