<template>
  <div class="mainWrapper">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="mainBox">
        <div class="physical-box1">
          <div class="flow-header">
            <h3>总流量</h3>
            <div class="filter-box">
              <el-select
                v-model="totalValue"
                placeholder="请选择"
                @change="totalSelect"
              >
                <el-option
                  v-for="item in filters"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="flow-body">
            <div
              id="totalFlow"
              :style="{ width: '100%', height: '420px' }"
              v-if="totalFlowVisible == true"
            ></div>
            <div v-else class="null-body">
              <img src="../../assets/data_null.png" alt="" />
              <p>暂无数据</p>
            </div>
          </div>
        </div>
        <div class="physical-box2">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <div class="grid-content">
                <div class="flow-header">
                  <h3>应用 TOP 5</h3>
                  <div class="filter-box">
                    <el-select
                      v-model="appValue"
                      placeholder="请选择"
                      @change="appSelect"
                    >
                      <el-option
                        v-for="item in filters"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="flow-body">
                  <div
                    id="applicationFlow"
                    :style="{ width: '100%', height: '420px' }"
                    v-if="appFlowVisible == true"
                  ></div>
                  <div v-else class="null-body">
                    <img src="../../assets/data_null.png" alt="" />
                    <p>暂无数据</p>
                  </div>
                </div>
              </div></el-col
            >
            <el-col :xs="24" :sm="6" :md="12" :lg="12">
              <div class="grid-content">
                <div class="flow-header">
                  <h3>用户 TOP 5</h3>
                  <div class="filter-box">
                    <el-select
                      v-model="userValue"
                      placeholder="请选择"
                      @change="userSelect"
                    >
                      <el-option
                        v-for="item in filters"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="flow-body">
                  <div
                    id="userFlow"
                    :style="{ width: '100%', height: '420px' }"
                    v-if="userFlowVisible == true"
                  ></div>
                  <div v-else class="null-body">
                    <img src="../../assets/data_null.png" alt="" />
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
// import { device, appRankingTopTen, userRankingTopTen } from '@/api/flow.js';
import {
  getDeviceList,
  getUserTotalStream,
  getAppTotalStream,

} from '@/api/modules/firewall.js';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { Loading } from 'element-ui';
import { mapGetters } from 'vuex';
import moment from 'moment';
import { unitConvert } from '@/utils/calculate.js';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      totalValue: 1, //总流量
      appValue: 1, //应用top5
      userValue: 1, //用户top5
      formLabelWidth: '120px',
      echartsArr: [], //图表数组
      filters: [
        {
          label: '最近一个月',
          value: 1,
        },
        {
          label: '最近一天',
          value: 2,
        },
        {
          label: '最近一小时',
          value: 3,
        },
      ],
      appNames: [], //应用名称数组
      apptotalStreams: [], //应用流量
      userNames: [], //用户名称数组
      userUpStreams: [], //用户上行流量
      userDownStreams: [], //用户下行流量
      totalTimes: [], //总流量时间数组
      totalBws: [], //总流量
      appFlowVisible: false,
      userFlowVisible: false,
      totalFlowVisible: false,
    };
  },
  created() { },
  filters: {},
  mounted() {
    this.getData();
    const self = this;
    window.addEventListener('resize', function () {
      self.echartsArr.forEach((item) => {
        item.resize();
      });
    });
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'tenantid']),
  },
  methods: {
    getData() {
      this.totalPort();
      this.applyPort();
      this.userPort();
    },
    //总流量
    totalPort() {
      let that = this;
      console.log(this.totalValue)
      getDeviceList(this.totalValue)
        .then((res) => {
          console.log(res);
          this.totalTimes = []; //总流量时间数组
          this.totalBws = []; //总流量
          if (res.code == 1) {
            if (JSON.stringify(res.data) != '{}' && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                this.totalTimes.push(moment(item.time).format('YYYY-MM-DD HH:mm')); //总流量时间数组
                this.totalBws.push(item.totalBw); //总流量
              });
              that.totalFlowVisible = true;
              // console.log(this.totalTimes)
              that.$nextTick(function () {
                that.initTotalFlow();
              });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //应用
    applyPort() {
      let that = this;
      //应用top10
      console.log(this.appValue)
      getAppTotalStream(this.appValue)
        .then((res) => {
          console.log(res);
          this.appNames = []; //应用名称数组
          this.apptotalStreams = []; //应用流量
          if (res.code == 1) {
            if (JSON.stringify(res.data) != '{}' && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                this.appNames.push(item.appName);
                this.apptotalStreams.push(item.appTotalStream);
              });
              this.appFlowVisible = true;
              that.$nextTick(function () {
                that.initApplyFlow();
              });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //用户top10接口
    userPort() {
      let that = this;
      console.log(this.userValue)
      getUserTotalStream(this.userValue)
        .then((res) => {
          console.log('用户流量')
          console.log(res);
          this.userNames = [];
          this.userUpStreams = [];
          this.userDownStreams = [];
          if (res.code == 1) {
            if (JSON.stringify(res.data) != '{}' && res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                this.userNames.push(item.user);
                this.userUpStreams.push(item.upStream);
                this.userDownStreams.push(item.downStream);
              });
              this.userFlowVisible = true;
              that.$nextTick(function () {
                that.initUserFlow();
              });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //总流量删选
    totalSelect(value) {
      this.totalValue = value;
      this.totalPort();
    },
    //应用top10-删选
    appSelect(value) {
      this.appValue = value;
      this.applyPort();
    },
    //用户top10-删选
    userSelect(value) {
      this.userValue = value;
      this.userPort();
    },

    initTotalFlow() {
      this.chart_total = this.$echarts.init(document.getElementById('totalFlow'));

      this.echartsArr.push(this.chart_total);

      this.setOptionsTotal();
    },
    initApplyFlow() {
      this.chart_apply = this.$echarts.init(document.getElementById('applicationFlow'));

      this.echartsArr.push(this.chart_apply);

      this.setOptionsApply();
    },
    initUserFlow() {
      this.chart_user = this.$echarts.init(document.getElementById('userFlow'));

      this.echartsArr.push(this.chart_user);

      this.setOptionsUser();
    },

    //总流量
    setOptionsTotal() {
      let that = this;
      this.chart_total.setOption({
        color: '#1389E1',
        grid: {
          top: '30px',
          left: '30px',
          right: '80px',
          bottom: '30px',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var result = '';
            // console.log(params);
            params.forEach(function (item) {
              result +=
                moment(item.axisValueLabel).format('YYYY/MM/DD HH:mm') +
                '</br>' +
                item.marker +
                ' ' +
                item.seriesName +
                ' : ' +
                unitConvert(item.data) +
                'bps' +
                '</br>';
            });
            return result;
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.totalTimes,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            showMaxLabel: true,
            color: '#97A4B6',
            fontSize: 16,
            align: 'center',
            formatter: function (value, index) {
              // console.log(value);
              let time;
              if (that.totalValue == 3) {
                time = moment(value).format(' HH:mm:ss');
              } else if (that.totalValue == 2) {
                time = moment(value).format('MM/DD HH:mm');
              } else if (that.totalValue == 1) {
                time = moment(value).format('MM/DD ');
              }

              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: true, // x轴坐标轴颜色
            lineStyle: {
              color: '#D9D9D9',
            },
          },

          axisTick: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#97A4B6',
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = unitConvert(value);
              return capacity;
            },
          },
          smooth: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dotted', //设置网格线类型 dotted：虚线   solid:实线
              color: '#DAE0E6',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.totalBws,
            symbol: 'none',
            type: 'line',
            name: '总流量',
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: ' rgba(117, 177, 255, 1)' },

                  { offset: 1, color: ' rgba(229, 244, 255, 1)' },
                ]),
              },
            },
          },
        ],
      });
    },
    //应用流量top10
    setOptionsApply() {
      let that = this;
      this.chart_apply.setOption({
        color: '#70C5FF',
        grid: {
          top: '40px',
          left: '20px',
          right: '20px',
          bottom: '30px',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter: function (params) {
            var result = '';
            // console.log(params);
            params.forEach(function (item) {
              result +=
                item.axisValueLabel +
                '</br>' +
                item.marker +
                ' ' +
                item.seriesName +
                ' : ' +
                unitConvert(item.data) +
                'B';
            });
            return result;
          },
        },
        xAxis: {
          type: 'category',
          data: this.appNames,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: '#97A4B6',
            fontSize: 12,
            rotate: 45,
          }, // x轴字体颜色

          axisLine: {
            show: true, // x轴坐标轴颜色
            lineStyle: {
              color: '#D9D9D9',
            },
          },

          axisTick: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
            },
          },
        },
        yAxis: {
          type: 'value',
          name: '流量(Bytes)',
          nameTextStyle: {
            fontSize: 14,
            align: 'right',
          },
          axisLabel: {
            color: '#97A4B6',
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = unitConvert(value);
              return capacity;
            },
          },
          smooth: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dotted', //设置网格线类型 dotted：虚线   solid:实线
              color: '#DAE0E6',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.apptotalStreams,
            symbol: 'none',
            type: 'bar',
            barWidth: 30, //柱图宽度
            name: '总流量',
          },
        ],
      });
    },
    //用户流量
    setOptionsUser() {
      let that = this;
      this.chart_user.setOption({
        color: ['#F9AE00', '#70C5FF'],
        grid: {
          top: '40px',
          left: '20px',
          right: '20px',
          bottom: '30px',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter: function (params) {
            var result = '';
            // console.log(params);
            // params.forEach(function (item) {
            // });
            result +=
              params[0].axisValueLabel +
              '</br>' +
              params[0].marker +
              ' ' +
              params[0].seriesName +
              ' : ' +
              unitConvert(params[0].data) +
              'B' +
              '</br>' +
              params[1].marker +
              ' ' +
              params[1].seriesName +
              ' : ' +
              unitConvert(params[1].data) +
              'B' +
              '</br>' +
              '总流量：' +
              unitConvert(params[0].data + params[1].data) +
              'B';
            return result;
          },
        },
        legend: {
          top: '10px',
          right: '20px',
          icon: 'rect',
        },
        xAxis: {
          type: 'category',
          data: this.userNames,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: '#97A4B6',
            fontSize: 12,
            rotate: 45,
          }, // x轴字体颜色

          axisLine: {
            show: true, // x轴坐标轴颜色
            lineStyle: {
              color: '#D9D9D9',
            },
          },

          axisTick: {
            show: true,
            lineStyle: {
              color: '#D9D9D9',
            },
          },
        },
        yAxis: {
          type: 'value',
          name: '流量(Bytes)',
          nameTextStyle: {
            fontSize: 14,
            align: 'right',
          },
          axisLabel: {
            color: '#97A4B6',
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = unitConvert(value);
              return capacity;
            },
          },
          smooth: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dotted', //设置网格线类型 dotted：虚线   solid:实线
              color: '#DAE0E6',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.userDownStreams,
            symbol: 'none',
            stack: 'total',
            type: 'bar',
            name: '下行流量',
            barWidth: 30, //柱图宽度
          },
          {
            data: this.userUpStreams,
            symbol: 'none',
            stack: 'total',
            type: 'bar',
            name: '上行流量',
            barWidth: 30, //柱图宽度
          },
        ],
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.mainWrapper {
  height: calc(100vh - 60px);
}
.el-scrollbar {
  height: 100%;
}
.mainBox {
  height: 100%;
  background: #f5f7fa;
  padding: 20px;
  .physical-box1 {
    background: #fff;
    height: 460px;
    padding: 20px;
    border-radius: 3px;
    margin-bottom: 20px;
  }
  .flow-header {
    display: flex;
    align-items: center;
    h3 {
      flex: 1;
      font-size: 16px;
      font-weight: normal;
    }
    .filter-box {
      label {
        display: inline-block;
        font-size: 14px;
        font-weight: 400;
        margin-right: 5px;
      }
    }
  }
  .physical-box2 {
    .grid-content {
      background: #fff;
      height: 460px;
      padding: 20px;
      border-radius: 3px;
      margin-bottom: 20px;
    }
  }
  .null-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 420px;
    p {
      color: #999;
    }
  }
}
</style>
