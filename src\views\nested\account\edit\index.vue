<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="accout-top">
          <el-form :model="dataForm" ref="dataForm" :rules="accountRules">

            <el-form-item label="联系人" :label-width="formLabelWidth" prop="userName">
              <el-input v-model="dataForm.userName" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="联系电话" :label-width="formLabelWidth" prop="mobile">
              <el-input v-model="dataForm.mobile" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="角色" :label-width="formLabelWidth" prop="roles">
              <el-select v-model="dataForm.roleIds" placeholder="请选择" multiple>
                <el-option
                  v-for="item in rolesList"
                  :key="item.id"
                  :label="item.remark"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="role-permission-box">
          <div v-for="(role,index) in rolesList" :key="index">
            <h2 class="role-name">{{role.remark}}</h2>
            <ul class="clearfix">
              <li v-for="(per,index) in role.menuList" :key="index" class="role-item">
                <span class="role-label">
                  <i class="el-icon-arrow-right" />
                </span>
                <span>{{per}}</span>
              </li>
            </ul>
          </div>
          <!-- <div class="permission-products-box">
            <h2 class="role-name">授权产品</h2>
            <ul class="clearfix">
              <li v-for="(product,index) in permissionProducts" :key="index" class="role-item">
                <span class="role-label"><i class="el-icon-arrow-right" /></span>
                <span>{{product}}</span>
              </li>
            </ul>
          </div>-->
        </div>
        <div class="btn-box">
           <el-button type="primary" @click="handleSaveClick('dataForm')">保存</el-button>
          <el-button @click="handleCancelClick()">取 消</el-button>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
import { mapGetters } from "vuex";
import { tenant, roleList, addAccount,editAccount } from "@/api/account.js";
export default {
  data() {
    return {
      userId: "",
      userType: "",
      tenantId: "",
      dataForm: {
        id: "",
        tenantId: "",
        userName: "",
        mobile: "",
        roleIds: "",
      },
      rolesList: [],
      formLabelWidth: "60px",
      accountRules: {

        userName: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],

        mobile: [{ message: "请输入手机号", trigger: "blur" }],
        roles: []
      },
      permissionProducts: [
        "综合漏洞扫描",
        "云数据库审计",
        "云堡垒机",
        "云WEB应用防火墙",
        "主机安全防火墙"
      ]
    };
  },
  created() {
    // console.log(this.$route.query)

    this.dataForm.id = this.$route.query.id; //接收参数
    this.dataForm.tenantId = this.$route.query.tenantId;
    this.dataForm.userName = this.$route.query.userName;
    this.dataForm.mobile = this.$route.query.mobile;
    this.dataForm.roleIds = this.$route.query.roleIds.split(',');

    this.getData();

  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"])
  },
  methods: {
    getData() {
      // userType=1  tenantId不传为空 平台
      // ueserType=2  tenantId传
      this.userId = this.userid;
      this.tenantId = this.tenantid;
      //租户角色

      let roleData = {
        userId: this.userId,
        tenantId: this.tenantId
      };
      roleList(roleData)
        .then(res => {
          // console.log(res);
          this.rolesList = res.obj;
          // console.log(this.rolesList);
        })
        .catch(error => {
          console.log(error);
        });
    },
    //取消修改
    handleCancelClick() {
      this.$router.go(-1);//返回上一层
    },
    //确实能够修改
    handleSaveClick(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          let addAccountArry = {
            id:this.dataForm.id,
            tenantId: this.dataForm.tenantId,
            userName: this.dataForm.userName,
            mobile: this.dataForm.mobile,
            roles: this.dataForm.roleIds.join(","),
            userId: this.userId
          };
          // console.log(addAccountArry);
          editAccount(addAccountArry)
            .then(res => {
              // console.log(res);

            })
            .catch(error => {
              console.log(error);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
$blue: rgb(64, 158, 255);
.el-scrollbar {
  height: calc(100vh - 50px);
}
.mainWrapper {
  padding: 10px;
  .mainBox {
    .accout-top {
      .account-title {
        display: inline-block;
        margin-right: 10px;
        .accout-nick {
          display: inline-block;
          margin-left: 10px;
        }
      }
    }
    .role-permission-box {
      padding: 16px 0;
      width: 600px;
      .role-name {
        font-weight: normal;
        font-size: 12px;
        color: rgb(64, 158, 255);
        padding-left: 10px;
        border-left: 2px solid $blue;
        margin-bottom: 16px;
      }
      .role-item {
        float: left;
        margin-bottom: 16px;
        margin-right: 16px;
        .role-checkbox {
          margin-right: 5px;
        }
      }
    }
    .permission-products-box {
      .role-label {
        color: #999;
        margin-right: 10px;
      }
    }
    .btn-box{
      width: 600px;
      text-align: center;
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.el-form-item__error::v-deep {
  top: 0 !important;
  left: 460px !important;
}
</style>

