<template>
  <div class="middle-box">
    <div class="nav-box">
      <img src="@/assets/security/nav.png" alt />
    </div>
    <div class="dot1-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="dot2-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="dot3-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="dot4-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="dot5-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="dot6-box">
      <img src="@/assets/security/circle-dot.png" alt class="dot" />
    </div>
    <div class="nav2-box">
      <img src="@/assets/security/nav2.png" alt />
    </div>
    <div class="admin-box">
      <img src="@/assets/security/admin.png" alt />
      <el-tooltip class="item" effect="light" content="管理员" placement="top">
        <img src="@/assets/security/admin_icon.png" alt class="icon hoverTip" />
      </el-tooltip>
      <img src="@/assets/security/admin_tit.png" alt class="title" />
    </div>

    <div class="safety-box">
      <img src="@/assets/security/safety-bg.png" alt />
      <el-tooltip
        class="item"
        effect="light"
        content="云下一代防火墙"
        placement="top"
      >
        <img
          src="@/assets/security/firewall.png"
          alt
          class="firewall-icon hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云WAF" placement="top">
        <img src="@/assets/security/waf.png" alt class="waf-icon hoverTip" />
      </el-tooltip>
      <img src="@/assets/security/safety-tit.png" alt class="title" />
    </div>
    <div class="product-box">
      <img src="@/assets/security/product-bg.png" alt />
      <el-tooltip class="item" effect="light" content="SSL VPN" placement="top">
        <img
          src="@/assets/security/product-1.png"
          alt
          class="vpn-icon hoverTip"
        />
      </el-tooltip>
      <el-tooltip
        class="item"
        effect="light"
        content="云主机安全"
        placement="top"
      >
        <img
          src="@/assets/security/product-2.png"
          alt
          class="host-icon hoverTip"
        />
      </el-tooltip>
      <el-tooltip
        class="item"
        effect="light"
        content="云数据库审计"
        placement="top"
      >
        <img
          src="@/assets/security/product-3.png"
          alt
          class="database-icon hoverTip"
        />
      </el-tooltip>
      <el-tooltip
        class="item"
        effect="light"
        content="云日志审计"
        placement="top"
      >
        <img
          src="@/assets/security/product-4.png"
          alt
          class="log-icon hoverTip"
        />
      </el-tooltip>
    </div>
    <div class="host-box">
      <img src="@/assets/security/host-bg.png" alt />

      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon1 hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon2 hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon3 hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon4 hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon5 hoverTip"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="light" content="云主机" placement="top">
        <img
          src="@/assets/security/host-icon.png"
          alt
          class="host-icon6 hoverTip"
        />
      </el-tooltip>
      <img src="@/assets/security/host-tit.png" alt class="title" />
    </div>
    <div class="fortress-box">
      <img src="@/assets/security/fortress-bg.png" alt />
      <el-tooltip
        class="item"
        effect="light"
        content="云堡垒机"
        placement="top"
      >
        <img
          src="@/assets/security/fortress-icon.png"
          alt
          class="icon hoverTip"
        />
      </el-tooltip>
    </div>
    <div class="vpc-box">
      <img src="@/assets/security/vpc.png" alt />
    </div>
  </div>
</template>

<script>
export default {
  name: 'NetMiddle',
};
</script>

<style lang="scss" scoped>
.middle-box {
  .admin-box {
    position: absolute;
    top: 22px;
    left: 226px;
    .title {
      position: absolute;
      top: 32px;
      left: 42px;
    }
    .icon {
      position: absolute;
      top: -6px;
      left: 62px;
    }
  }
  .dot1-box {
    position: absolute;
    top: 132px;
    left: 116px;
    width: 180px;

    border: 1px dashed #00518d;
    transform: rotate(-30deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling linear infinite normal;
    }
  }
  .dot2-box {
    position: absolute;
    top: 232px;
    left: 152px;
    width: 58px;

    border: 1px dashed #00518d;
    transform: rotate(30deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling2 linear infinite normal;
    }
  }
  .dot3-box {
    position: absolute;
    top: 246px;
    left: 128px;
    width: 58px;

    border: 1px dashed #00518d;
    transform: rotate(-150deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling2 linear infinite normal;
    }
  }
  .dot4-box {
    position: absolute;
    top: 296px;
    left: 682px;
    width: 76px;
    border: 1px dashed #00518d;
    transform: rotate(32deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling3 linear infinite normal;
    }
  }
  .dot5-box {
    position: absolute;
    top: 316px;
    left: 648px;
    width: 76px;
    border: 1px dashed #00518d;
    transform: rotate(-150deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling3 linear infinite normal;
    }
  }
  .dot6-box {
    position: absolute;
    top: 260px;
    left: 388px;
    width: 116px;
    // border: 1px dashed #00518d;
    transform: rotate(-210deg);
    .dot {
      position: absolute;
      top: -8px;
      left: -8px;
      transform: rotate(30deg);
      animation: 1.2s rolling4 linear infinite normal;
    }
  }

  .safety-box {
    position: absolute;
    top: 92px;
    left: 356px;
    .title {
      position: absolute;
      top: 92px;
      left: 98px;
    }
    .firewall-icon {
      position: absolute;
      top: 26px;
      left: 92px;
    }
    .waf-icon {
      position: absolute;
      top: 98px;
      left: 202px;
    }
  }
  .product-box {
    position: absolute;
    top: 306px;
    left: 382px;
    .vpn-icon {
      position: absolute;
      top: 156px;
      left: 86px;
    }
    .host-icon {
      position: absolute;
      top: 106px;
      left: 166px;
    }
    .database-icon {
      position: absolute;
      top: 54px;
      left: 260px;
    }
    .log-icon {
      position: absolute;
      top: 4px;
      left: 346px;
    }
  }
  .host-box {
    position: absolute;
    top: 218px;
    left: 68px;
    .title {
      position: absolute;
      top: 152px;
      left: 110px;
    }
    .host-icon1 {
      position: absolute;
      top: 22px;
      left: 152px;
    }
    .host-icon2 {
      position: absolute;
      top: 69px;
      left: 230px;
    }
    .host-icon3 {
      position: absolute;
      top: 116px;
      left: 310px;
    }
    .host-icon4 {
      position: absolute;
      top: 70px;
      left: 70px;
    }
    .host-icon5 {
      position: absolute;
      top: 116px;
      left: 152px;
    }
    .host-icon6 {
      position: absolute;
      top: 162px;
      left: 232px;
    }
  }
  .fortress-box {
    position: absolute;
    top: 152px;
    left: 20px;
    .icon {
      position: absolute;
      top: -12px;
      left: 42px;
    }
  }
  .vpc-box {
    position: absolute;
    top: 192px;
    left: 192px;
  }
  .nav-box {
    position: absolute;
    top: 232px;
    left: 396px;
  }
  .nav2-box {
    position: absolute;
    top: 242px;
    left: 426px;
  }
}
</style>
