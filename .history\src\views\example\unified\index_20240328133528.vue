<template>
  <div class="mainWrapper">
    <div class v-if="showTenant">
      <div class="header">
        <h3 class="title">模块管理</h3>
      </div>

      <el-row :gutter="28">
        <el-col>
          <el-card shadow="always" @click="add">
            <div class="card-img">
              <!-- <img :src="item.name == name2 ? '@/assets/yunshujushengji.png':item.name == name2 ? '@/assets/anhFire.png' : item.name == name3 ? '@/assets/hostSafety.png' : item.name == name4 ? '@/assets/cloudFortress.png':'@/assets/webFire.png'" alt="" /> -->
              <img src="@/assets/unified/add.png" alt="" style="width: 44px;">
            </div>
            <p> 添加应用 </p>

          </el-card>
        </el-col>
        <el-col v-for="(item, index) in list" :key="index">
          <el-card shadow="always">
            <div class="card-img">
              <!-- <img :src="item.name == name2 ? '@/assets/yunshujushengji.png':item.name == name2 ? '@/assets/anhFire.png' : item.name == name3 ? '@/assets/hostSafety.png' : item.name == name4 ? '@/assets/cloudFortress.png':'@/assets/webFire.png'" alt="" /> -->
              <img src="@/assets/unified/yunshujushengji.png" alt="">
            </div>
            <p> {{ item.name }} </p>

          </el-card>
        </el-col>
      </el-row>
      <AddDialog v-if="addDialogShow"/>

    </div>
  </div>
</template>

<script>
import AddDialog from './add.vue'

export default {
  components: { AddDialog },

  data() {
    return {
      showTenant: true,
      addDialogShow:false,
      name1: '云数据审计',
      name2: '云数据审计',
      name3: '云数据审计',
      name4: '云数据审计',
      name5: '云数据审计',
      name6: '云数据审计',
      name7: '云数据审计',

      list: [
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
        {
          name: '云数据审计'
        },
      ]

    };
  },
  created() {

  },
  computed: {

  },
  watch: {
    // 新窗口打开页面，解决浏览器拦截问题
    // jumpUrl() {
    //   if (this.jumpUrl) {
    //     window.open(this.jumpUrl, "_blank");
    //   }
    //   this.jumpUrl = null;
    // }
  },
  mounted() {

  },

  methods: {
    add() {
      this.addDialogShow =true
    }

  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}

.mainWrapper {
  .el-row {
    margin: 20px 10px;
    padding-left: 20px;

    .card-img {
      width: 98px;
      height: 98px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 70px;
      }

    }

    p {
      margin-bottom: 20px;
    }

  }
}
</style>
<style>
.el-col {
  width: 218px;
  height: 218px;
}

.el-card__body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.product-img:focus {
  outline: none !important;
}

.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow:after {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow {
  border-left-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow:after {
  border-left-color: #004578;
}

/* 控制主题颜色 */
.el-tooltip__popper.is-light {
  background: #0043b1 !important;
}
</style>
