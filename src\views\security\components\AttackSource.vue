<template>
  <div class="attack-wrap">
    <ul>
      <li
        class="attack-source-item"
        v-for="(attack, index) in attackSourceList"
        :key="index"
      >
        <span class="ranking">{{ index + 1 }}</span>
        <span class="ip">{{ attack.ip }}</span>
        <!-- <span class="city">无</span>  -->
        <span class="line"></span>
        <span class="number">{{ attack.attackCount }}</span>
      </li>
    </ul>
    <ul>
      <li
        class="attack-source-item"
        v-for="(item, index) in attackSourceNullList"
        :key="index"
      >
        <span class="ranking">{{ item }}</span>
        <span class="ip">无</span>
        <!-- <span class="city">无</span> -->
        <span class="line"></span>
        <span class="number">-</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'AttackSource',
  props: {
    attackSourceList: {
      type: Array,
      default: [],
    },
    attackSourceNullList: {
      type: Array,
      default: [],
    },
  },
  watch: {
    attackSourceList: {
      handler(newVal, oldVal) {
        this.attackSourceList = newVal;
      },
      deep: true,
    },
    attackSourceNullList: {
      handler(newVal, oldVal) {
        this.attackSourceNullList = newVal;
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.attack-wrap {
  .attack-source-item {
    position: relative;
    display: flex;
    margin-top: 10px;
    box-sizing: border-box;
    color: #cbedff;
    .ranking {
      display: inline-block;
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      background: rgba(0, 54, 96, 1);
      color: #52c4ff;
      margin-right: 20px;
    }
    .ip {
      flex: 1;
      height: 24px;
      line-height: 24px;
    }
    .city {
      display: inline-block;
      width: 80px;
      height: 24px;

      line-height: 24px;
    }
    .line {
      display: inline-block;
      // width: 112px;
      width: 160px;
      height: 1px;
      margin-top: 10px;
      margin-right: 20px;
      background: #003660;
    }
    .number {
      display: inline-block;
      width: 80px;
      height: 24px;
      line-height: 24px;
      text-align: right;
      background: #0042a4;
      color: #fff;
      box-sizing: border-box;
      padding: 0 5px;
    }
  }
}
</style>
