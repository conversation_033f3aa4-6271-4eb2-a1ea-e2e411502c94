<template>
  <div>
    <!-- <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">操作日志</h3>
          </div> -->
    <div class="serch-box clearfix">
      <div class="filter-container">
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          @click="handleRefresh()"
        >
          <svg-icon icon-class="refresh" />
        </el-button>
        <!-- <div class="search-container filter-item">
                <el-input
                  v-model="listQuery.title"
                  placeholder="日志名称/用户名/IP地址"
                  style="width: 200px"
                  v-on:input="search"
                />
                <span
                  class="el-icon-search search-btn"
                  @click="handleSearch()"
                ></span>
              </div> -->
        <!-- <el-select
                v-model="listQuery.logType"
                placeholder="请选择"
                class="filter-item"
                style="width: 200px"
                @change="search()"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select> -->
      </div>
      <div class="page-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="sizes, prev,slot, next,total"
          :total="total"
        >
          <span class="pageNum">
            {{ this.listQuery.page }}
            <i class="divider">/</i>
            {{ totalPage }}
          </span>
        </el-pagination>
      </div>
    </div>
    <div class="table-box">
      <el-table-bar>
        <el-table :data="tableData" style="width: 100%; margin-bottom: 20px">
          <el-table-column
            prop="logName"
            label="日志名称"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="logType"
            label="日志类型"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="userName"
            label="用户名称"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="ipAddress"
            label="IP地址"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="description"
            label="日志内容"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="时间"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <!-- <el-table-column label="操作" width="160" align="center">
                  <template slot-scope="scope">
                    <el-dropdown>
                      <span class="el-dropdown-link">
                        <i class="el-icon-more" ></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <div @click="handleClick(scope.row)" class="opt">查看</div>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column> -->
        </el-table>
      </el-table-bar>
    </div>
    <!-- </div>
      </div> -->
  </div>
</template>
  <script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { oprateLog } from "@/api/system.js";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [],
      total: 0,
      totalPage: 2,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: "",
        logType: "",
        date: "",
      },
      currentPage: 1,
      downloadLoading: false,
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "login",
          label: "登录日志",
        },
        {
          value: "operation",
          label: "操作日志",
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = {
        // keyWord: this.listQuery.title,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
        // logType: this.listQuery.logType,
      };
      console.log(data);
      //操作日志列表

      oprateLog(data)
        .then((res) => {
          // console.log(res);
          setTimeout(() => {
            this.listLoading.close();
          }, 200);
          this.tableData = res.data.rows;
          this.total = res.data.total_rows;
          this.currentPage = res.data.page;
          if (res.data.total_rows == 0) {
            this.totalPage = 1;
          } else {
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          }
        })
        .catch((error) => {
          console.log(error);
          this.listLoading.close();
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //刷新
    handleRefresh() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    handleClick(row) {
      // console.log(row);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
  </script>
  <style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 245px);
}
</style>
  