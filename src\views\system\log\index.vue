<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">系统日志</h3>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="登录日志" name="first">
          <loginLog />
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="second">
          <oprateLog />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import loginLog from "./loginLog/index.vue";
import oprateLog from "./opratelog/index.vue";

export default {
  components: {
    loginLog,
    oprateLog,
  },
  data() {
    return {
      activeName: "first",
    };
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event);
    },
  },
};
</script>
<style scoped lang="scss">
  .elTableBar {
    height: calc(100vh - 204px);
  }
.mainWrapper {
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;
       

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.mainBox::v-deep .el-tabs__item {
  height: 46px;
  line-height: 46px;
}
</style>
