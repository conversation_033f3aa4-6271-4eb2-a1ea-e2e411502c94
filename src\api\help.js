import request from "@/utils/request";
//axios 参数data用于post ;params多用于get

//帮助中心列表
export function helpList(data) {
  //    debugger
  return request({
    url: "/api/help/getHelpList",
    method: "get",
    params: data,
  });
}

//帮助中心新增或更新
export function addHelp(data) {
  //    debugger
  return request({
    url: "/api/help/addOrUpdateCenterInfo",
    method: "post",
    params: data,
  });
}

//帮助中心文件上传
export function uploadFiles(data) {
  //    debugger
  return request({
    headers: {
      "Content-Type": "multipart/form-data;charset=UTF-8",
    },
    contentType:false,
    processData:false,
    url: "/api/help/uploadFile",
    method: "post",
    data: data,
    transformRequest:[function(){
      return data;
    }]
  });
}
//帮助中心删除
export function delHelpList(data) {
  // debugger
  return request({
    url: "/api/help/deleteById",
    method: "post",
    params: data,
  });
}

//帮助中心-下载文件
export function fileDown(data) {
  // debugger
  return request({
    url: "/api/help/fileDown",
    method: "get",
    params:data,
    responseType: 'arraybuffer'
    
  });
}