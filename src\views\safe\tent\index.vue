<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="mainWrapper">
        <div class="mainBox">
          <div class="filter-container">
            <el-input
              v-model="listQuery.title"
              placeholder="名称/租户名"
              style="width: 200px;"
              class="filter-item"
            />

            <el-button v-waves class="filter-item2" type="primary" icon="el-icon-search">搜索</el-button>
          </div>
          <el-table :data="tableData" style="width: 100%;" row-key="id" border>
            <el-table-column prop="name" label="产品名称" sortable ></el-table-column>
            <el-table-column prop="alias" label="产品ID/别名" sortable width="180"></el-table-column>
            <el-table-column prop="tentant" label="租户名" sortable></el-table-column>
            <el-table-column prop="specification" label="规格" width="80"></el-table-column>
            <el-table-column prop="startTime" label="开通时间"></el-table-column>
            <el-table-column prop="endTime" label="到期时间"></el-table-column>
            <el-table-column prop="status" label="状态"></el-table-column>
            <el-table-column label="操作" width="220" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small">立即使用</el-button>
                <el-button @click="dialogEditVisible = true" type="text" size="small">扩容</el-button>
                <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="50"
          ></el-pagination>
        </div>
      </div>
    </el-scrollbar>

    <el-dialog title="扩容" :visible.sync="dialogEditVisible"  top="0">
      <el-form :model="form2">
        <el-form-item label="容量(G)" :label-width="formLabelWidth">
          <el-input v-model="form2.name" autocomplete="off"></el-input>
        </el-form-item>
       
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogEditVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [
        {
          id: 1,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },
        {
          id: 2,
          name: "下一代防火墙",
          alias: "156/EDR",
          tentant: "test002",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },
        {
          id: 3,
          name: "云WEB应用防火墙",
          alias: "101/VWAF",
          tentant: "test006",
          specification: "标准版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },
        {
          id: 4,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },
        {
          id: 5,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },
        {
          id: 6,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        },{
          id: 7,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
          specification: "基础版",
          startTime: "2020-06-16 17:30:25",
          endTime: "2020-08-16 23:59:59",
          status: "可用"
        }
      ],

      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+id"
      },
      currentPage4: 1,
      downloadLoading: false,
      dialogFormVisible: false,
      form: {
        name: "",
        menuNumber: "",
        fatherNumber: "",
        address: "",
        sort: "",
        isMenu: "",
        icon: ""
      },
      formLabelWidth: "120px",
      dialogEditVisible: false,
      form2: {
        name: "2.2",
        
      }
    };
  },
  created() {},
  methods: {
    handleClick(row) {
      console.log(row);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  .mainBox {
    .filter-container {
      margin-bottom: 10px;
      .filter-item {
        margin-right: 10px;
      }
    }
    .el-pagination {
      margin-top: 10px;
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>

