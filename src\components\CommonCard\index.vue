<template>
  <el-card class="box-card">
    <div class="common-card">
      <div class="title">{{ title }}</div>
      <div class="card-bottom">
        <div class="left">
          <slot name="value"></slot>
        </div>
        <div class="chart">
          <slot name="chart"></slot>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script>
export default {
  name: 'CommonCard',
  props: {
    title: String,
  },
};
</script>
<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
}
.title {
  margin-bottom: 16px;
}
.card-bottom {
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;
  }
}
</style>
