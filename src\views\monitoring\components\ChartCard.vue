<template>
  <el-card class="card-box">
    <div class="card-header">
      <div class="title">{{ title }}</div>
      <div class="tags-group">
        <div class="tags-hd" v-for="(item, key) in tags">
          <span class="dot" :class="item.bg"></span>
          <span class="cont">{{ item.name }}</span>
        </div>
        <div class="info">
          <slot name="tips"></slot>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <div class="chart">
        <slot></slot>
      </div>
      <div class="data">
        <slot name="list"></slot>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'ChartCard',
  props: {
    title: String,
    tags: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    // tags: {
    //   handler(val) {
    //     this.tags = val;
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    // console.log(this.tags);
  },
};
</script>

<style lang="scss" scoped>
.card-box {
  height: 320px;
  margin-bottom: 20px;
  .card-header {
    display: flex;
    align-content: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .tags-group {
    display: flex;
    align-items: center;
    .info {
      display: inline-block;
      margin-left: 20px;
    }
    .tags-hd {
      margin-left: 12px;
    }
    .dot {
      display: inline-block;
      width: 10px;
      height: 10px;
      // margin-right: 3px;
      background: #e3e3e3;
      &.bg-warm {
        background: #ffc700;
      }
      &.bg-blue {
        background: #108ff4;
      }
      &.bg-violet {
        background: #6e3cfc;
      }
      &.bg-red {
        background: #f95b6c;
      }
      &.bg-green {
        background: #00a700;
      }
    }
  }
  .card-footer {
    display: flex;
    .chart {
      width: 42%;
      height: 240px;
    }
    .data {
      width: 58%;
      height: 240px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-content: center;
      // justify-content: center;
    }
  }
}
</style>
