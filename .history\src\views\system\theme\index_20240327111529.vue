<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">主题外观</h3>
          <h6 class="sub-title">用户可自定义设置平台主题外观</h6>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="theme-box">
            <h3>标题设置</h3>
            <ul class="theme-list">
              <li v-for="(item, index) in themeList" :key="index">
                <img :src="item.image" alt="" />
                <span class="title">{{ item.title }}</span>
                <span class="sorce"
                  >{{ item.subTitle == null ? 0 : item.subTitle }}
                  <span v-if="item.type == 5">s</span></span
                >
                <svg-icon icon-class="edit" @click="editTheme(item)" />
              </li>
            </ul>
          </div>

          <!-- <div class="theme-box">
          <h3>轮播时间设置</h3>
          <ul class="theme-list">
            <li v-for="(item, index) in rotationArr" :key="index">
              <span class="title">{{ item.title }}</span>
              <span class="sorce">{{ item.time }}s</span>
              <svg-icon icon-class="edit" @click="editTheme(item)" />
            </li>
          </ul>
        </div> -->
        </el-scrollbar>
      </div>
    </div>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="30%"
      top="0"
    >
      <div class="menudata-box">
        <el-form ref="form" :model="form" label-width="90px" :rules="rules">
          <div v-if="form.type == 5">
            <el-form-item label="轮播时间(s)" prop="subTitle">
              <el-input v-model="form.subTitle"></el-input>
            </el-form-item>
          </div>
          <div v-else>
            <el-form-item label="标题文字" prop="title">
              <el-input v-model="form.title"></el-input>
            </el-form-item>
            <el-form-item label="图片">
              <el-upload
                class="avatar-uploader"
                action="/api/uploadFile/upload"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="form.imageUrl" :src="form.imageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <span style="color: #f00">上传图片大小不能超过 2MB!</span>
              <span v-if="form.type == 1" style="color: #f00"
                >(图片只能上传ico文件)</span
              >
              <span v-else style="color: #f00">(图片只能上传jpg/png格式)</span>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
import { getAllTheme, update, addTime } from "@/api/theme.js";
export default {
  inject: ['reload'],
  directives: { waves },
  data() {
    return {
      dialogVisible: false,
      themeList: [],
      form: {
        title: "",
        imageUrl: "",
        type: "",
        id: "",
        updateImg: "",
        subTitle: "",
      },
      dialogTitle: "设置",
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
      },
      rotationArr: [{
        title: '轮播图时间设置',
        time: '2'
      }]
    };
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      getAllTheme()
        .then((res) => {
          console.log(res);
          this.themeList = res;

          // if (res.code == 1) {

          // }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //编辑主题
    editTheme(item) {
      this.dialogVisible = true;
      if (item.type == 5) {
        this.dialogTitle = item.title;
      } else {
        this.dialogTitle = "设置" + item.subTitle + "主题";
      }
      this.form.title = item.title;
      this.form.imageUrl = item.image;
      this.form.type = item.type;
      this.form.id = item.id;
      this.form.subTitle = item.subTitle;
    },
    handleAvatarSuccess(res, file) {
      this.form.imageUrl = URL.createObjectURL(file.raw);
      let ishttps = 'https:' == document.location.protocol ? true : false;
      let reg = new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
      let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
      //  console.log(res);
      if (res.code == 1) {
        // if(ishttps){
        //   let ip=res.data.fileUrl.match(reg)[0]+'/api';
        //   this.form.imageUrl = res.data.fileUrl.replace(spat,ip) 
        // }else{
        //     this.form.imageUrl = res.data.fileUrl;
        // }
        this.form.imageUrl = res.data.fileUrl;
        console.log("图片上传测试地址：" + this.form.imageUrl);
        this.$message.success("图片上传成功");
      } else {
        this.$message.error("图片上传失败");
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;
      const isIcon = file.type === "image/x-icon";
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      if (this.form.type == 1) {
        if (!isIcon) {
          this.$message.error("上传图片只能上传ico文件");
        }
        return isIcon && isLt2M;
      } else {
        if (!isJPG) {
          this.$message.error("上传图片只能上传jpg/png格式!");
        }
        return isJPG && isLt2M;
      }
    },
    handleSave() {
      if (this.form.type == 5) {
        let prams = {
          time: this.form.subTitle,
          id: this.form.id
        }
        console.log(prams);
        addTime(prams).then((res) => {
          console.log(res);
          if (res.code === 1) {
            this.dialogVisible = false;
            this.$message.success("修改成功");
            this.getData()
          }
        })
          .catch((error) => {
            console.log(error);
          });
      } else {
        let data = {
          id: this.form.id,
          title: this.form.title,
          image: this.form.imageUrl,
          sub_title: this.form.subTitle,
        };
        // console.log(data);
        update(data)
          .then((res) => {
            // console.log(res);
            if (res.flag == true) {
              this.dialogVisible = false;
              this.$message.success("修改成功");
              // this.getData();
              if (this.form.type == 1) {
                location.reload();
              } else {
                this.reload();
              }
              if (this.form.type == 1) {
                //浏览器
                localStorage.setItem("browseImg", this.form.imageUrl);
                localStorage.setItem("browseTitle", this.form.title);

              } else if (this.form.type == 2) {
                //网络大屏
                localStorage.setItem("screenImg", this.form.imageUrl);
                localStorage.setItem("screenTitle", this.form.title);
              } else if (this.form.type == 3) {
                //主页面
                localStorage.setItem("mainImg", this.form.imageUrl);
                localStorage.setItem("mainTitle", this.form.title);
              } else if (item.type == 6) {
                //密码大屏
                localStorage.setItem("psdScreenImg", item.image);
                localStorage.setItem("psdScreenTitle", item.title);
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });

      }

    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 148px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .sub-title {
      font-weight: normal;
      margin-top: 10px;
    }
    .theme-box {
      width: 60%;
      margin: 0 auto;
      h3 {
        font-weight: normal;
        line-height: 46px;
        margin: 20px 0;
      }
      .theme-list {
        li {
          padding: 10px;
          background: #f5f7fa;
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          img {
            max-width: 60px;
            height: 30px;
            margin-right: 8px;
          }
          .title {
            flex: 1;
          }
          .sorce {
            flex: 0 0 auto;
            margin-right: 10px;
          }
          .svg-icon {
            cursor: pointer;
          }
        }
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.menudata-box {
  width: 100%;
  box-sizing: border-box;

  .el-input {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
