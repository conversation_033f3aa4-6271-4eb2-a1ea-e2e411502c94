<template>
  <div :id="id" :style="style"></div>
</template>

<script>
import moment from 'moment';

export default {
  name: 'AttackType',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.init();
    });
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      this.$nextTick(() => {
        this.setOption();
        // console.log(this.chartData)
      });
      window.addEventListener('resize', this.chart.resize);
    },
    setOption() {
      const that = this;
      let option = {};
      option = {
        grid: {
          top: '20px',
          left: '0',
          right: '8%',
          bottom: '20px',
          containLabel: true,
        },
        tooltip: {},
        xAxis: {
          type: 'category',
          data: this.chartData.xAxisData,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: '#fff',
            interval: 0,
            rotate: -45,
            formatter: function (value) {
              if (value.length > 8) {
                return `${value.slice(0, 6)}...`;
              }
              return value;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid', //设置网格线类型 dotted：虚线   solid:实线
              color: '#003660',
            },
          }, //去除网格线
          nameTextStyle: {
            color: '#003660',
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: this.chartData.yAxisData,
            barWidth: 16, //柱图宽度
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: '#005EA4', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#0091FF', // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
            },
            type: 'bar',
            showBackground: false,
            label: {
              show: true,
              position: 'top',
              color: '#fff',
            },
            backgroundStyle: {
              color: 'rgba(220, 220, 220, 0.8)',
            },
          },
        ],
      };

      this.chart.setOption(option, true);
    },
  },
};
</script>
