<template>
  <div class="head" :style="{ backgroundImage: 'url(' + headBg + ')' }">
    <span class="title">{{ title }} </span>
    <div class="datebox">
      <img :src="imgSrc" alt="" style="margin-right: 10px" />
      <div class="time">{{ nowTime }}</div>
      <div class="content">
        <p class="week">{{ nowWeek }}</p>
        <p class="date">{{ nowDate }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import headBg2 from '@/assets/security/head_bg.png';
export default {
  name: 'TopHead',
  props: {
    title: {
      type: String,
      default: '',
    },
    imgSrc: {
      type: String,
      default: '',
    },
  },
  watch: {
    title: {
      handler(newVal, oldVal) {
        this.title = newVal;
      },
      deep: true,
    },
    imgSrc: {
      handler(newVal, oldVal) {
        this.imgSrc = newVal;
      },
      deep: true,
    },
  },
  data() {
    return {
      headBg: headBg2,
      nowDate: '', // 当前日期
      nowTime: '', // 当前时间
      nowWeek: '', // 当前星期
    };
  },
  mounted() {
    this.currentTime();
  },
  methods: {
    currentTime() {
      setInterval(this.getDate, 500);
    },
    getDate() {
      var _this = this;
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let week = new Date().getDay();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? '0' + new Date().getMinutes()
          : new Date().getMinutes();
      let ms =
        new Date().getSeconds() < 10
          ? '0' + new Date().getSeconds()
          : new Date().getSeconds();
      if (week == 1) {
        this.nowWeek = '周一';
      } else if (week == 2) {
        this.nowWeek = '周二';
      } else if (week == 3) {
        this.nowWeek = '周三';
      } else if (week == 4) {
        this.nowWeek = '周四';
      } else if (week == 5) {
        this.nowWeek = '周五';
      } else if (week == 6) {
        this.nowWeek = '周六';
      } else {
        this.nowWeek = '周日';
      }
      _this.nowTime = hh + ':' + mf + ':' + ms;
      _this.nowDate = yy + '年' + mm + '月' + dd + '日';
    },
  },
  beforeDestroy() {
    // 离开页面的时候清除
    if (this.getDate) {
      //console.log("销毁定时器");
      clearInterval(this.getDate); // 在Vue实例销毁前，清除时间定时器
    }
  },
};
</script>

<style lang="scss" scoped>
.head {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  .title {
    font-weight: normal;
    margin-left: 60px;
    font-size: 40px;
    color: #fff;
    white-space: pre;
    letter-spacing: 1.74px;

    text-shadow: 0 0 6px #0089f3;
    /* .switch-btn{
        display: inline-block;
        width: 28px;
        height: 28px;
        margin-left: 20px;
        cursor: pointer;
      } */
  }
  .datebox {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    border-radius: 2px;
    margin-right: 60px;
    .time {
      font-family: Roboto, PingFang SC, Noto Sans CJK, Microsoft YaHei;
      cursor: pointer;
      font-size: 48px;
      letter-spacing: 0;
      height: 48px;
      margin-right: 28px;
      color: rgba(82, 196, 255, 1);
    }
    .content {
      font-size: 16px;
      font-family: PingFangSC-Semibold;

      color: rgba(82, 196, 255, 1);
      .week {
        line-height: 24px;
      }
      .date {
        letter-spacing: 1px;

        line-height: 24px;
      }
    }
  }
}
</style>
