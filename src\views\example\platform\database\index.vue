<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box">
        
        <div class="auth-item">
          <h2 class="title">云数据库审计(闪捷)</h2>
          <el-table
            :data="databaseData"
            style="width: 100%;margin-bottom:20px;"
            row-key="id"
            border
          >
            <el-table-column prop="name" label="单位名称" sortable width="120"></el-table-column>
            <el-table-column prop="time" label="审计有效期" sortable></el-table-column>
            <el-table-column prop="assetsNum" label="资产数" width="90"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称"></el-table-column>
            <el-table-column prop="version" label="版本号" width="80"></el-table-column>
            <el-table-column prop="issueNum" label="发布号" width="80"></el-table-column>
            <el-table-column prop="compilationNum" label="管理中心编译号"></el-table-column>
            <el-table-column prop="dataNum" label="数据处理中心编译号"></el-table-column>
            <el-table-column prop="satrtTime" label="编译时间"></el-table-column>
            <el-table-column prop="factory" label="设备厂家"></el-table-column>
          </el-table>
        </div>
      
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
   
      databaseData: [
        {
          name: "池州市政务云",
          time: "2020-07-29",
          assetsNum: "20",
          deviceName: "数据库审计和防护系统",
          version: "3.0",
          issueNum: "********",
          compilationNum: "14351",
          dataNum: "14470",
          satrtTime: "2020-06-08 14：31",
          factory: "闪捷信息科技有限公司"
        }
      ],
     
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
    
    }
  }
}
</style>
