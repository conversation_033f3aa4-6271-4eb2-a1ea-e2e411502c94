<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="physical-box1">
      <el-row :gutter="15">
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <img src="@/assets/offline.png" alt class="virtualm-img" />
            <div class="text-box">
              <p class="number">8</p>
              <p class="title">离线</p>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <img src="@/assets/virtual-machine.png" alt class="virtualm-img" />
            <div class="text-box">
              <p class="number">12</p>
              <p class="title">虚拟机总数</p>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <img src="@/assets/alarm.png" alt class="virtualm-img" />
            <div class="text-box">
              <p class="number">2</p>
              <p class="title">告警</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="physical-box2">
      <div class="container">
        <div class="filter-container">
          <el-input
            v-model="listQuery.title"
            placeholder="产品/租户名"
            style="width: 200px;"
            class="filter-item"
          />

          <el-button v-waves class="filter-item2" type="primary" icon="el-icon-search">搜索</el-button>
        </div>
        <el-table :data="tableData" style="width: 100%;" row-key="id" border>
         
          <el-table-column prop="alias" label="产品ID/别名" sortable width="180"></el-table-column>
          <el-table-column prop="tentant" label="所属租户" sortable></el-table-column>
          <el-table-column prop="address" label="地址"></el-table-column>
          <el-table-column prop="status" label="状态"></el-table-column>
          <el-table-column prop="cpu" label="CPU" width="80"></el-table-column>
          <el-table-column prop="ram" label="内存"  width="80"></el-table-column>
          <el-table-column prop="disk" label="磁盘"  width="100"></el-table-column>
          <el-table-column prop="incoming" label="网络入带宽"></el-table-column>
          <el-table-column prop="outBand" label="网络出带宽"></el-table-column>
           <el-table-column  label="告警状态">
               <template slot-scope="scope">
                   <div>
                      <span v-if="scope.row.alarmStatus==1" class="normal">正常</span>
                       <span v-if="scope.row.alarmStatus==2" class="abnormal">异常</span>
                   </div>
            
            </template>
           </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)" type="text" size="small">查看图表</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="50"
        ></el-pagination>
      </div>
    </div>
   
  </el-scrollbar>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [
        {
          id: 1,
          alias: "32/VBD",
          tentant: "test001",
          address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:1

        },
        {
          id: 2,
        
          alias: "156/EDR",
          tentant: "test002",
            address: "172.16.104",
         status: "离线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:2
        },
        {
          id: 3,
          name: "云WEB应用防火墙",
          alias: "101/VWAF",
          tentant: "test006",
           address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:1
        },
        {
          id: 4,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
            address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:2
        },
        {
          id: 5,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
           address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:2
        },
        {
          id: 6,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
            address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:1
        },
        {
          id: 7,
          name: "云数据审计",
          alias: "32/VBD",
          tentant: "test001",
            address: "172.16.104",
         status: "在线",
         cpu: "10%",
         ram: "68.23%",
         disk:'33.36%',
         incoming:'126.78KB',
         outBand:'67.18KB',
         alarmStatus:1
        }
      ],

      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+id"
      },
      currentPage4: 1,
      downloadLoading: false,
      dialogFormVisible: false,
      form: {
        name: "",
        menuNumber: "",
        fatherNumber: "",
        address: "",
        sort: "",
        isMenu: "",
        icon: ""
      },
      formLabelWidth: "120px",
      dialogEditVisible: false,
      form2: {
        name: "2.2"
      }
    };
  },
  created() {},
  methods: {
      //查看图表
    handleClick(row) {
      console.log(row);
       this.$router.push({path:`/monitoring/virtualm/echarts/${row.id}`})
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.physical-box1 {
  padding: 20px 30px;
  margin-bottom: 10px;
  background-color: #4f535b;
  .grid-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 160px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    color: #fff;
    .virtualm-img {
      width: 86px;
      height: 86px;
      margin-right: 80px;
    }
    .text-box {
      .number {
        text-align: center;
        font-size: 36px;
        border-bottom: 2px solid #0de9ed;
        padding-bottom: 8px;
        margin-bottom: 10px;
      }
    }
  }
}
.physical-box2 {
  padding: 10px;
  .container {
    padding: 20px 10px;
    background: #fff;
    border-radius: 3px;
    height:auto;
    .filter-container {
      margin-bottom: 10px;
      .filter-item {
        margin-right: 10px;
      }
    }
    .el-pagination {
      margin-top: 10px;
    }
    .normal{
        color: #409EFF;
    }
    .abnormal{
        color: #F56C6C;
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>

