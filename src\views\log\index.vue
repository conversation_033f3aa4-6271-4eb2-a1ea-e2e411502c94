<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">安全日志</h3>
        <ul class="tab-box">
          <li
            class="tab-item"
            v-for="(tab, index) in tab"
            :key="index"
            @click="tabClick(index)"
            :class="{ activeColor: index == tabNum }"
          >
            {{ tab }}
          </li>
        </ul>
      </div>
      <!-- 云防火墙 -->
      <div v-if="tabNum == 0" class="tab-pane-content">
        <div class="tab-pane-content">
          <div class="border-card-box">
            <el-tabs type="border-card" @tab-click="handleClickFirewall">
              <el-tab-pane label="事件日志">
                <div class="tab-pane-content">
                  <div class="serch-box clearfix">
                    <div class="filter-container">
                      <el-button
                        v-waves
                        class="filter-item"
                        type="primary"
                        @click="fireeventRefresh"
                      >
                        <svg-icon icon-class="refresh" />
                      </el-button>
                      <!-- <el-select
                        v-model="listQueryFireevent.date"
                        placeholder="请选择"
                        @change="fireEventDateSelct"
                      >
                        <el-option
                          v-for="item in dateOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>-->
                    </div>
                    <div class="page-box">
                      <el-pagination
                        @size-change="handleSizeChangeFireevent"
                        @current-change="handleCurrentChangFireevent"
                        :current-page="listQueryFireevent.currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="sizes, prev,slot, next,total"
                        :total="listQueryFireevent.total"
                      >
                        <span class="pageNum">
                          {{ this.listQueryFireevent.page }}
                          <i class="divider">/</i>
                          {{ this.listQueryFireevent.totalPage }}
                        </span>
                      </el-pagination>
                    </div>
                  </div>
                  <div class="table-box">
                    <el-table-bar height="calc(100vh - 210px)">
                      <el-table
                        :data="fireEventData"
                        style="width: 100%; margin-bottom: 20px"
                        row-key="id"
                      >
                        <el-table-column
                          prop="timestamp"
                          label="时间"
                          width="210"
                          :formatter="dateFormat"
                          sortable
                        ></el-table-column>

                        <el-table-column label="级别" width="120" sortable>
                          <template slot-scope="scope">
                            <span v-if="scope.row.severity == 0">紧急</span>
                            <span v-if="scope.row.severity == 1">警报</span>
                            <span v-if="scope.row.severity == 2">严重</span>
                            <span v-if="scope.row.severity == 3">错误</span>
                            <span v-if="scope.row.severity == 4">警告</span>
                            <span v-if="scope.row.severity == 5">通告</span>
                            <span v-if="scope.row.severity == 6">信息</span>
                            <span v-if="scope.row.severity == 7">调试</span>
                            <span v-if="scope.row.severity == 8">>=紧急</span>
                            <span v-if="scope.row.severity == 9">>=警报</span>
                            <span v-if="scope.row.severity == 10">>=严重</span>
                            <span v-if="scope.row.severity == 11">>=错误</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="content"
                          label="消息"
                          show-overflow-tooltip
                        ></el-table-column>
                      </el-table>
                    </el-table-bar>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="网络日志">
                <div class="tab-pane-content">
                  <div class="serch-box clearfix">
                    <div class="filter-container">
                      <el-button
                        v-waves
                        class="filter-item"
                        type="primary"
                        @click="firenetWorkRefresh"
                      >
                        <svg-icon icon-class="refresh" />
                      </el-button>
                      <!-- <el-select
                        v-model="listQueryFirenetWork.date"
                        placeholder="请选择"
                        @change="firenetWorkDateSelct"
                      >
                        <el-option
                          v-for="item in dateOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>-->
                    </div>
                    <div class="page-box">
                      <el-pagination
                        @size-change="handleSizeChangeFirenetWork"
                        @current-change="handleCurrentChangFirenetWork"
                        :current-page="listQueryFirenetWork.currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="sizes, prev,slot, next,total"
                        :total="listQueryFirenetWork.total"
                      >
                        <span class="pageNum">
                          {{ this.listQueryFirenetWork.page }}
                          <i class="divider">/</i>
                          {{ this.listQueryFirenetWork.totalPage }}
                        </span>
                      </el-pagination>
                    </div>
                  </div>
                  <div class="table-box">
                    <el-table-bar height="calc(100vh - 210px)">
                      <el-table
                        :data="fireNetworkData"
                        style="width: 100%; margin-bottom: 20px"
                        row-key="id"
                      >
                        <el-table-column
                          prop="timestamp"
                          label="时间"
                          width="210"
                          :formatter="dateFormat"
                          sortable
                        ></el-table-column>

                        <el-table-column label="级别" width="120" sortable>
                          <template slot-scope="scope">
                            <span v-if="scope.row.severity == 0">紧急</span>
                            <span v-if="scope.row.severity == 1">警报</span>
                            <span v-if="scope.row.severity == 2">严重</span>
                            <span v-if="scope.row.severity == 3">错误</span>
                            <span v-if="scope.row.severity == 4">警告</span>
                            <span v-if="scope.row.severity == 5">通告</span>
                            <span v-if="scope.row.severity == 6">信息</span>
                            <span v-if="scope.row.severity == 7">调试</span>
                            <span v-if="scope.row.severity == 8">>=紧急</span>
                            <span v-if="scope.row.severity == 9">>=警报</span>
                            <span v-if="scope.row.severity == 10">>=严重</span>
                            <span v-if="scope.row.severity == 11">>=错误</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="content"
                          label="消息"
                          show-overflow-tooltip
                        ></el-table-column>
                      </el-table>
                    </el-table-bar>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="配置日志">
                <div class="tab-pane-content">
                  <div class="serch-box clearfix">
                    <div class="filter-container">
                      <el-button
                        v-waves
                        class="filter-item"
                        type="primary"
                        @click="fireConfigureRefresh"
                      >
                        <svg-icon icon-class="refresh" />
                      </el-button>
                      <!-- <el-select
                        v-model="listQueryFireConfigure.date"
                        placeholder="请选择"
                        @change="fireConfigureDateSelct"
                      >
                        <el-option
                          v-for="item in dateOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>-->
                    </div>
                    <div class="page-box">
                      <el-pagination
                        @size-change="handleSizeChangeFireConfigure"
                        @current-change="handleCurrentChangFireConfigure"
                        :current-page="listQueryFireConfigure.currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="sizes, prev,slot, next,total"
                        :total="listQueryFireConfigure.total"
                      >
                        <span class="pageNum">
                          {{ this.listQueryFireConfigure.page }}
                          <i class="divider">/</i>
                          {{ this.listQueryFireConfigure.totalPage }}
                        </span>
                      </el-pagination>
                    </div>
                  </div>
                  <div class="table-box">
                    <el-table-bar height="calc(100vh - 210px)">
                      <el-table
                        :data="fireConfigureData"
                        style="width: 100%; margin-bottom: 20px"
                        row-key="id"
                      >
                        <el-table-column
                          prop="timestamp"
                          label="时间"
                          width="210"
                          :formatter="dateFormat"
                          sortable
                        ></el-table-column>

                        <el-table-column label="级别" width="120" sortable>
                          <template slot-scope="scope">
                            <span v-if="scope.row.severity == 0">紧急</span>
                            <span v-if="scope.row.severity == 1">警报</span>
                            <span v-if="scope.row.severity == 2">严重</span>
                            <span v-if="scope.row.severity == 3">错误</span>
                            <span v-if="scope.row.severity == 4">警告</span>
                            <span v-if="scope.row.severity == 5">通告</span>
                            <span v-if="scope.row.severity == 6">信息</span>
                            <span v-if="scope.row.severity == 7">调试</span>
                            <span v-if="scope.row.severity == 8">>=紧急</span>
                            <span v-if="scope.row.severity == 9">>=警报</span>
                            <span v-if="scope.row.severity == 10">>=严重</span>
                            <span v-if="scope.row.severity == 11">>=错误</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="content"
                          label="消息"
                          show-overflow-tooltip
                        ></el-table-column>
                      </el-table>
                    </el-table-bar>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="威胁日志">
                <div class="tab-pane-content">
                  <div class="serch-box clearfix">
                    <div class="filter-container">
                      <el-button
                        v-waves
                        class="filter-item"
                        type="primary"
                        @click="fireThreatRefresh"
                      >
                        <svg-icon icon-class="refresh" />
                      </el-button>
                      <!-- <el-select
                        v-model="listQueryFireThreat.date"
                        placeholder="请选择"
                        @change="fireThreatDateSelct"
                      >
                        <el-option
                          v-for="item in dateOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>-->
                    </div>
                    <div class="page-box">
                      <el-pagination
                        @size-change="handleSizeChangeFireThreat"
                        @current-change="handleCurrentChangFireThreat"
                        :current-page="listQueryFireThreat.currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="sizes, prev,slot, next,total"
                        :total="listQueryFireThreat.total"
                      >
                        <span class="pageNum">
                          {{ this.listQueryFireThreat.page }}
                          <i class="divider">/</i>
                          {{ this.listQueryFireThreat.totalPage }}
                        </span>
                      </el-pagination>
                    </div>
                  </div>
                  <div class="table-box">
                    <el-table-bar height="calc(100vh - 210px)">
                      <el-table
                        :data="fireThreatData"
                        style="width: 100%; margin-bottom: 20px"
                        row-key="id"
                      >
                        <el-table-column
                          prop="name"
                          label="名称"
                          width="210"
                        ></el-table-column>
                        <el-table-column label="类型">
                          <template slot-scope="scope">
                            {{ scope.row.threatType.name }}-{{
                              scope.row.threatSubtype.name
                            }}
                          </template>
                        </el-table-column>

                        <el-table-column
                          label="级别"
                          align="center"
                          width="100"
                          sortable
                        >
                          <template slot-scope="scope">
                            <div>
                              <span
                                v-if="scope.row.severity == 2"
                                class="bg-level bg-warm"
                                >中</span
                              >
                              <span
                                v-if="scope.row.severity == 4"
                                class="bg-level bg-warn"
                                >严重</span
                              >
                              <span
                                v-if="scope.row.severity == 3"
                                class="bg-level bg-danger"
                                >高</span
                              >
                              <span
                                v-if="scope.row.severity == 1"
                                class="bg-level bg-normal"
                                >低</span
                              >
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                          prop="source.hostName"
                          label="源"
                          show-overflow-tooltip
                        ></el-table-column>
                        <el-table-column
                          prop="destination.hostName"
                          label="目的"
                          show-overflow-tooltip
                        ></el-table-column>
                        <el-table-column
                          prop="protocolApplication"
                          label="应用/协议"
                          show-overflow-tooltip
                        ></el-table-column>

                        <el-table-column
                          prop="endTime"
                          label="结束时间"
                          width="210"
                          :formatter="dateFormat"
                          sortable
                        ></el-table-column>
                        <el-table-column
                          prop="defender.name"
                          label="检测引擎"
                          show-overflow-tooltip
                        ></el-table-column>
                      </el-table>
                    </el-table-bar>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
      <!-- 云堡垒机 -->
      <div v-if="tabNum == 1" class="tab-pane-content">
        <div class="border-card-box">
          <el-tabs type="border-card" @tab-click="handleClickFortress">
            <el-tab-pane label="系统登录日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="fortressLoginRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <!-- <el-date-picker
                v-model="listQueryFortress.date"
                type="date"
                placeholder="选择日期"
               
                class="filter-item"
                    ></el-date-picker>-->

                    <div class="search-container" style="width: 320px">
                      <el-select
                        v-model="listQueryFortress.skeyName"
                        placeholder="请选择"
                        class="loginSelect"
                        @change="loginNameSelect"
                      >
                        <el-option
                          v-for="item in fortressLoginOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                      <el-select
                        v-model="listQueryFortress.logCont"
                        placeholder="请选择"
                        style="width: 140px"
                        class="filter-item"
                        v-if="listQueryFortress.skeyName == 'LOG'"
                        @change="loginLogCSelect"
                      >
                        <el-option
                          v-for="item in fortressLogCOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>

                      <el-input
                        v-model="listQueryFortress.keyword"
                        placeholder="输入搜索项查询"
                        style="width: 140px"
                        class="filter-item"
                        v-on:input="fortressLoginSearch"
                        v-else
                      />
                      <span class="el-icon-search search-btn"></span>
                    </div>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeForess"
                      @current-change="handleCurrentChangeForess"
                      :current-page="listQueryFortress.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryFortress.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryFortress.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryFortress.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar>
                    <el-table
                      :data="fortressData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="time"
                        label="时间"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="name"
                        label="用户"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="ip"
                        label="来源IP"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="log"
                        label="日志内容"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        label="登录方式"
                        show-overflow-tooltip
                        sortable
                      >
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.loginSystemType == 1"
                              >Web页面</span
                            >
                            <span v-if="scope.row.loginSystemType == 2"
                              >SSH客户端</span
                            >
                            <span v-if="scope.row.loginSystemType == 3"
                              >FTP客户端</span
                            >
                            <span v-if="scope.row.loginSystemType == 4"
                              >SFTP客户端</span
                            >
                            <span v-if="scope.row.loginSystemType == 5"
                              >数据库客户端</span
                            >
                            <span v-if="scope.row.loginSystemType == 6"
                              >SCP客户端</span
                            >
                            <span v-if="scope.row.loginSystemType == 7"
                              >APP</span
                            >
                          </div>
                        </template>
                      </el-table-column>

                      <el-table-column
                        label="结果"
                        sortable
                        show-overflow-tooltip
                      >
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.operResult == 1">成功</span>
                            <span v-if="scope.row.operResult == 2">失败</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="备注" show-overflow-tooltip>
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.des == ''">--</span>
                            <span v-if="scope.row.des != ''">{{
                              scope.row.des
                            }}</span>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="系统操作日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="fortressOperRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <!-- <el-date-picker
                v-model="listQueryFortressOper.date"
                type="date"
                placeholder="选择日期"
               
                class="filter-item"
                    ></el-date-picker>-->

                    <div class="search-container" style="width: 320px">
                      <el-select
                        v-model="listQueryFortressOper.skeyName"
                        placeholder="请选择"
                        class="loginSelect"
                        @change="operNameSelect"
                      >
                        <el-option
                          v-for="item in fortressLoginOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>

                      <el-input
                        v-model="listQueryFortressOper.keyword"
                        placeholder="输入搜索项查询"
                        style="width: 140px"
                        class="filter-item"
                        v-on:input="fortressOperSearch"
                      />
                      <span class="el-icon-search search-btn"></span>
                    </div>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeForessOper"
                      @current-change="handleCurrentChangeForessOper"
                      :current-page="listQueryFortressOper.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryFortressOper.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryFortressOper.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryFortressOper.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar>
                    <el-table
                      :data="fortressOperData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="time"
                        label="时间"
                        sortable
                        show-overflow-tooltip
                        width="230"
                      ></el-table-column>
                      <el-table-column
                        prop="name"
                        label="用户"
                        show-overflow-tooltip
                        width="160"
                      ></el-table-column>

                      <el-table-column
                        prop="ip"
                        label="来源IP"
                        show-overflow-tooltip
                        width="160"
                      ></el-table-column>
                      <el-table-column
                        label="模块"
                        sortable
                        show-overflow-tooltip
                        width="160"
                      >
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.module == 1">部门</span>
                            <span v-if="scope.row.module == 2">用户</span>
                            <span v-if="scope.row.module == 3">资源</span>
                            <span v-if="scope.row.module == 4">运维</span>
                            <span v-if="scope.row.module == 5">策略</span>
                            <span v-if="scope.row.module == 6">审计</span>
                            <span v-if="scope.row.module == 7">工单</span>
                            <span v-if="scope.row.module == 8">系统</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="log"
                        label="日志内容"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        label="结果"
                        sortable
                        show-overflow-tooltip
                        width="120"
                      >
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.operResult == 1">成功</span>
                            <span v-if="scope.row.operResult == 2">失败</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="备注"
                        show-overflow-tooltip
                        width="230"
                      >
                        <template slot-scope="scope">
                          <div>
                            <span v-if="scope.row.des == ''">--</span>
                            <span v-if="scope.row.des != ''">{{
                              scope.row.des
                            }}</span>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <!-- 云主机安全 -->
      <div v-if="tabNum == 2" class="tab-pane-content">
        <div class="border-card-box">
          <el-tabs type="border-card" @tab-click="handleClick">
            <el-tab-pane label="防火墙 / 网络管理日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="firewallRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="listQueryFirewall.period"
                      type="daterange"
                      :default-time="['00:00:00', '23:59:59']"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="fireWallChange"
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <!-- <el-pagination
                      @size-change="handleSizeChangeVirus"
                      @current-change="handleCurrentChangeVirus"
                      :current-page="listQueryFirewall.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryFirewall.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryFirewall.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryFirewall.totalPage }}
                      </span>
                    </el-pagination> -->
                    <button
                      type="button"
                      class="prev-btn page-btn"
                      @click="prevVirus()"
                      :class="previrus ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-left"></i>
                    </button>
                    <button
                      type="button"
                      class="next-btn page-btn"
                      @click="nextVirus()"
                      :class="nextvirus ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-right"></i>
                    </button>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="fireWallData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="0"
                        label="时间"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="1"
                        label="虚拟机/终端"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="2"
                        label="IP地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="3"
                        label="分组"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="4"
                        label="安全配置"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="5"
                        label="网络协议"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="6"
                        label="源地址"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="入侵防御日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="hostrefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="listQueryHostFire.period"
                      type="daterange"
                      :default-time="['00:00:00', '23:59:59']"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="fireWallChange"
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <!-- <el-pagination
                      @size-change="handleSizeChangeHost"
                      @current-change="handleCurrentChangeHost"
                      :current-page="listQueryHostFire.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryHostFire.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryHostFire.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryHostFire.totalPage }}
                      </span>
                    </el-pagination> -->
                    <button
                      type="button"
                      class="prev-btn page-btn"
                      @click="prevHost()"
                      :class="preHostDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-left"></i>
                    </button>
                    <button
                      type="button"
                      class="next-btn page-btn"
                      @click="nextHost()"
                      :class="nextHostDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-right"></i>
                    </button>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="hostFireData"
                      style="width: 100%; margin-bottom: 20px"
                    >
                      <el-table-column
                        prop="0"
                        label="时间"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="1"
                        label="虚拟机/终端"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="2"
                        label="IP地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="3"
                        label="分组"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="4"
                        label="安全配置"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="5"
                        label="攻击类型"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="6"
                        label="规则信息"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="7"
                        label="严重性"
                        sortable
                        show-overflow-tooltip
                      >
                      </el-table-column>

                      <el-table-column
                        prop="8"
                        label="源地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="9"
                        label="目的地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="10"
                        label="动作"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="防暴力破解日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="preFCrefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="listQueryPreFC.period"
                      type="daterange"
                      :default-time="['00:00:00', '23:59:59']"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="fireWallChange"
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <!-- <el-pagination
                      @size-change="handleSizeChangePreFC"
                      @current-change="handleCurrentChangePreFC"
                      :current-page="listQueryPreFC.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryPreFC.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryPreFC.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryPreFC.totalPage }}
                      </span>
                    </el-pagination> -->
                    <button
                      type="button"
                      class="prev-btn page-btn"
                      @click="prevPreFc()"
                      :class="prePreFcDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-left"></i>
                    </button>
                    <button
                      type="button"
                      class="next-btn page-btn"
                      @click="nextPreFc()"
                      :class="nextPreFcDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-right"></i>
                    </button>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="preFcData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="0"
                        label="攻击时间"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="1"
                        label="主机池/项目"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="2"
                        label="虚拟机/终端"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="3"
                        label="IP地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="4"
                        label="分组"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="5"
                        label="安全配置"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="6"
                        label="协议类型"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="7"
                        label="威胁来源"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="8"
                        label="攻击次数"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="防恶意软件">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="webShellrefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="listQueryShell.period"
                      type="daterange"
                      :default-time="['00:00:00', '23:59:59']"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="fireWallChange"
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <!-- <el-pagination
                      @size-change="handleSizeChangeShell"
                      @current-change="handleCurrentChangeShell"
                      :current-page="listQueryShell.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryShell.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryShell.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryShell.totalPage }}
                      </span>
                    </el-pagination> -->
                    <button
                      type="button"
                      class="prev-btn page-btn"
                      @click="prevShell()"
                      :class="preShellDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-left"></i>
                    </button>
                    <button
                      type="button"
                      class="next-btn page-btn"
                      @click="nextShell()"
                      :class="nextShellDis ? 'disabled' : ''"
                    >
                      <i class="el-icon el-icon-caret-right"></i>
                    </button>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="webShellData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="0"
                        label="时间"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="1"
                        label="虚拟机/终端"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="2"
                        label="IP地址"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="3"
                        label="分组"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="4"
                        label="安全配置"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="5"
                        label="恶意软件"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="6"
                        label="恶意软件名"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="7"
                        label="文件名"
                        sortable
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <!-- 云数据库审计 -->
      <div v-if="tabNum == 3" class="tab-pane-content">
        <div class="tab-pane-content">
          <div class="serch-box clearfix">
            <div class="filter-container">
              <el-button
                v-waves
                class="filter-item"
                type="primary"
                @click="databaseRefresh"
              >
                <svg-icon icon-class="refresh" />
              </el-button>
              <el-date-picker
                v-model="listQueryDatabase.date"
                type="daterange"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                v-on:input="databaseChange"
                class="filter-item"
              ></el-date-picker>
              <!-- 
              <div class="search-container">
                <el-input
                  v-model="listQuery.title"
                  placeholder="用户/来源IP/日志内容"
                  style="width: 200px;"
                  class="filter-item"
                />
                <span class="el-icon-search search-btn"></span>
              </div>-->
            </div>
            <div class="page-box">
              <el-pagination
                @size-change="handleSizeChangeDatabase"
                @current-change="handleCurrentChangeDatabase"
                :current-page="listQueryDatabase.currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="sizes, prev,slot, next,total"
                :total="listQueryDatabase.total"
              >
                <span class="pageNum">
                  {{ this.listQueryDatabase.page }}
                  <i class="divider">/</i>
                  {{ this.listQueryDatabase.totalPage }}
                </span>
              </el-pagination>
            </div>
          </div>
          <div class="table-box">
            <el-table-bar>
              <el-table
                :data="databaseData"
                style="width: 100%; margin-bottom: 20px"
                row-key="id"
              >
                <el-table-column label="用户">
                  <template slot-scope="scope">
                    <div>
                      <span v-if="scope.row.userName == null">--</span>
                      <span v-else>{{ scope.row.userName }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="IP">
                  <template slot-scope="scope">
                    <div>
                      <span v-if="scope.row.ip == null">--</span>
                      <span v-else>{{ scope.row.ip }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="time"
                  label="时间"
                  sortable
                ></el-table-column>

                <el-table-column
                  prop="action"
                  label="操作"
                  sortable
                ></el-table-column>

                <el-table-column prop="content" label="内容"></el-table-column>
                <el-table-column label="结果">
                  <template slot-scope="scope">
                    <div>
                      <span v-if="scope.row.result == true">成功</span>
                      <span v-if="scope.row.result == false">失败</span>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="详情" width="100" align="center">
                  <template slot-scope="scope">
                    <el-dropdown>
                      <span class="el-dropdown-link">
                        <i class="el-icon-more" style="transform:rotate(90deg)"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <div @click="handleInfoClick(scope.row)" class="opt">详情</div>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>-->
              </el-table>
            </el-table-bar>
          </div>
        </div>
      </div>
      <!-- 云WAF -->
      <div v-if="tabNum == 4" class="tab-pane-content">
        <div class="border-card-box">
          <el-tabs type="border-card" @tab-click="handleClickWaf">
            <el-tab-pane label="事件日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="wafEventRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-select
                      v-model="listQueryWafevent.date"
                      placeholder="请选择"
                      @change="wafEventDateSelct"
                    >
                      <el-option
                        v-for="item in dateOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeWafevent"
                      @current-change="handleCurrentChangeWafevent"
                      :current-page="listQueryWafevent.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryWafevent.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryWafevent.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryWafevent.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="wafeventData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="timestamp"
                        label="时间"
                        width="210"
                        :formatter="dateFormat"
                        sortable
                      ></el-table-column>

                      <el-table-column label="级别" width="120" sortable>
                        <template slot-scope="scope">
                          <span v-if="scope.row.severity == 0">紧急</span>
                          <span v-if="scope.row.severity == 1">警报</span>
                          <span v-if="scope.row.severity == 2">严重</span>
                          <span v-if="scope.row.severity == 3">错误</span>
                          <span v-if="scope.row.severity == 4">警告</span>
                          <span v-if="scope.row.severity == 5">通告</span>
                          <span v-if="scope.row.severity == 6">信息</span>
                          <span v-if="scope.row.severity == 7">调试</span>
                          <span v-if="scope.row.severity == 8">>=紧急</span>
                          <span v-if="scope.row.severity == 9">>=警报</span>
                          <span v-if="scope.row.severity == 10">>=严重</span>
                          <span v-if="scope.row.severity == 11">>=错误</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="content"
                        label="消息"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="网络日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="wafNetworkRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-select
                      v-model="listQueryWafnetwork.date"
                      placeholder="请选择"
                      @change="wafNetworkDateSelct"
                    >
                      <el-option
                        v-for="item in dateOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeWafNetwork"
                      @current-change="handleCurrentChangeWafNetwork"
                      :current-page="listQueryWafnetwork.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryWafnetwork.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryWafnetwork.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryWafnetwork.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="networkData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="timestamp"
                        label="时间"
                        width="210"
                        :formatter="dateFormat"
                        sortable
                      ></el-table-column>

                      <el-table-column label="级别" width="120" sortable>
                        <template slot-scope="scope">
                          <span v-if="scope.row.severity == 0">紧急</span>
                          <span v-if="scope.row.severity == 1">警报</span>
                          <span v-if="scope.row.severity == 2">严重</span>
                          <span v-if="scope.row.severity == 3">错误</span>
                          <span v-if="scope.row.severity == 4">警告</span>
                          <span v-if="scope.row.severity == 5">通告</span>
                          <span v-if="scope.row.severity == 6">信息</span>
                          <span v-if="scope.row.severity == 7">调试</span>
                          <span v-if="scope.row.severity == 8">>=紧急</span>
                          <span v-if="scope.row.severity == 9">>=警报</span>
                          <span v-if="scope.row.severity == 10">>=严重</span>
                          <span v-if="scope.row.severity == 11">>=错误</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="content"
                        label="消息"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
              >
            </el-tab-pane>
            <el-tab-pane label="配置日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="wafConfigurationRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-select
                      v-model="listQueryWafConfiguration.date"
                      placeholder="请选择"
                      @change="wafConfigurationDateSelct"
                    >
                      <el-option
                        v-for="item in dateOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeWafConfiguration"
                      @current-change="handleCurrentChangeWafConfiguration"
                      :current-page="listQueryWafConfiguration.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryWafConfiguration.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryWafConfiguration.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryWafConfiguration.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="configurationData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="timestamp"
                        label="时间"
                        width="210"
                        :formatter="dateFormat"
                        sortable
                      ></el-table-column>

                      <el-table-column label="级别" width="120" sortable>
                        <template slot-scope="scope">
                          <span v-if="scope.row.severity == 0">紧急</span>
                          <span v-if="scope.row.severity == 1">警报</span>
                          <span v-if="scope.row.severity == 2">严重</span>
                          <span v-if="scope.row.severity == 3">错误</span>
                          <span v-if="scope.row.severity == 4">警告</span>
                          <span v-if="scope.row.severity == 5">通告</span>
                          <span v-if="scope.row.severity == 6">信息</span>
                          <span v-if="scope.row.severity == 7">调试</span>
                          <span v-if="scope.row.severity == 8">>=紧急</span>
                          <span v-if="scope.row.severity == 9">>=警报</span>
                          <span v-if="scope.row.severity == 10">>=严重</span>
                          <span v-if="scope.row.severity == 11">>=错误</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="content"
                        label="消息"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="网络安全日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button
                      v-waves
                      class="filter-item"
                      type="primary"
                      @click="wafNetworkSecurityRefresh"
                    >
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-select
                      v-model="listQueryWafNetworkSecurity.date"
                      placeholder="请选择"
                      @change="wafNetworkSecurityDateSelct"
                    >
                      <el-option
                        v-for="item in dateOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChangeWafNetworkSecurity"
                      @current-change="handleCurrentChangeWafNetworkSecurity"
                      :current-page="listQueryWafNetworkSecurity.currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="listQueryWafNetworkSecurity.total"
                    >
                      <span class="pageNum">
                        {{ this.listQueryWafNetworkSecurity.page }}
                        <i class="divider">/</i>
                        {{ this.listQueryWafNetworkSecurity.totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="networkSecurityData"
                      style="width: 100%; margin-bottom: 20px"
                      row-key="id"
                    >
                      <el-table-column
                        prop="name"
                        label="名称"
                        width="210"
                      ></el-table-column>
                      <el-table-column label="类型">
                        <template slot-scope="scope">
                          {{ scope.row.threatType.name }}-{{
                            scope.row.threatSubtype.name
                          }}
                        </template>
                      </el-table-column>

                      <el-table-column
                        label="级别"
                        align="center"
                        width="100"
                        sortable
                      >
                        <template slot-scope="scope">
                          <div>
                            <span
                              v-if="scope.row.severity == 2"
                              class="bg-level bg-warm"
                              >中</span
                            >
                            <span
                              v-if="scope.row.severity == 4"
                              class="bg-level bg-warn"
                              >严重</span
                            >
                            <span
                              v-if="scope.row.severity == 3"
                              class="bg-level bg-danger"
                              >高</span
                            >
                            <span
                              v-if="scope.row.severity == 1"
                              class="bg-level bg-normal"
                              >低</span
                            >
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="source.hostName"
                        label="源"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="destination.hostName"
                        label="目的"
                        show-overflow-tooltip
                      ></el-table-column>

                      <el-table-column
                        prop="endTime"
                        label="结束时间"
                        width="210"
                        :formatter="dateFormat"
                        sortable
                      ></el-table-column>
                      <el-table-column
                        prop="defender.name"
                        label="检测引擎"
                        show-overflow-tooltip
                      ></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane label="IP防护日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button v-waves class="filter-item" type="primary">
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="value1"
                      type="date"
                      placeholder="选择日期"
                   
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="total"
                    >
                      <span class="pageNum">
                        {{ this.listQuery.page }}
                        <i class="divider">/</i>
                        {{ totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table :data="ipData" style="width: 100%;margin-bottom:20px;" row-key="id">
                      <el-table-column prop="time" label="时间" sortable width="160"></el-table-column>

                      <el-table-column prop="clientIP" label="客户端IP" width="120"></el-table-column>
                      <el-table-column prop="serverIP" label="服务器IP"></el-table-column>
                      <el-table-column prop="siteName" label="站点名称"></el-table-column>
                      <el-table-column prop="strategyName" label="策略名称"></el-table-column>
                      <el-table-column prop="ProtectiveAction" label="防护动作"></el-table-column>
                      <el-table-column prop="city" label="国家/地区"></el-table-column>
                      <el-table-column prop="method" label="HTTP请求方法"></el-table-column>
                      <el-table-column prop="domainName" label="域名"></el-table-column>
                      <el-table-column prop="url" label="URL"></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="网页安全日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button v-waves class="filter-item" type="primary">
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="value1"
                      type="date"
                      placeholder="选择日期"
                    
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="total"
                    >
                      <span class="pageNum">
                        {{ this.listQuery.page }}
                        <i class="divider">/</i>
                        {{ totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="webSecurityData"
                      style="width: 100%;margin-bottom:20px;"
                      row-key="id"
                    >
                      <el-table-column prop="time" label="时间" sortable width="160"></el-table-column>
                      <el-table-column prop="alarmLevel" label="告警级别" sortable width="160"></el-table-column>

                      <el-table-column prop="clientIP" label="客户端IP" width="120"></el-table-column>
                      <el-table-column prop="serverIP" label="服务器IP"></el-table-column>
                      <el-table-column prop="protectionType" label="防护类型"></el-table-column>
                      <el-table-column prop="protectionSubType" label="防护子类型"></el-table-column>
                      <el-table-column prop="ruleId" label="规则ID"></el-table-column>
                      <el-table-column prop="siteName" label="站点名称"></el-table-column>
                      <el-table-column prop="strategyName" label="策略名称"></el-table-column>
                      <el-table-column prop="ProtectiveAction" label="防护动作"></el-table-column>

                      <el-table-column prop="domainName" label="域名"></el-table-column>
                      <el-table-column prop="user" label="用户名"></el-table-column>
                      <el-table-column prop="sessionID" label="会话标识名称"></el-table-column>

                      <el-table-column prop="sessionValue" label="会话标识值"></el-table-column>
                      <el-table-column prop="statusCode" label="服务器状态码"></el-table-column>
                      <el-table-column prop="url" label="URL"></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="防篡改日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button v-waves class="filter-item" type="primary">
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="value1"
                      type="date"
                      placeholder="选择日期"
                      
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="total"
                    >
                      <span class="pageNum">
                        {{ this.listQuery.page }}
                        <i class="divider">/</i>
                        {{ totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="tamperProofData"
                      style="width: 100%;margin-bottom:20px;"
                      row-key="id"
                    >
                      <el-table-column prop="time" label="时间" sortable width="160"></el-table-column>
                      <el-table-column prop="siteName" label="站点名称"></el-table-column>
                      <el-table-column prop="url" label="URL" width="120"></el-table-column>
                      <el-table-column prop="modification" label="修改方式"></el-table-column>
                      <el-table-column prop="result" label="裁决结果"></el-table-column>
                      <el-table-column prop="before" label="变更前"></el-table-column>
                      <el-table-column prop="after" label="变更后"></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="访问控制日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button v-waves class="filter-item" type="primary">
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="value1"
                      type="date"
                      placeholder="选择日期"
                      class="filter-item"
                     
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="total"
                    >
                      <span class="pageNum">
                        {{ this.listQuery.page }}
                        <i class="divider">/</i>
                        {{ totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="accessControlData"
                      style="width: 100%;margin-bottom:20px;"
                      row-key="id"
                    >
                      <el-table-column prop="time" label="时间" sortable></el-table-column>

                      <el-table-column prop="clientIP" label="客户端IP"></el-table-column>
                      <el-table-column prop="serverIP" label="服务器IP"></el-table-column>

                      <el-table-column prop="siteName" label="站点名称"></el-table-column>
                      <el-table-column prop="strategyName" label="策略名称"></el-table-column>
                      <el-table-column prop="ProtectiveAction" label="防护动作"></el-table-column>
                      <el-table-column prop="method" label="HTTP请求方法"></el-table-column>
                      <el-table-column prop="domainName" label="域名"></el-table-column>
                      <el-table-column prop="user" label="用户名"></el-table-column>
                      <el-table-column prop="sessionID" label="会话标识名称"></el-table-column>

                      <el-table-column prop="sessionValue" label="会话标识值"></el-table-column>

                      <el-table-column prop="url" label="URL"></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="自学习模型违背日志">
              <div class="tab-pane-content">
                <div class="serch-box clearfix">
                  <div class="filter-container">
                    <el-button v-waves class="filter-item" type="primary">
                      <svg-icon icon-class="refresh" />
                    </el-button>
                    <el-date-picker
                      v-model="value1"
                      type="date"
                      placeholder="选择日期"
                    
                      class="filter-item"
                    ></el-date-picker>
                  </div>
                  <div class="page-box">
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="currentPage"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="sizes, prev,slot, next,total"
                      :total="total"
                    >
                      <span class="pageNum">
                        {{ this.listQuery.page }}
                        <i class="divider">/</i>
                        {{ totalPage }}
                      </span>
                    </el-pagination>
                  </div>
                </div>
                <div class="table-box">
                  <el-table-bar height="calc(100vh - 210px)">
                    <el-table
                      :data="modelData"
                      style="width: 100%;margin-bottom:20px;"
                      row-key="id"
                    >
                      <el-table-column prop="time" label="时间" sortable></el-table-column>
                      <el-table-column prop="alarmLevel" label="告警级别" sortable></el-table-column>

                      <el-table-column prop="clientIP" label="客户端IP"></el-table-column>
                      <el-table-column prop="serverIP" label="服务器IP"></el-table-column>

                      <el-table-column prop="siteName" label="站点名称"></el-table-column>
                      <el-table-column prop="strategyName" label="规则名称"></el-table-column>
                      <el-table-column prop="ProtectiveAction" label="防护动作"></el-table-column>

                      <el-table-column prop="domainName" label="域名"></el-table-column>
                      <el-table-column prop="user" label="用户名"></el-table-column>
                      <el-table-column prop="sessionID" label="会话标识名称"></el-table-column>

                      <el-table-column prop="sessionValue" label="会话标识值"></el-table-column>
                      <el-table-column prop="method" label="HTTP请求方法"></el-table-column>
                      <el-table-column prop="statusCode" label="服务器状态码"></el-table-column>
                      <el-table-column prop="url" label="URL"></el-table-column>
                    </el-table>
                  </el-table-bar>
                </div>
              </div>
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
      <!-- 云日志审计 -->
      <!-- <div v-if="tabNum == 5" class="tab-pane-content">
        <div class="tab-pane-content">
          <div class="serch-box clearfix">
            <div class="filter-container">
              <el-button v-waves class="filter-item" type="primary">
                <svg-icon icon-class="refresh" />
              </el-button>
              <el-date-picker
                v-model="value1"
                type="date"
                placeholder="选择日期"
               
                class="filter-item"
              ></el-date-picker>
            </div>
            <div class="page-box">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="sizes, prev,slot, next,total"
                :total="10"
              >
                <span class="pageNum">
                  {{ this.listQuery.page }}
                  <i class="divider">/</i>
                  {{ totalPage }}
                </span>
              </el-pagination>
            </div>
          </div>
          <div class="table-box">
            <el-table-bar>
              <el-table :data="logAuditData" style="width: 100%;margin-bottom:20px;" row-key="id">
                <el-table-column prop="time" label="接收时间" sortable></el-table-column>
                <el-table-column prop="type" label="类型" sortable></el-table-column>
                <el-table-column prop="device" label="设备" show-overflow-tooltip></el-table-column>
                <el-table-column prop="level" label="级别" show-overflow-tooltip></el-table-column>
                <el-table-column prop="content" label="内容" show-overflow-tooltip></el-table-column>
              </el-table>
            </el-table-bar>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { Loading } from "element-ui";
import { mapGetters } from "vuex";

import moment from "moment"; //时间格式转化
import {
  loginLog,
  operLog,
  database,
  ipsRaw,
  fireWallLog,
  preventForceCrack,
  webShellLog,
  systemLog,
  threatLog,
  wafsystemLog,
  wafNetworkSecurity,
  wafdIpReputation,
} from "@/api/log.js";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tab: [
        "云防火墙",
        "云堡垒机",
        "云主机安全",
        "云数据库审计",
        // "云Web应用防火墙",
        // "云日志审计",
      ],
      hostTabItem: "",
      dateOptions: [
        {
          value: "hour",
          label: "最近一小时",
        },
        {
          value: "day",
          label: "最近一天",
        },
        {
          value: "month",
          label: "最近一月",
        },
      ],
      fortressLoginOptions: [
        {
          value: "NAME",
          label: "用户",
        },
        {
          value: "LOG",
          label: "日志内容",
        },
        {
          value: "IP",
          label: "来源IP",
        },
      ],
      fortressLogCOptions: [
        {
          value: "1",
          label: "登录系统",
        },
        {
          value: "2",
          label: "退出系统",
        },
      ],
      tabNum: 0,
      fortressData: [], //云堡垒机系统登录日志
      fortressOperData: [], //云堡垒机系统操作日志
      account: "",
      currentPage: 1,
      total: 0,
      totalPage: 1,
      value1: "",
      listQuery: {
        page: 1,
        limit: 10,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+id",
      },
      listQueryFortress: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
        skeyName: "用户",
        skey: "",
        sValue: "",
        keyword: "",
        logCont: "",
      },
      listQueryFortressOper: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
        skeyName: "用户",
        skey: "",
        sValue: "",
        keyword: "",
        logCont: "",
      },
      listQueryFireevent: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryFirenetWork: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryFireConfigure: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryFireThreat: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryFirewall: {
        page: 1,
        limit: 50,
        date: "",
        period: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryHostFire: {
        page: 1,
        limit: 50,
        date: "",
        period: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryPreFC: {
        page: 1,
        limit: 50,
        date: "",
        period: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryShell: {
        page: 1,
        limit: 50,
        date: "",
        period: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryWafevent: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryWafnetwork: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryWafConfiguration: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryWafNetworkSecurity: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
      },
      listQueryDatabase: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
        startTime: "",
        endTime: "",
      },
      listLoading: false,
      downloadLoading: false,
      formLabelWidth: "120px",
      fireWallData: [], //云主机安全---防火墙、网络管理日志
      hostFireData: [], //云主机安全---入侵防御日志
      preFcData: [], //云主机安全---防暴力破解日志
      webShellData: [], //云主机安全---防恶意软件
      fireEventData: [], //云防火墙事件日志
      fireNetworkData: [], //云防火墙网络日志
      fireConfigureData: [], //云防火墙配置日志
      fireThreatData: [], //云防火墙威胁日志
      databaseData: [],
      wafeventData: [], //云WAF事件日志
      networkData: [], //云WAF网络日志
      configurationData: [], //云WAF配置日
      networkSecurityData: [], //云WAF网络安全日志
      ipData: [],
      webSecurityData: [],
      tamperProofData: [],
      accessControlData: [],
      modelData: [],
      logAuditData: [
        {
          id: 1,
          time: "2020-07-03 08:29:39",
          type: "事件日志",
          device: "172-12-200-33(*************)",
          level: "1",
          content: "",
        },
        {
          id: 2,
          time: "2020-07-03 08:29:39",
          device: "0010021629358539(*************)",
          type: "告警日志",
          level: "2",
          content: "",
        },
        {
          id: 3,
          time: "2020-07-03 08:29:39",
          device: "locahost(*************)",
          type: "网络日志",
          level: "3",
          content: "",
        },
        {
          id: 4,
          time: "2020-07-03 08:29:39",
          device: "hsmp(*************)",
          type: "配置",
          level: "4",
          content: "",
        },
        {
          id: 5,
          time: "2020-07-03 08:29:39",
          device: "*************",
          type: "IPS",
          level: "5",
          content: "",
        },
        {
          id: 6,
          time: "2020-07-03 08:29:39",
          device: "001002162935839(*************)",
          type: "安全日志",
          level: "6",
          content: "",
        },
        {
          id: 7,
          time: "2020-07-03 08:29:39",
          device: "001002162935839(*************)",
          type: "威胁日志",
          level: "7",
          content: "",
        },
        {
          id: 8,
          time: "2020-07-03 08:29:39",
          device: "001002162935839(*************)",
          type: "网络安全日志",
          level: "8",
          content: "",
        },
        {
          id: 9,
          time: "2020-07-03 08:29:39",
          device: "001002162935839(*************)",
          type: "防篡改日志",
          level: "9",
          content: "",
        },
        {
          id: 10,
          time: "2020-07-03 08:29:39",
          device: "001002162935839(*************)",
          type: "URL",
          level: "10",
          content: "",
        },
      ],
      previrus: false,
      nextvirus: false,
      preHostDis: false,
      nextHostDis: false,
      prePreFcDis: false,
      nextPreFcDis: false,
      preShellDis: false,
      nextShellDis: false,
    };
  },
  created() {
    this.getData();
    this.getFireevnetData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  filters: {
    numFilter(value) {
      // 截取当前数据到小数点后两位--四舍五入 ---时间戳转换
      let realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
  },
  methods: {
    dateFormat(row, column) {
      var moment = require("moment");
      var date = row[column.property];
      return moment(date).format("YYYY-MM-DD hh:mm:ss");
    },
    getData() {},
    //云防火墙---事件日志
    getFireevnetData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryFireevent.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryFireevent.page,
        limit: this.listQueryFireevent.limit,
        extraParams: 0, // 0:事件日志，6:网络日志，2：配置日志，
        userId: this.userid,
      };
      systemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.fireEventData = res.data.result;
            this.listQueryFireevent.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryFireevent.totalPage = 1;
            } else {
              this.listQueryFireevent.totalPage = Math.ceil(
                this.listQueryFireevent.total / this.listQueryFireevent.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云防火墙---网络日志
    getFirenetWorkData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryFirenetWork.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryFirenetWork.page,
        limit: this.listQueryFirenetWork.limit,
        extraParams: 6, // 0:事件日志，6:网络日志，2：配置日志，
      };
      systemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.fireNetworkData = res.data.result;
            this.listQueryFirenetWork.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryFirenetWork.totalPage = 1;
            } else {
              this.listQueryFirenetWork.totalPage = Math.ceil(
                this.listQueryFirenetWork.total /
                  this.listQueryFirenetWork.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云防火墙---配置日志
    getFireConfigureData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryFireConfigure.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryFireConfigure.page,
        limit: this.listQueryFireConfigure.limit,
        extraParams: 2, // 0:事件日志，6:网络日志，2：配置日志，
      };
      systemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.fireConfigureData = res.data.result;
            this.listQueryFireConfigure.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryFireConfigure.totalPage = 1;
            } else {
              this.listQueryFireConfigure.totalPage = Math.ceil(
                this.listQueryFireConfigure.total /
                  this.listQueryFireConfigure.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云防火墙---威胁日志
    getFireThreatData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryFireThreat.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryFireThreat.page,
        limit: this.listQueryFireThreat.limit,
        userId: this.userid,
      };
      threatLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.fireThreatData = res.data.result;
            this.listQueryFireThreat.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryFireThreat.totalPage = 1;
            } else {
              this.listQueryFireThreat.totalPage = Math.ceil(
                this.listQueryFireThreat.total / this.listQueryFireThreat.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云waf---事件日志

    getWafevnetData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryWafevent.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryWafevent.page,
        limit: this.listQueryWafevent.limit,
        extraParams: 0, // 0:事件日志，6:网络日志，2：配置日志，
        userId: this.userid,
      };
      wafsystemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            if (res.data.success == false) {
              this.listQueryWafevent.totalPage = 1;
            } else {
              this.wafeventData = res.data.result;
              this.listQueryWafevent.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryWafevent.totalPage = 1;
              } else {
                this.listQueryWafevent.totalPage = Math.ceil(
                  this.listQueryWafevent.total / this.listQueryWafevent.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云waf---网络日志
    getWafNetworkData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryWafnetwork.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryWafnetwork.page,
        limit: this.listQueryWafnetwork.limit,
        extraParams: 6, // 0:事件日志，6:网络日志，2：配置日志，
        userId: this.userid,
      };
      wafsystemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            if (res.data.success == false) {
              this.listQueryWafnetwork.totalPage = 1;
            } else {
              this.networkData = res.data.result;
              this.listQueryWafnetwork.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryWafnetwork.totalPage = 1;
              } else {
                this.listQueryWafnetwork.totalPage = Math.ceil(
                  this.listQueryWafnetwork.total /
                    this.listQueryWafnetwork.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云waf---配置日志
    getWafConfigurationData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryWafConfiguration.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryWafConfiguration.page,
        limit: this.listQueryWafConfiguration.limit,
        extraParams: 2, // 0:事件日志，6:网络日志，2：配置日志，
        userId: this.userid,
      };
      wafsystemLog(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            if (res.data.success == false) {
              this.listQueryWafConfiguration.totalPage = 1;
            } else {
              this.configurationData = res.data.result;
              this.listQueryWafConfiguration.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryWafConfiguration.totalPage = 1;
              } else {
                this.listQueryWafConfiguration.totalPage = Math.ceil(
                  this.listQueryWafConfiguration.total /
                    this.listQueryWafConfiguration.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云waf---网络安全日志
    getWafNetworkSecurityData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let systemData = {
        date: this.listQueryWafNetworkSecurity.date, //“hour”（最近一小时）；“day”（最近一天）；“month”（最近一月）。
        page: this.listQueryWafNetworkSecurity.page,
        limit: this.listQueryWafNetworkSecurity.limit,
        userId: this.userid,
      };
      wafNetworkSecurity(systemData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            if (res.data.success == false) {
              this.listQueryWafNetworkSecurity.totalPage = 1;
            } else {
              this.networkSecurityData = res.data.result;
              this.listQueryWafNetworkSecurity.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryWafNetworkSecurity.totalPage = 1;
              } else {
                this.listQueryWafNetworkSecurity.totalPage = Math.ceil(
                  this.listQueryWafNetworkSecurity.total /
                    this.listQueryWafNetworkSecurity.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },

    //云主机防火墙 / 网络管理
    getFirewallData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let firewallData = {
        limit: this.listQueryFirewall.limit,
        page: this.listQueryFirewall.page,
        date: this.listQueryFirewall.date,
        userId: this.userid,
      };
      console.log(firewallData);
      fireWallLog(firewallData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.fireWallData = res.data.results;
            this.listQueryFirewall.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryFirewall.totalPage = 1;
            } else {
              this.listQueryFirewall.totalPage = Math.ceil(
                this.listQueryFirewall.total / this.listQueryFirewall.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },

    //云主机安全入侵防御日志

    getHostData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let hostData = {
        limit: this.listQueryHostFire.limit,
        page: this.listQueryHostFire.page,
        date: this.listQueryHostFire.date,
        userId: this.userid,
      };
      ipsRaw(hostData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.hostFireData = res.data.results;
            this.listQueryHostFire.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryHostFire.totalPage = 1;
            } else {
              this.listQueryHostFire.totalPage = Math.ceil(
                this.listQueryHostFire.total / this.listQueryHostFire.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云主机安全防暴力破解日志
    getPreFCData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let preFcData = {
        limit: this.listQueryPreFC.limit,
        page: this.listQueryPreFC.page,
        date: this.listQueryPreFC.date,
        userId: this.userid,
      };
      console.log(preFcData);
      preventForceCrack(preFcData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            if (JSON.stringify(res.data) != "{}") {
              this.preFcData = res.data.results;
              this.listQueryPreFC.total = res.data.total;
              if (res.data.total == 0) {
                this.listQueryPreFC.totalPage = 1;
              } else {
                this.listQueryPreFC.totalPage = Math.ceil(
                  this.listQueryPreFC.total / this.listQueryPreFC.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云主机安全webShell
    getwebShellData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let shellData = {
        limit: this.listQueryShell.limit,
        page: this.listQueryShell.page,
        date: this.listQueryShell.date,
        userId: this.userid,
      };
      console.log(shellData);
      webShellLog(shellData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.webShellData = res.data.results;
            this.listQueryShell.total = res.data.total;
            if (res.data.total == 0) {
              this.listQueryShell.totalPage = 1;
            } else {
              this.listQueryShell.totalPage = Math.ceil(
                this.listQueryShell.total / this.listQueryShell.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云堡垒机--登录日志
    getFortressData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (this.listQueryFortress.skey == "") {
        if (this.listQueryFortress.skeyName == "用户") {
          this.listQueryFortress.skey = "NAME";
          this.listQueryFortress.keyword = "";
        } else if (this.listQueryFortress.skeyName == "来源IP") {
          this.listQueryFortress.skey = "IP";
          this.listQueryFortress.keyword = "";
        } else if (this.listQueryFortress.skeyName == "日志内容") {
          this.listQueryFortress.skey = "LOG";
          this.listQueryFortress.keyword = "";
        }
      }
      console.log(this.listQueryFortress.skey);
      console.log(this.listQueryFortress.keyword);

      let fortressData = {
        pageSize: this.listQueryFortress.limit,
        page: this.listQueryFortress.page,
        sKey: this.listQueryFortress.skey,
        sValue: this.listQueryFortress.keyword,
        userId: this.userid,
      };
      console.log(fortressData);
      loginLog(fortressData)
        .then((res) => {
          console.log(res);
          if (res.ok == true) {
            this.listLoading.close();
            if (res.obj != null) {
              this.fortressData = res.obj.rows;
              this.listQueryFortress.total = res.obj.total;
              if (res.obj.total == 0) {
                this.listQueryFortress.totalPage = 1;
              } else {
                this.listQueryFortress.totalPage = Math.ceil(
                  this.listQueryFortress.total / this.listQueryFortress.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },

    //云堡垒机--操作日志
    getFortressOperData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (this.listQueryFortressOper.skey == "") {
        if (this.listQueryFortressOper.skeyName == "用户") {
          this.listQueryFortressOper.skey = "NAME";
        } else if (this.listQueryFortressOper.skeyName == "来源IP") {
          this.listQueryFortressOper.skey = "IP";
        } else if (this.listQueryFortressOper.skeyName == "日志内容") {
          this.listQueryFortressOper.skey = "LOG";
        }
      }

      let fortressOperData = {
        pageSize: this.listQueryFortressOper.limit,
        page: this.listQueryFortressOper.page,
        sKey: this.listQueryFortressOper.skey,
        sValue: this.listQueryFortressOper.keyword,
        userId: this.userid,
      };
      operLog(fortressOperData)
        .then((res) => {
          console.log(res);
          if (res.ok == true) {
            this.listLoading.close();
            if (res.obj != null) {
              this.fortressOperData = res.obj.rows;
              this.listQueryFortressOper.total = res.obj.total;
              if (res.obj.total == 0) {
                this.listQueryFortressOper.totalPage = 1;
              } else {
                this.listQueryFortressOper.totalPage = Math.ceil(
                  this.listQueryFortressOper.total /
                    this.listQueryFortressOper.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云数据库审计
    getDatabaseData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });

      let baseData = {
        page: this.listQueryDatabase.page,
        rows: this.listQueryDatabase.limit,
        startTime: this.listQueryDatabase.startTime,
        endTime: this.listQueryDatabase.endTime,
        userId: this.userid,
      };
      console.log(baseData);
      database(baseData)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.listLoading.close();
            this.databaseData = res.data.rows;
            this.listQueryDatabase.total = res.data.total;
            if (res.obj.total == 0) {
              this.listQueryDatabase.totalPage = 1;
            } else {
              this.listQueryDatabase.totalPage = Math.ceil(
                this.listQueryDatabase.total / this.listQueryDatabase.limit
              );
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },

    tabClick(index) {
      this.tabNum = index;
      if (index == 2) {
        //云主机安全防火墙、网络管理日志
        this.hostTabItem = "防火墙/网络管理日志";
        this.getFirewallData();
      } else if (index == 4) {
        //云waf---事件日志
        this.getWafevnetData();
      } else if (index == 1) {
        //云堡垒机---系统登录日志
        this.getFortressData();
      } else if (index == 3) {
        //云数据库审计
        this.getDatabaseData();
      }
    },
    //主机安全二级tab选项卡
    handleClick(tab, event) {
      console.log(tab.label);
      if (tab.label == "防火墙 / 网络管理日志") {
        this.hostTabItem = "防火墙/网络管理日志";
        this.getFirewallData();
      } else if (tab.label == "入侵防御日志") {
        this.hostTabItem = "入侵防御日志";
        this.getHostData();
      } else if (tab.label == "防暴力破解日志") {
        this.hostTabItem = "防暴力破解日志";
        this.getPreFCData();
      } else if (tab.label == "防恶意软件") {
        this.hostTabItem = "防恶意软件";
        this.getwebShellData();
      }
    },
    //云堡垒机二级tab选项卡
    handleClickFortress(tab, event) {
      console.log(tab.label);
      if (tab.label == "系统登录日志") {
        this.getFortressData();
      } else if (tab.label == "系统操作日志") {
        this.getFortressOperData();
      }
    },

    //云防火墙二级tab选项卡
    handleClickFirewall(tab, event) {
      console.log(tab);
      if (tab.label == "事件日志") {
        this.getFireevnetData();
      } else if (tab.label == "网络日志") {
        this.getFirenetWorkData();
      } else if (tab.label == "配置日志") {
        this.getFireConfigureData();
      } else if (tab.label == "威胁日志") {
        this.getFireThreatData();
      }
    },
    //云Waf二级tab选项卡
    handleClickWaf(tab, event) {
      console.log(tab);
      if (tab.label == "事件日志") {
        this.getWafevnetData();
      } else if (tab.label == "网络日志") {
        this.getWafNetworkData();
      } else if (tab.label == "配置日志") {
        this.getWafConfigurationData();
      } else if (tab.label == "网络安全日志") {
        this.getWafNetworkSecurityData();
      }
    },
    //云主机安全防火墙、网络日志刷新
    firewallRefresh() {
      this.getFirewallData();
    },
    //云主机安全入侵防御刷新
    hostrefresh() {
      this.getHostData();
    },
    //云主机安全防暴力破解日志刷新
    preFCrefresh() {
      this.getPreFCData();
    },
    //云主机安全防恶意软件刷新
    webShellrefresh() {
      this.getwebShellData();
    },
    //云防火墙事件日志时间筛选
    fireEventDateSelct(value) {
      console.log(value);
      this.listQueryFireevent.date = value;
      this.getFireevnetData();
    },
    //云防火墙事件日志刷新
    fireeventRefresh() {
      this.getFireevnetData();
    },
    //云防火墙网络日志筛选
    firenetWorkDateSelct(value) {
      console.log(value);
      this.listQueryFirenetWork.date = value;
      this.getFirenetWorkData();
    },
    //云防火墙网络日志刷新
    firenetWorkRefresh() {
      this.getFirenetWorkData();
    },
    //云防火墙配置日志筛选
    fireConfigureDateSelct(value) {
      console.log(value);
      this.listQueryFireConfigure.date = value;
      this.getFireConfigureData();
    },
    //云防火墙配置日志刷新
    fireConfigureRefresh() {
      this.getFireConfigureData();
    },
    //云防火墙威胁日志筛选
    fireThreatDateSelct(value) {
      console.log(value);
      this.listQueryFireThreat.date = value;
      this.getFireThreatData();
    },
    //云防火墙威胁日志刷新
    fireThreatRefresh() {
      this.getFireThreatData();
    },
    //云WAF事件日志筛选
    wafEventDateSelct(value) {
      console.log(value);
      this.listQueryWafevent.date = value;
      this.getWafevnetData();
    },
    //云WAF事件日志刷新
    wafEventRefresh() {
      this.getWafevnetData();
    },
    //云WAF网络日志筛选
    wafNetworkDateSelct(value) {
      console.log(value);
      this.listQueryWafnetwork.date = value;
      this.getWafNetworkData();
    },
    //云WAF网络日志刷新
    wafNetworkRefresh() {
      this.getWafNetworkData();
    },
    //云WAF配置日志筛选
    wafConfigurationDateSelct(value) {
      console.log(value);
      this.listQueryWafConfiguration.date = value;
      this.getWafConfigurationData();
    },
    //云WAF配置日志刷新
    wafConfigurationRefresh() {
      this.getWafConfigurationData();
    },
    //云WAF网络安全日志筛选
    wafNetworkSecurityDateSelct(value) {
      console.log(value);
      this.listQueryWafNetworkSecurity.date = value;
      this.getWafNetworkSecurityData();
    },
    //云WAF网络安全日志刷新
    wafNetworkSecurityRefresh() {
      this.getWafNetworkSecurityData();
    },
    //云堡垒机登录日志实时搜索
    fortressLoginSearch() {
      this.getFortressData();
    },
    //云堡垒机登录日志筛选
    loginNameSelect(value) {
      console.log(value);
      console.log(this.listQueryFortress.skeyName);
      this.listQueryFortress.skey = value;
      this.listQueryFortress.keyword = "";
      this.getFortressData();
    },
    //云堡垒机登录日志日志内容筛选
    loginLogCSelect(value) {
      console.log(value);
      this.listQueryFortress.keyword = value;

      console.log(this.listQueryFortress.keyword);

      this.getFortressData();
    },
    //云堡垒机登录日志刷新
    fortressLoginRefresh() {
      this.getFortressData();
    },
    //云堡垒机操作日志实时搜索
    fortressOperSearch() {
      this.getFortressOperData();
    },

    //云堡垒机操作日志筛选
    operNameSelect(value) {
      console.log(value);
      this.listQueryFortressOper.skey = value;
      this.listQueryFortressOper.keyword = "";

      this.getFortressOperData();
    },
    //云堡垒机操作日志刷新
    fortressOperRefresh() {
      this.getFortressOperData();
    },

    //云数据库审计日志刷新
    databaseRefresh() {
      this.getDatabaseData();
    },
    //云主机防火墙、网络管理-入侵防御-防暴力破解时间段筛选
    fireWallChange(value) {
      console.log(value);
      if (value != null) {
        let date = [];
        let d1 = this.getTimestamp(
          moment(value[0]).format("YYYY-MM-DD HH:mm:ss")
        );
        let d2 = this.getTimestamp(
          moment(value[1]).format("YYYY-MM-DD HH:mm:ss")
        );

        date.push(d1, d2);
        // console.log(date);
        // console.log(this.hostTabItem);
        if (this.hostTabItem == "防火墙/网络管理日志") {
          this.listQueryFirewall.date = date.join(",");
          console.log(this.listQueryFirewall.date);
          this.getFirewallData();
        } else if (this.hostTabItem == "入侵防御日志") {
          this.listQueryHostFire.date = date.join(",");
          console.log(this.listQueryHostFire.date);
          this.getHostData();
        } else if (this.hostTabItem == "防暴力破解日志") {
          this.listQueryPreFC.date = date.join(",");
          console.log(this.listQueryPreFC.date);
          this.getPreFCData();
        } else if (this.hostTabItem == "防恶意软件") {
          this.listQueryShell.date = date.join(",");
          console.log(this.listQueryShell.date);
          this.getwebShellData();
        }
      } else {
        if (this.hostTabItem == "防火墙/网络管理日志") {
          this.listQueryFirewall.date = "";
          this.getFirewallData();
        } else if (this.hostTabItem == "入侵防御日志") {
          this.listQueryHostFire.date = "";

          this.getHostData();
        } else if (this.hostTabItem == "防暴力破解日志") {
          this.listQueryPreFC.date = "";

          this.getPreFCData();
        } else if (this.hostTabItem == "防恶意软件") {
          this.listQueryShell.date = "";

          this.getwebShellData();
        }
      }
    },

    //云数据库审计时间筛选
    databaseChange(value) {
      console.log(value);
      let d1 = moment(value[0]).format("YYYY-MM-DD HH:mm:ss");
      let d2 = moment(value[1]).format("YYYY-MM-DD HH:mm:ss");
      console.log(d1);
      console.log(d2);
      this.listQueryDatabase.startTime = this.getTimestamp(d1);
      this.listQueryDatabase.endTime = this.getTimestamp(d2);
      this.getDatabaseData();
    },
    getTimestamp(time) {
      //把时间日期转成时间戳
      return new Date(time).getTime() / 1000;
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    //防火墙 / 网络管理
    // handleSizeChangeVirus(val) {
    //   console.log(`每页 ${val} 条`);
    //   this.listQueryFirewall.limit = val;
    //   this.getFirewallData();
    // },
    // handleCurrentChangeVirus(val) {
    //   console.log(`当前页: ${val}`);
    //   this.listQueryFirewall.page = val;
    //   this.getFirewallData();
    // },
    prevVirus() {
      if (this.listQueryFirewall.page > 1) {
        this.listQueryFirewall.page--;
      } else {
        this.listQueryFirewall.page = 1;
        this.previrus = true;
        this.$message({
          message: "已经是第一页",
          type: "warning",
        });
      }
      console.log(this.listQueryFirewall.page);
      this.getFirewallData();
    },
    nextVirus() {
      if (this.fireWallData.length < 50) {
        this.nextvirus = true;
        this.$message({
          message: "已经是最后一页",
          type: "warning",
        });
      } else {
        this.listQueryFirewall.page++;
        this.getFirewallData();
      }
    },
    //主机安全入侵防御
    // handleSizeChangeHost(val) {
    //   console.log(`每页 ${val} 条`);
    //   this.listQueryHostFire.limit = val;
    //   this.getHostData();
    // },
    // handleCurrentChangeHost(val) {
    //   console.log(`当前页: ${val}`);
    //   this.listQueryHostFire.page = val;
    //   this.getHostData();
    // },
    prevHost() {
      if (this.listQueryHostFire.page > 1) {
        this.listQueryHostFire.page--;
      } else {
        this.listQueryHostFire.page = 1;
        this.preHostDis = true;
        this.$message({
          message: "已经是第一页",
          type: "warning",
        });
      }

      this.getHostData();
    },
    nextHost() {
      if (this.hostFireData.length < 50) {
        this.nextHostDis = true;
        this.$message({
          message: "已经是最后一页",
          type: "warning",
        });
      } else {
        this.listQueryHostFire.page++;
        this.getHostData();
      }
    },
    //主机安全防暴力破解日志
    // handleSizeChangePreFC(val) {
    //   console.log(`每页 ${val} 条`);
    //   this.listQueryPreFC.limit = val;
    //   this.getPreFCData();
    // },
    // handleCurrentChangePreFC(val) {
    //   console.log(`当前页: ${val}`);
    //   this.listQueryPreFC.page = val;
    //   this.getPreFCData();
    // },
    prevPreFc() {
      if (this.listQueryPreFC.page > 1) {
        this.listQueryPreFC.page--;
      } else {
        this.listQueryPreFC.page = 1;
        this.prePreFcDis = true;
        this.$message({
          message: "已经是第一页",
          type: "warning",
        });
      }

      this.getPreFCData();
    },
    nextPreFc() {
      if (this.preFcData.length < 50) {
        this.nextPreFcDis = true;
        this.$message({
          message: "已经是最后一页",
          type: "warning",
        });
      } else {
        this.listQueryPreFC.page++;
        this.getPreFCData();
      }
    },
    //云主机安全---防恶意软件
    // handleSizeChangeShell(val) {
    //   console.log(`每页 ${val} 条`);
    //   this.listQueryShell.limit = val;
    //   this.getwebShellData();
    // },
    // handleCurrentChangeShell(val) {
    //   console.log(`当前页: ${val}`);
    //   this.listQueryShell.page = val;
    //   this.getwebShellData();
    // },
    prevShell() {
      if (this.listQueryShell.page > 1) {
        this.listQueryShell.page--;
      } else {
        this.listQueryShell.page = 1;
        this.preShellDis = true;
        this.$message({
          message: "已经是第一页",
          type: "warning",
        });
      }

      this.getwebShellData();
    },
    nextShell() {
      if (this.webShellData.length < 50) {
        this.nextShellDis = true;
        this.$message({
          message: "已经是最后一页",
          type: "warning",
        });
      } else {
        this.listQueryShell.page++;
        this.getwebShellData();
      }
    },
    //云防火墙---事件日志

    handleSizeChangeFireevent(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFireevent.limit = val;
      this.getFireevnetData()();
    },
    handleCurrentChangFireevent(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFireevent.page = val;
      this.getFireevnetData();
    },
    //云防火墙---网路日志

    handleSizeChangeFirenetWork(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFirenetWork.limit = val;
      this.getFirenetWorkData();
    },
    handleCurrentChangFirenetWork(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFirenetWork.page = val;
      this.getFirenetWorkData();
    },
    //云防火墙---配置日志

    handleSizeChangeFireConfigure(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFireConfigure.limit = val;
      this.getFireConfigureData();
    },
    handleCurrentChangFireConfigure(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFireConfigure.page = val;
      this.getFireConfigureData();
    },
    //云防火墙---威胁日志

    handleSizeChangeFireThreat(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFireThreat.limit = val;
      this.getFireThreatData();
    },
    handleCurrentChangFireThreat(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFireThreat.page = val;
      this.getFireThreatData();
    },
    //云WAF---事件日志

    handleSizeChangeWafevent(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryWafevent.limit = val;
      this.getWafevnetData();
    },
    handleCurrentChangeWafevent(val) {
      console.log(`当前页: ${val}`);
      this.listQueryWafevent.page = val;
      this.getWafevnetData();
    },
    //云WAF---网络日志

    handleSizeChangeWafNetwork(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryWafnetwork.limit = val;
      this.getWafNetworkData();
    },
    handleCurrentChangeWafNetwork(val) {
      console.log(`当前页: ${val}`);
      this.listQueryWafnetwork.page = val;
      this.getWafNetworkData();
    },
    //云WAF---配置日志

    handleSizeChangeWafConfiguration(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryWafConfiguration.limit = val;
      this.getWafConfigurationData();
    },
    handleCurrentChangeWafConfiguration(val) {
      console.log(`当前页: ${val}`);
      this.listQueryWafConfiguration.page = val;
      this.getWafConfigurationData();
    },
    //云WAF---网络安全日志

    handleSizeChangeWafNetworkSecurity(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryWafNetworkSecurity.limit = val;
      this.getWafNetworkSecurityData();
    },
    handleCurrentChangeWafNetworkSecurity(val) {
      console.log(`当前页: ${val}`);
      this.listQueryWafNetworkSecurity.page = val;
      this.getWafNetworkSecurityData();
    },
    //云堡垒机--登录日志

    handleSizeChangeForess(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFortress.limit = val;
      this.getFortressData();
    },
    handleCurrentChangeForess(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFortress.page = val;
      this.getFortressData();
    },
    //云堡垒机--操作日志

    handleSizeChangeForessOper(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryFortressOper.limit = val;
      this.getFortressOperData();
    },
    handleCurrentChangeForessOper(val) {
      console.log(`当前页: ${val}`);
      this.listQueryFortressOper.page = val;
      this.getFortressOperData();
    },
    //云日志审计

    handleSizeChangeDatabase(val) {
      console.log(`每页 ${val} 条`);
      this.listQueryDatabase.limit = val;
      this.getDatabaseData();
    },
    handleCurrentChangeDatabase(val) {
      console.log(`当前页: ${val}`);
      this.listQueryDatabase.page = val;
      this.getDatabaseData();
    },

    //主机安全操作日志详情
    handleInfoClick(row) {},
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 230px);
}

.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.el-tabs--border-card {
  border: none;
  box-shadow: none;
}
.el-date-editor::v-deep .el-range__icon {
  position: absolute;
  right: 5px;
  top: 2px;
}
.el-date-editor::v-deep .el-input__inner {
  padding-left: 15px;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 5px;
}
.bg-warm {
  background: rgba(242, 174, 27, 1);
}
.bg-warn {
  background: rgba(236, 89, 96, 1);
}
.bg-normal {
  background: rgba(0, 94, 164, 1);
}
.bg-danger {
  background: rgba(188, 78, 115, 1);
}
.loginSelect {
  width: 120px;
  border-right: 1px solid #dae0e6;
}
.page-btn {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  border: none;
  outline: none;
  padding: 0 6px;
  background: none;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }

  i {
    width: 34px;
    height: 34px;
    background: #fff;
    border: 1px solid #d7dce2;
    border-radius: 2px;
    padding: 8px;
    font-size: 14px;
    color: #005ea4;
  }
}
</style>
