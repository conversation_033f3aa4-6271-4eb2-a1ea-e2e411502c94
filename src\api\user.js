import request from '@/utils/request'
//用户登录接口
export function login(data) {
  // debugger
  return request({
    url: '/api/login',
    method: 'post',
    params:data 
  })
}
//用户登出接口

export function logout(account) {
  //  debugger
  return request({
    url: '/api/syslogout',
    method: 'post',
    params:{account:account}
  })
}

export function getInfo(token) {
  return request({
    url: '/api/verify',
    method: 'post',
    params: { 'token':token }
  })
}


//验证码接口
export function vcode(data) {
  return request({
    url: '/api/kaptcha/checkVerificationCode',
    method: 'post',
    params:{verificationCode:data}
  })
}
//判断用户是否授权
export function isAuth(data) {
  return request({
    url: '/api/license/license',
    method: 'get',
    params:data
    
  })
}
//授权
export function license(code) {
  return request({
    url: '/api/license/importlicense',
    method: 'get',
    params:{code:code}
    
  })
}
// 授权提醒
export function getNews() {
  return request({
    url: '/api/license/getNews',
    method: 'get',
   
    
  })
}
// 授权校验
export function getExpireDate() {
  return request({
    url: '/api/license/getExpireDate',
    method: 'get',
   
    
  })
}
//ukey管理证书登录详情
export function getCerInfo(data) {
  return request({
    url: '/api/cer/getInfo',
    method: 'get',
    params:data  
  })
}
//ukey管理证书登录新增
export function addTCertificat(data) {
  return request({
    url: '/api/cer/addTCertificate',
    method: 'get',
    params:data  
  })
}
//ukey管理证书登录更新
export function updateStateByUserId(data) {
  return request({
    url: '/api/cer/updateStateByUserId',
    method: 'get',
    params:data  
  })
}

//ukey管理证书登录
export function ukeyLogin(data) {
  return request({
    url: '/api/ukeyLogin',
    method: 'post',
    params:data  
  })
}
//用户名查状态-是否绑定ukey 
export function selectUkeyByUserName(userName) {
  return request({
    url: '/api/selectUkeyByUserName',
    method: 'post',
    params:{
      userName:userName
    }
  })
}