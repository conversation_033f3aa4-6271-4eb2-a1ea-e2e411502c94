<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box">
      
        <div class="auth-item">
          <h2 class="title">云日志审计(山石网科)</h2>
          <el-table
            :data="logAuditData"
            style="width: 100%;margin-bottom:20px;"
            row-key="id"
            border
          >
            <el-table-column prop="sn" label="设备SN" sortable width="180"></el-table-column>
            <el-table-column prop="cust" label="客户" sortable width="180"></el-table-column>
            <el-table-column prop="type" label="类型" sortable></el-table-column>
            <el-table-column prop="capacity" label="存储大小"></el-table-column>
            <el-table-column prop="time" label="有效时间" sortable></el-table-column>
          </el-table>
        </div>
       
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
     
      logAuditData: [
        {
          sn: "0000000000000",
          cust: "Hillstone Network ",
          type: "VHSA",
          capacity: "200G",
          time: "--"
        }
      ],
     
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
    
    }
  }
}
</style>
