<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box" v-if="showTenant">
        <div class="auth-item">
          <h2 class="title">云防火墙(山石网科)</h2>
          <el-table
            :data="firewallData"
            style="width: 100%;margin-bottom:20px;"
            row-key="id"
            border
          >
            <el-table-column prop="cust" label="客户" sortable width="180"></el-table-column>
            <el-table-column prop="type" label="类型" sortable></el-table-column>
            <el-table-column prop="time" label="有效时间" sortable></el-table-column>
            <el-table-column prop="info" label="其他信息"></el-table-column>
          </el-table>
        </div>
       
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
      firewallData: [
        {
          cust: "Trial user",
          type: "URL DB",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "应用特征库",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "入侵防御",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "病毒过滤",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "SCVPN",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "IPSEC VPN",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "平台",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "",
          type: "平虚拟机CPU",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "终端防护",
          time: "未授权",
          info: ""
        }
      ],
  
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
     
    }
  }
}
</style>
