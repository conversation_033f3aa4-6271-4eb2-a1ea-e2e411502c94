<template>
 
      <!-- <div class="header clearfix">
        <h3 class="title">签名服务器信息</h3>
      </div> -->
      <div>
        <div class="serch-box clearfix">
          <div class="filter-container">
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              @click="handleRefresh"
            >
              <svg-icon icon-class="refresh" />
            </el-button>
          </div>
          <div class="page-box">
            <!-- <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChang"
              :current-page="listQuery.currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="10"
              layout="sizes, prev,slot, next,total"
              :total="listQuery.total"
            >
              <span class="pageNum">
                {{ this.listQuery.page }}
                <i class="divider">/</i>
                {{ this.listQuery.totalPage }}
              </span>
            </el-pagination> -->
          </div>
        </div>
        <div class="table-box table-box2">
          <el-table-bar>
            <el-table
              :data="tableData"
              style="width: 100%; margin-bottom: 20px"
              row-key="id"
            >
             <el-table-column  label="序号" > 
                 <template slot-scope="scope">
                 <span>{{scope.$index+1}}</span>
                 
                </template>
              </el-table-column>
              <el-table-column
                prop="devName"
                label="设备名称"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="devFunction"
                label="设备性能"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="devIp"
                label="IP地址"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="devPort"
                label="端口号"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column label="是否启用" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="scope.row.isEnable == '0'">
                  弃用
                    <!-- <el-switch :value="value1" disabled> </el-switch> -->
                  </span>
                  <span v-else-if="scope.row.isEnable == '1'">
                  启用
                    <!-- <el-switch :value="value2" disabled> </el-switch> -->
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="设备状态" sortable show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-tag type="warning" v-if="scope.row.status == '1'"
                    >录入</el-tag
                  >
                  <el-tag type="danger" v-else-if="scope.row.status == '2'"
                    >审批</el-tag
                  >
                  <el-tag type="success" v-else-if="scope.row.status == '3'"
                    >接入</el-tag
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-table-bar>
        </div>
      </div>
 
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { Loading } from "element-ui";
import { mapGetters } from "vuex";
import moment from "moment"; //时间格式转化
import { qmfwqInfo } from "@/api/monitoring.js";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableData: [], //
      listQuery: {
        page: 1,
        limit: 10,
        currentPage: 1,
        total: 0,
        totalPage: 1,
        keyWord: "",
        startTime: "",
        endTime: ""
      },
      value1:false,
      value2:true
    };
  },
  created() {
    this.getTableData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"])
  },
  methods: {
    dateFormat(row, column) {
      var moment = require("moment");
      var date = row[column.property];
      return moment(date).format("YYYY-MM-DD hh:mm:ss");
    },
    getTableData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)"
      });

      qmfwqInfo()
        .then(res => {
          // console.log(res);
          if (res.code == 1) {
           setTimeout(() => {
            this.listLoading.close();
          }, 200);
            if (res.data != null) {
              this.tableData = res.data;
              // this.listQuery.total = res.data.total;
              // if (res.data.total == 0) {
              //   this.listQuery.totalPage = 1;
              // } else {
              //   this.listQuery.totalPage = Math.ceil(
              //     this.listQuery.total / this.listQuery.limit
              //   );
              // }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch(error => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //刷新
    handleRefresh() {
      this.getTableData();
    },
    //
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getTableData();
    },
    handleCurrentChang(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getTableData();
    }
  }
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 230px);
}

.el-tabs--border-card {
  border: none;
  box-shadow: none;
}
.el-date-editor::v-deep .el-range__icon {
  position: absolute;
  right: 5px;
  top: 2px;
}
.el-date-editor::v-deep .el-input__inner {
  padding-left: 15px;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 5px;
}
.loginSelect {
  width: 120px;
  border-right: 1px solid #dae0e6;
}
.page-btn {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  border: none;
  outline: none;
  padding: 0 6px;
  background: none;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }

  i {
    width: 34px;
    height: 34px;
    background: #fff;
    border: 1px solid #d7dce2;
    border-radius: 2px;
    padding: 8px;
    font-size: 14px;
    color: #005ea4;
  }
}
</style>

