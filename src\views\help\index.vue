<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">帮助中心</h3>
      </div>
      <div class="search-box">
        <div class="search-container">
          <el-input
            v-model="title"
            placeholder="文档名称"
            style="width: 200px"
            class="filter-item"
            v-on:input="search"
          />
          <span
            class="el-icon-search search-btn"
            @click="handleSearch()"
          ></span>
        </div>
      </div>
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <ul class="doc-box">
          <li
            v-for="(item, index) in docList"
            :key="index"
            @click="openDoc(item)"
          >
            <svg-icon icon-class="word" />
            <span>{{ item.title }}</span>
          </li>
        </ul>
      </el-scrollbar>
    </div>
    <el-dialog
      :title="docTitle"
      :visible.sync="docDialogVisible"
      width="50%"
      :before-close="handleClose"
    >
      <div class="info-box">
        <div v-html="docContent" class="info-cont"></div>
        <div v-if="fileArray.length > 0" class="info-list-box">
          <h3>参考文档：</h3>
          <ul>
            <li v-for="(item, index) in fileArray" :key="index">
              <span class="text" @click="preVirwFile(item)"
                ><svg-icon icon-class="word" /> {{ item.fileName }}</span
              >
              <span class="download" @click="downloadFileBtn(item)"
                ><svg-icon icon-class="download"
              /></span>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :title="fileName"
      :visible.sync="docPreviewVisible"
      width="50%"
      @close="closePreview"
      :close-on-click-modal="false"
    >
      <div class="file-wrap" style="min-height: 260px">
        <!-- pdf文档预览 -->
        <div v-if="fileType == 'pdf'">
          <el-row :gutter="20">
            <span>共{{ pageCount }}页， 当前第 {{ pdfPage }} 页 </span>
            <el-button type="text" size="mini" @click.stop="previousPage"
              >上一页</el-button
            >
            <el-button type="text" size="mini" @click.stop="nextPage"
              >下一页</el-button
            >
          </el-row>

          <pdf
            :src="src"
            :page="pdfPage"
            ref="pdf"
            @num-pages="pageCount = $event"
            @page-loaded="pdfPage = $event"
            style="display: inline-block; width: 100%"
          ></pdf>
        </div>
        <!-- word文档预览 -->
        <div v-else-if="fileType == 'word'">
          <div id="wordView" v-html="wordHtml"></div>
        </div>
        <!-- excel表格预览 -->
        <div v-else-if="fileType == 'excel'">
          <div id="table">
            <el-table :data="excelData" style="width: 100%">
              <el-table-column
                v-for="(value, key, index) in excelData[2]"
                :key="index"
                :prop="key"
                :label="key"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!-- 图片预览 -->
        <div v-else-if="fileType == 'image'">
          <img :src="imgUrl" alt="" />
        </div>
        <!-- ppt -->
        <div v-else-if="fileType == 'ppt'">
          <iframe
            id="iframe1"
            width="100%"
            height="100%"
            frameborder="0"
            border="0"
            marginwidth="0"
            marginheight="0"
            scrolling="no"
            allowtransparency="yes"
            :src="'http://view.officeapps.live.com/op/view.aspx?src=' + pptUrl"
          ></iframe>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { helpList, fileDown } from "@/api/help.js";
import { Loading } from "element-ui";
import pdf from "vue-pdf";
import mammoth from "mammoth";
import XLSX from "xlsx";
export default {
  components: {
    pdf
  },
  data() {
    return {
      title: "", //文档关键词搜索
      centosImg: require("@/assets/centos.png"),
      docList: [],
      docTitle: "",
      docDialogVisible: false,
      listLoading: false,
      docContent: "", //文本内容
      fileArray: [],
      docPreviewVisible: false,
      file: {},
      fileName: "",
      src: null,
      pdfPage: 1,
      pageCount: 0,
      fileType: "",
      wordHtml: "", //word文件内容
      wordUrl: "", //word文件地址
      excelData: [],
      workbook: {},
      excelUrl: "", //文件地址
      imgUrl: "", //图片地址
      pptUrl: ""
    };
  },
  created() {},
  computed: {
    // filterItems() {
    //   var _search = this.title;
    //   // console.log(_search);
    //   if (_search) {
    //     // 不区分大小写处理
    //     var reg = new RegExp(_search, "ig");
    //     // es6 filter过滤匹配，有则返回当前，无则返回所有
    //     return this.docList.filter(function (e) {
    //       // 匹配所有字段
    //       // console.log(e);
    //       return Object.keys(e).some(function (key) {
    //         return e[key].match(reg);
    //       });
    //       // 匹配某个字段
    //       return e.name.match(reg);
    //     });
    //   }
    //   // console.log(this.docList)
    //   return this.docList;
    // },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)"
      });
      let data = {
        keyWord: this.title
      };
      helpList(data)
        .then(res => {
          console.log(res);
          if (res.code == 1) {
             setTimeout(() => {
            this.listLoading.close();
          }, 200);
            this.docList = res.data.rows;
          }
        })
        .catch(error => {
          console.log(error);
          this.listLoading.close();
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },

    openDoc(item) {
      // console.log(item);
      this.docTitle = item.title;
      this.docContent = item.content;
      if (item.annex != "") {
        this.fileArray = JSON.parse(item.annex);
      }
      this.docDialogVisible = true;
    },
    handleClose() {
      this.docDialogVisible = false;
      this.docContent = "";
      this.docContent = "";
      this.fileArray = [];
    },
    //下载文件
    downloadFileBtn(item) {
      console.log(item);
      // fileDown()
      //   .then(res => {
      //     console.log(res);
      //     if (res.code == 1) {
      //     }
      //   })
      //   .catch(error => {
      //     console.log(error);
      //   });
      let url = item.fileUrl;
      let name = item.fileName;
      this.downLoadFile(url, name);
    },
    //下载文件
    downLoadFile(url, name) {
      if (name == "附件") {
        var first = url.lastIndexOf("."); //取到文件名开始到最后一个点的长度
        var namelength = url.length; //取到文件名长度
        var filesuffix = url.substring(first + 1, namelength); //截取获得后缀名
        name = name + "." + filesuffix;
      }
      // 下载文件
      this.download(url, name); // OSS可下载的文件url，你想要改的名字
    },
    getBlob(url, cb) {
      // 获取文件流
      var xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function() {
        if (xhr.status === 200) {
          cb(xhr.response);
        }
      };
      xhr.send();
    },

    saveAs(blob, filename) {
      // 改名字
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename);
      } else {
        var link = document.createElement("a");
        var body = document.querySelector("body");

        link.href = window.URL.createObjectURL(blob);
        link.download = filename;

        // fix Firefox
        link.style.display = "none";
        body.appendChild(link);

        link.click();
        body.removeChild(link);

        window.URL.revokeObjectURL(link.href);
      }
    },
    download(url, filename) {
      let _this = this;
      // 执行
      this.getBlob(url, function(blob) {
        _this.saveAs(blob, filename);
      });
    },
    //预览文件
    preVirwFile(item) {
      this.file = item;
      this.fileName = item.fileName;
      let fileUrl = item.fileUrl;
      console.log(fileUrl);
      if (
        !(
          fileUrl.includes(".png") ||
          fileUrl.includes(".jpg") ||
          fileUrl.includes(".jpeg") ||
          fileUrl.includes(".bmp") ||
          fileUrl.includes(".JPG") ||
          fileUrl.includes(".PNG") ||
          fileUrl.includes(".JPEG") ||
          fileUrl.includes(".BMP") ||
          fileUrl.includes(".pdf") ||
          fileUrl.includes(".txt") ||
          fileUrl.includes(".xls") ||
          fileUrl.includes(".doc") ||
          fileUrl.includes(".docx") ||
          fileUrl.includes(".xlsx") ||
          fileUrl.includes(".ppt") ||
          fileUrl.includes(".pptx")
        )
      ) {
        this.$message.error("文件类型不支持预览");
        return false;
      }
      //文件路径
      var filePath = item.filePath;
      //获取最后一个.的位置
      var index = filePath.lastIndexOf(".");
      //获取后缀
      var ext = filePath.substr(index + 1);
      console.log(ext);
      let ishttps = 'https:' == document.location.protocol ? true: false;
      let reg=new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
      let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
      // if(ishttps){
      //     let ip=fileUrl.match(reg)[0];
      //     fileUrl=fileUrl.replace(spat,ip) 
      // }else{
        
      // }
      console.log(fileUrl);
      
      // console.log(this.isAssetTypeAnImage(ext));
      if (ext == "pdf" || ext == "txt") {
        this.fileType = "pdf";
        //pdf预览
        this.src = pdf.createLoadingTask({
          url: fileUrl
        });
        this.src.promise.then(pdf => {
          this.docPreviewVisible = true;
        });
      } else if (ext == "doc" || ext == "docx") {
        this.fileType = "word";
        this.readWordFromRemoteFile(fileUrl);
        this.docPreviewVisible = true;
      } else if (ext == "xls" || ext === "xlsx") {
        this.fileType = "excel";
        this.readWorkbookFormRemoteFile(fileUrl);
        this.docPreviewVisible = true;
      } else if (this.isAssetTypeAnImage(ext) == true) {
        this.fileType = "image";
        this.imgUrl = fileUrl;
        this.docPreviewVisible = true;
      } else if (ext == "ppt" || ext === "pptx") {
        this.fileType = "ppt";
        this.pptUrl = fileUrl;
        this.docPreviewVisible = true;
      } else {
        this.fileType = "";
        this.imgUrl = "";
        this.pptUrl = "";
      }
    },
    //在线查看word文件
    readWordFromRemoteFile(url) {
      let vm = this;
      var xhr = new XMLHttpRequest();
      xhr.open("get", url, true);
      xhr.responseType = "arraybuffer";
      xhr.onload = function() {
        if (xhr.status == 200) {
          mammoth
            .convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) })
            .then(function(resultObject) {
              vm.$nextTick(() => {
                vm.wordHtml = resultObject.value;
              });
            });
        }
      };
      xhr.send();
    },
    //在线预览excel 文件
    readWorkbookFormRemoteFile(url) {
      var xhr = new XMLHttpRequest();
      xhr.open("get", url, true);
      xhr.responseType = "arraybuffer";
      let _this = this;
      xhr.onload = function(e) {
        if (xhr.status === 200) {
          var data = new Uint8Array(xhr.response);
          var workbook = XLSX.read(data, { type: "array" });
          console.log("workbook", workbook);

          var sheetNames = workbook.SheetNames; // 工作表名称集合
          _this.workbook = workbook;
          _this.getTable(sheetNames[0]);
        }
      };
      xhr.send();
    },
    getTable(sheetName) {
      console.log(sheetName);
      var worksheet = this.workbook.Sheets[sheetName];
      this.excelData = XLSX.utils.sheet_to_json(worksheet);
      console.log(this.excelData);
    },
    //图片类型
    isAssetTypeAnImage(ext) {
      return (
        [
          "bmp",
          "jpg",
          "png",
          "tif",
          "gif",
          "pcx",
          "tga",
          "exif",
          "fpx",
          // "svg",
          "psd",
          "cdr",
          "pcd",
          "dxf",
          "ufo",
          "eps",
          "ai",
          "raw,wmf",
          "jpeg"
        ].indexOf(ext.toLowerCase()) !== -1
      );
    },
    closePreview() {
      this.pdfPage = 1;
    },
    previousPage() {
      let p = this.pdfPage;
      p = p > 1 ? p - 1 : this.pageCount;
      this.pdfPage = p;
    },
    nextPage() {
      let p = this.pdfPage;
      p = p < this.pageCount ? p + 1 : 1;
      this.pdfPage = p;
    }
  }
};
</script>
<style lang="scss" scoped>
.mainWrapper {
  height: calc(100vh - 60px);
  .mainBox {
    background: #fff;
    height: 100%;
    .search-box {
      padding: 20px 30px;
    }
    .doc-box {
      padding: 0 30px;
      li {
        line-height: 46px;
        padding: 5px 10px;
        cursor: pointer;
        color: #005bd4;
        .svg-icon {
          font-size: 18px;
        }
        span {
          display: inline-block;
          margin-left: 5px;
        }
        &:hover {
          background: #f0f2f5;
        }
      }
    }
  }
}
.el-scrollbar {
  height: calc(100vh - 140px);
}
.doc-content {
  -moz-user-select: none; /* Firefox私有属性 */
  -webkit-user-select: none; /* WebKit内核私有属性 */
  -ms-user-select: none; /* IE私有属性(IE10及以后) */
  -khtml-user-select: none; /* KHTML内核私有属性 */
  -o-user-select: none; /* Opera私有属性 */
  user-select: none; /* CSS3属性 */
  p {
    text-indent: 2em;
    line-height: 26px;
    margin-bottom: 10px;
  }
  h3 {
    margin-bottom: 10px;
  }
  .root-list {
    padding-left: 60px;
    li {
      list-style: disc;
      line-height: 26px;
      margin-bottom: 10px;
    }
  }
}
.info-box {
  p {
    line-height: 1.5;
  }
  .info-list-box {
    h3 {
      font-weight: 400;
      margin: 16px 0 10px 0;
    }
    li {
      display: flex;
      align-items: center;
      padding: 5px;
      color: #005bd4;
      .text {
        display: inline-block;
        margin-right: 16px;
        cursor: pointer;
      }
      .download {
        cursor: pointer;
      }
      &:hover {
        background: #eee;
      }
    }
  }
}
.file-wrap {
  width: 100%;
  height: 100%;
}
</style>
