<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">云密码日志</h3>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="密码服务实例" name="instance">
          <div>
            <div class="serch-box clearfix">
              <div class="filter-container">
                <el-button
                  v-waves
                  class="filter-item"
                  type="primary"
                  @click="handleRefresh"
                >
                  <svg-icon icon-class="refresh" />
                </el-button>
                <!-- <el-date-picker
                  v-model="listQuery.date"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="dateChangeInstance"
                  class="filter-item"
                >
                </el-date-picker> -->
                <el-select
              v-model="listQuery.date"
              placeholder="请选择"
              @change="dateChangeInstance"
            >
              <el-option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
                
              </div>
              <div class="page-box">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChang"
                  :current-page="listQuery.currentPage"
                  :page-sizes="[10, 20, 30, 50]"
                  :page-size="10"
                  layout="sizes, prev,slot, next,total"
                  :total="listQuery.total"
                >
                  <span class="pageNum">
                    {{ this.listQuery.page }}
                    <i class="divider">/</i>
                    {{ this.listQuery.totalPage }}
                  </span>
                </el-pagination>
              </div>
            </div>
            <div class="table-box">
              <el-table-bar>
                <el-table
                  :data="tableData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column label="业务类型">
                    <template slot-scope="scope">
                      <span v-if="scope.row.moduleType == 1">密码服务实例</span>
                      <span v-else>密钥管理系统</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="requestIp"
                    label="请求ip"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="requestMethod"
                    label="请求方法"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="httpMethod"
                    label="请求类型"
                    sortable
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="operator"
                    label="操作用户"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="createTime"
                    label="创建时间"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="operationTime"
                    label="操作时间"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column label="日志状态" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span v-if="scope.row.status == 1">签名成功</span>
                      <span v-else-if="scope.row.status == 2">验签成功</span>
                      <span v-else-if="scope.row.status == 3">验签失败</span>
                      <span v-else-if="scope.row.status == 4">归档</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" align="center">
                    <template slot-scope="scope">
                      <el-dropdown>
                        <span class="el-dropdown-link">
                          <i class="el-icon-more"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                          <div @click="handleInfo(scope.row)" class="opt">
                            详情
                          </div>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="密钥管理系统" name="km">
          <div>
            <div class="serch-box clearfix">
              <div class="filter-container">
                <el-button
                  v-waves
                  class="filter-item"
                  type="primary"
                  @click="handleRefresh"
                >
                  <svg-icon icon-class="refresh" />
                </el-button>
                <!-- <el-date-picker
                  v-model="listQuery2.date"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="dateChangeKm"
                  :default-time="['00:00:00', '23:59:59']"
                  class="filter-item"
                >
                </el-date-picker> -->
                <el-select
              v-model="listQuery2.date"
              placeholder="请选择"
              @change="dateChangeKm"
            >
              <el-option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
              </div>
              <div class="page-box">
                <el-pagination
                  @size-change="handleSizeChange2"
                  @current-change="handleCurrentChang2"
                  :current-page="listQuery2.currentPage"
                  :page-sizes="[10, 20, 30, 50]"
                  :page-size="10"
                  layout="sizes, prev,slot, next,total"
                  :total="listQuery2.total"
                >
                  <span class="pageNum">
                    {{ this.listQuery2.page }}
                    <i class="divider">/</i>
                    {{ this.listQuery2.totalPage }}
                  </span>
                </el-pagination>
              </div>
            </div>
            <div class="table-box">
              <el-table-bar>
                <el-table
                  :data="tableData2"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column label="业务类型">
                    <template slot-scope="scope">
                      <span v-if="scope.row.moduleType == 1">密码服务实例</span>
                      <span v-else>密钥管理系统</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="requestIp"
                    label="请求ip"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="requestMethod"
                    label="请求方法"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="httpMethod"
                    label="请求类型"
                    sortable
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="operator"
                    label="操作用户"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="createTime"
                    label="创建时间"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="operationTime"
                    label="操作时间"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column label="日志状态" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span v-if="scope.row.status == 1">签名成功</span>
                      <span v-else-if="scope.row.status == 2">验签成功</span>
                      <span v-else-if="scope.row.status == 3">验签失败</span>
                      <span v-else-if="scope.row.status == 4">归档</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" align="center">
                    <template slot-scope="scope">
                      <el-dropdown>
                        <span class="el-dropdown-link">
                          <i class="el-icon-more"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                          <div @click="handleInfo(scope.row)" class="opt">
                            详情
                          </div>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </div></el-tab-pane
        >
      </el-tabs>
    </div>
    <el-dialog title="日志详情" :visible.sync="dialogVisible" width="80%">
      <div class="info-box">
        <el-form
          ref="form"
          :model="info"
          label-width="120px"
          size="mini"
          :disabled="true"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="业务类型：">
                {{ info.moduleType == 1 ? "密码服务实例" : "密钥管理系统" }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求ip：">
                {{ info.requestIp }}
              </el-form-item></el-col
            >
            <el-col :span="24">
              <el-form-item label="请求方法包名：">
                {{ info.requestPackage }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求方法：">
                {{ info.requestMethod }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请求类型：">
                {{ info.httpMethod }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="请求参数：">
                {{ info.requestParam }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="返回结果：">
                {{ info.responseParam }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作用户：">
                {{ info.operator }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作时间：">
                {{ info.operationTime }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建时间：">
                {{ info.createTime }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日志状态：">
                <span v-if="info.status == 1">签名成功</span>
                <span v-else-if="info.status == 2">验签成功</span>
                <span v-else-if="info.status == 3">验签失败</span>
                <span v-else-if="info.status == 4">归档</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="数字签名：">
                {{ info.sign }}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="方法描述：">
                {{ info.description }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { Loading } from "element-ui";
import { mapGetters } from "vuex";
import { bussinessLog } from "@/api/log.js";
import moment from "moment";


export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableData: [], //
      listQuery: {
        page: 1,
        limit: 10,
        currentPage: 1,
        total: 0,
        totalPage: 1,
        keyWord: "",
        startTime: "",
        endTime: "",
        date: 'month'
      },
      tableData2: [], //
      listQuery2: {
        page: 1,
        limit: 10,
        currentPage: 1,
        total: 0,
        totalPage: 1,
        keyWord: "",
        startTime: "",
        endTime: "",
        date: 'month'
      },
      activeName: "instance",
      dialogVisible: false,
      info: {} ,//详情
      dateOptions: [
        {
          value: "hour",
          label: "最近一小时",
        },
        {
          value: "day",
          label: "最近一天",
        },
        {
          value: "month",
          label: "最近一月",
        },
      ],
    };
  },
  created() {
    this.getTableData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"])
  },
  methods: {
    dateFormat(row, column) {
      var moment = require("moment");
      var date = row[column.property];
      return moment(date).format("YYYY-MM-DD hh:mm:ss");
    },
    getTimestamp(time) {
      //把时间日期转成时间戳
      return new Date(time).getTime() / 1000;
    },
    filtersDate(value){
       // 当前时间
       let endTime=moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        let startTime;
      if (value == 'month') {
        //获取前一个月的日期：
        startTime=moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
        return {startTime,endTime}
      } else if(value == 'day') {
        //获取前一天的日期
        startTime=moment(new Date()).subtract(1,'days').format('YYYY-MM-DD HH:mm:ss');
        return {startTime,endTime}
      }else if(value == 'hour'){
        //获取最近一个小时
        startTime=moment(new Date()).subtract(1,'hours').format('YYYY-MM-DD HH:mm:ss');
        return {startTime,endTime}
      }
    },
    dateChangeInstance() {
      const date=this.filtersDate(this.listQuery.date);
      // console.log(date);
      this.listQuery.startTime = date.startTime;
      this.listQuery.endTime = date.endTime;
      // console.log(this.listQuery.startTime)
      // console.log(this.listQuery.endTime);
      this.getTableData();
    },
    dateChangeKm() {
      const date=this.filtersDate(this.listQuery2.date);
      // console.log(date);
      this.listQuery2.startTime = date.startTime;
      this.listQuery2.endTime = date.endTime;
      // console.log(this.listQuery.startTime)
      // console.log(this.listQuery.endTime);
      this.getTableData();
      
    },

    getTableData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)"
      });
      let data;
      if (this.activeName == "instance") {
        if(this.listQuery.startTime=='')  {
          this.listQuery.startTime=moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
          this.listQuery.endTime=moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
        data = {
          page: this.listQuery.page,
          limit: this.listQuery.limit,
          startTime: this.listQuery.startTime,
          endTime: this.listQuery.endTime
        };
      } else if (this.activeName == "km") {
        if(this.listQuery2.startTime=='' ) {
          this.listQuery2.startTime=moment(new Date()).subtract(1,'months').format('YYYY-MM-DD HH:mm:ss');
          this.listQuery2.endTime=moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
        data = {
          page: this.listQuery2.page,
          limit: this.listQuery2.limit,
          startTime: this.listQuery2.startTime,
          endTime: this.listQuery2.endTime
        };
      }

      console.log(data);
      bussinessLog(data)
        .then(res => {
          console.log(res);
          if (res.code == 1) {
            setTimeout(() => {
              this.listLoading.close();
            }, 200);
            if (res.data != null) {
              this.tableData = res.data.mmfw_instance_log.data;
              this.listQuery.total = res.data.mmfw_instance_log.total;
              this.tableData2 = res.data.mmfw_km_log.data;
              this.listQuery2.total = res.data.mmfw_km_log.total;
              if (res.data.mmfw_instance_log.total == 0) {
                this.listQuery.totalPage = 1;
              } else {
                this.listQuery.totalPage = Math.ceil(
                  this.listQuery.total / this.listQuery.limit
                );
              }
              if (res.data.mmfw_km_log.total == 0) {
                this.listQuery2.totalPage = 1;
              } else {
                this.listQuery2.totalPage = Math.ceil(
                  this.listQuery2.total / this.listQuery2.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch(error => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //刷新
    handleRefresh() {
      this.getTableData();
    },
    //
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getTableData();
    },
    handleCurrentChang(val) {
      console.log(val);
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getTableData();
    },
    handleSizeChange2(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery2.limit = val;
      this.getTableData();
    },
    handleCurrentChang2(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery2.page = val;
      this.getTableData();
    },
    handleInfo(row) {
      console.log(row);
      this.info = row;
      this.dialogVisible = true;
    },
    handleClick(tab, event) {
      // console.log(this.activeName);
      this.getTableData();
    }
  }
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 230px);
}
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.el-tabs--border-card {
  border: none;
  box-shadow: none;
}
.el-date-editor::v-deep .el-range__icon {
  position: absolute;
  right: 5px;
  top: 2px;
}
.el-date-editor::v-deep .el-input__inner {
  padding-left: 15px;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 5px;
}
.loginSelect {
  width: 120px;
  border-right: 1px solid #dae0e6;
}
.page-btn {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  border: none;
  outline: none;
  padding: 0 6px;
  background: none;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }

  i {
    width: 34px;
    height: 34px;
    background: #fff;
    border: 1px solid #d7dce2;
    border-radius: 2px;
    padding: 8px;
    font-size: 14px;
    color: #005ea4;
  }
}
</style>

<style scoped>
.info-box::v-deep .el-form-item__label {
  font-weight: 700;
}</style
>>
