<template>
  <div :id="id" class="chart"></div>
</template>
<script>
export default {
  props: {
    id: String,
    chartData: Object,
  },
  data() {
    return {
      title: "",
      chart: null,
    };
  },
  mounted() {
    this.initChart();
    window.onresize = () => {
      return (() => {
        this.chart.resize();
      })();
    };
    // console.log(this.chartData);
  },
  // filters: {
  //   numFilter(value) {
  //     // 截取当前数据到小数点后两位--四舍五入
  //     let realVal = parseFloat(value).toFixed(2);
  //     return realVal;
  //   },
  // },
  methods: {
    //柱状图初始化
    initChart() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      
      let _this=this;
      this.$nextTick(()=>{
            _this.setOptions();
      })
    
    },
    //配置
    setOptions() {
      let that = this;
      this.chart.setOption({
        grid: {
          top: "6%",
          left: "10%",
          right: "5%",
          bottom: "5%",
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false, //y轴坐标点消失
          },
          axisLabel: {
            color: "#fff",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          type: "category",
          data: that.chartData.yArr,
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#fff",
          },
        },
        series: [
          {
            type: "bar",
            data:that.chartData.xArr,
            backgroundStyle: {
              color: "#00FCFF",
            },
            barWidth: 21,
            itemStyle: {
              normal: {
                //柱体的颜色
                //右，下，左，上（1，0，0，0）表示从正右开始向左渐变
                color: function (params) {
                  // console.log(params);
                  var colorList = [
                    ["#0091FF ", "#0042A4"],
                    ["#00FFFF", "#067D68"],
                    ["#901698", "#F157EE"],
                    ["#40FBCB", "#009871"],
                  ];

                  var colorItem = colorList[params.dataIndex];
                  return new that.$echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: colorItem[0],
                      },
                      {
                        offset: 1,
                        color: colorItem[1],
                      },
                    ],
                    false
                  );
                },
              },
            },
          },
        ],
      });
    },
    //容量转换
    // diskSize(num) {
    //   if (num == 0) return "0 B";
    //   var k = 1024; //设定基础容量大小
    //   var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
    //   var i = 0; //单位下标和次幂
    //   for (var l = 0; l < 8; l++) {
    //     //因为只有8个单位所以循环八次
    //     if (num / Math.pow(k, l) < 1) {
    //       //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
    //       break; //小于1跳出循环
    //     }
    //     i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
    //   } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  所以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
    //   return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    // },
  },
};
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
