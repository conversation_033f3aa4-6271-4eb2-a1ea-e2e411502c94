<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="mainWrapper">
        <div class="mainBox">
          <div class="filter-container">
            <el-input
              v-model="listQuery.title"
              placeholder="产品名称/品牌"
              style="width: 200px;"
              class="filter-item"
            />
            <el-button v-waves class="filter-item2" type="primary" icon="el-icon-search">搜索</el-button>
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="dialogFormVisible = true"
            >添加</el-button>
          </div>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="name" label="产品名称" width="260"></el-table-column>
            <el-table-column prop="brand" label="品牌"></el-table-column>
            <el-table-column prop="status" label="状态" width="120"></el-table-column>
            <el-table-column prop="version" label="版本号" width="120"></el-table-column>

            <el-table-column label="操作" width="160" align="center">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
                <el-button @click="dialogEditVisible = true" type="text" size="small">修改</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="50"
          ></el-pagination>
        </div>
      </div>
    </el-scrollbar>
    <el-dialog title="添加产品" :visible.sync="dialogFormVisible" :append-to-body="true"  top="0">
      <el-form :model="form">
        <el-form-item label="产品名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="品牌" :label-width="formLabelWidth">
          <el-select v-model="form.region" placeholder="请选择品牌">
            <el-option label="奇安信" value="qanx"></el-option>
            <el-option label="山石" value="ss"></el-option>
            <el-option label="闪捷" value="sj"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-radio v-model="radio" label="1">开通</el-radio>
          <el-radio v-model="radio" label="2">不开通</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改产品" :visible.sync="dialogEditVisible" :append-to-body="true">
      <el-form :model="form">
        <el-form-item label="产品名称" :label-width="formLabelWidth">
          <el-input v-model="form2.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="品牌" :label-width="formLabelWidth">
          <el-select v-model="form2.region" placeholder="请选择品牌">
            <el-option label="奇安信" value="qanx"></el-option>
            <el-option label="山石" value="ss"></el-option>
            <el-option label="闪捷" value="sj"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-radio v-model="radio2" label="1" aria-checked>开通</el-radio>
          <el-radio v-model="radio2" label="2">不开通</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogEditVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [
        {
          id: 1,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "开通",
          version: "v1.0"
        },
        {
          id: 2,
          name: "下一代防火墙",
          brand: "山石",
          status: "未开通",
          version: "v2.0"
        },
        {
          id: 3,
          name: "云主机安全",
          brand: "奇安信",
          status: "开通",
          version: "v1.0"
        },
        {
          id: 4,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "开通",
          version: "v1.0"
        },
        {
          id: 5,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "开通",
          version: "v1.0"
        },
        {
          id: 6,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "未开通",
          version: "v1.0"
        },
        {
          id: 7,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "开通",
          version: "v1.0"
        },
        {
          id: 8,
          name: "下一代防火墙",
          brand: "奇安信",
          status: "未开通",
          version: "v1.0"
        }
      ],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+id"
      },
      currentPage4: 1,
      downloadLoading: false,
      dialogFormVisible: false,
      form: {
        name: "",
        region: "",
        date1: "",
        date2: "",
        delivery: false,
        type: [],
        resource: "",
        desc: ""
      },
      formLabelWidth: "120px",
      dialogEditVisible: false,
      form2: {
        name: "下一代防火墙",
        region: "奇安信",
        delivery: false,
        type: [],
        resource: "",
        desc: ""
      },
       radio: "",
      radio2: "1"
    };
  },
  created() {},
  methods: {
    handleClick(row) {
      console.log(row);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  .mainBox {
    .filter-container {
      margin-bottom: 10px;
      .filter-item {
        margin-right: 10px;
      }
    }
    .el-pagination {
      margin-top: 10px;
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>

