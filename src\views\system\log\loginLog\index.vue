<template>
  <div>
    <!-- <div class="mainWrapper">
        <div class="mainBox"> -->
    <!-- <div class="header">
            <h3 class="title">操作日志</h3>
          </div> -->
    <div class="serch-box clearfix">
      <div class="filter-container">
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          @click="handleRefresh()"
          style="margin-right: 16px"
        >
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="search-container filter-item">
          <el-input
            v-model="listQuery.title"
            placeholder="用户名"
            style="width: 200px"
            v-on:input="search"
          />
          <span
            class="el-icon-search search-btn"
            @click="handleSearch()"
          ></span>
        </div>
      </div>
      <div class="page-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="sizes, prev,slot, next,total"
          :total="total"
        >
          <span class="pageNum">
            {{ this.listQuery.page }}
            <i class="divider">/</i>
            {{ totalPage }}
          </span>
        </el-pagination>
      </div>
    </div>
    <div class="table-box">
      <el-table-bar>
        <el-table :data="tableData" style="width: 100%; margin-bottom: 20px">
          <el-table-column
            prop="user_name"
            label="登录人"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="ip"
            label="ip"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="log_info"
            label="日志类型"
            sortable
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="create_time"
            label="时间"
            sortable
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </el-table-bar>
    </div>
    <!-- </div>
      </div> -->
  </div>
</template>
  <script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { loginLog } from "@/api/system.js";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [],
      total: 0,
      totalPage: 1,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: "",

      },
      currentPage: 1,
      downloadLoading: false,

    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = {
        keyWord: this.listQuery.title,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
        // logType: this.listQuery.logType,
      };
      console.log(data);
      console.log('22')
      //登录日志列表
      loginLog(data)
        .then((res) => {
          console.log(res);
          setTimeout(() => {
            this.listLoading.close();
          }, 200);
          this.tableData = res.data.rows;
          this.total = res.data.total_rows;
          this.currentPage = res.data.page;
          if (res.data.total_rows == 0) {
            this.totalPage = 1;
          } else {
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          }
        })
        .catch((error) => {
          console.log(error);
          this.listLoading.close();
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //刷新
    handleRefresh() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    handleClick(row) {
      // console.log(row);
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
  </script>
  <style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 245px);
}
</style>
  
  