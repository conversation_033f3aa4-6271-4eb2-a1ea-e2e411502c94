<template>
  <el-dialog
    width="640px"
    :visible.sync="visible"
    title="阈值设置"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="120px"
      @keyup.enter.native="dataFormSubmitHandle()"
    >
      <el-form-item label="主机名称" prop="hostName">
        <el-input v-model="dataForm.hostName" disabled />
      </el-form-item>
      <el-form-item label="阈值条件">
        <el-input value="大于等于" disabled />
      </el-form-item>
      <div class="rateName color-blue">CPU 阈值</div>
      <div class="form-group">
        <el-form-item label="警告阈值" prop="cpuWarnThreshold">
          <el-input v-model="dataForm.cpuWarnThreshold" />
        </el-form-item>
        <el-form-item label="严重阈值" prop="cpuSevereThreshold">
          <el-input v-model="dataForm.cpuSevereThreshold" />
        </el-form-item>
      </div>
      <div class="rateName color-blue">内存阈值</div>
      <div class="form-group">
        <el-form-item label="警告阈值" prop="memoryWarnThreshold">
          <el-input v-model="dataForm.memoryWarnThreshold" />
        </el-form-item>
        <el-form-item label="严重阈值" prop="memorySevereThreshold">
          <el-input v-model="dataForm.memorySevereThreshold" />
        </el-form-item>
      </div>
      <div class="rateName color-blue">磁盘阈值</div>
      <div class="form-group">
        <el-form-item label="警告阈值" prop="diskWarnThreshold">
          <el-input v-model="dataForm.diskWarnThreshold" />
        </el-form-item>
        <el-form-item label="严重阈值" prop="diskSevereThreshold">
          <el-input v-model="dataForm.diskSevereThreshold" />
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定 </el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
import { modifyTheHostRate, getDeviceInfo } from '@/api/modules/monitor';
var validNumberPass1 = (rule, value, callback) => {
  //包含小数的数字
  let reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g;
  if (value === '') {
    callback(new Error('告警阈值不能为空'));
  } else if (!reg.test(value)) {
    callback(new Error('请输入数字'));
  } else {
    callback();
  }
};

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: '', //主机id
        hostName: '', //主机名称
        userId: '',
        userName: '',
        cpuWarnThreshold: '', //    cpu警告阈值
        cpuSevereThreshold: '', //  cpu严重阈值
        memoryWarnThreshold: '', //   内存警告阈值
        memorySevereThreshold: '', //内存严重阈值
        diskWarnThreshold: '', //   磁盘警告阈值
        diskSevereThreshold: '', //  磁盘严重阈值
      },
    };
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'name']),
    dataRule() {
      return {
        cpuWarnThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
        cpuSevereThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'change' },
        ],
        memoryWarnThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
        memorySevereThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
        diskWarnThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
        diskSevereThreshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
      };
    },
  },

  created() {},
  mounted() {
    this.dataForm.userId = this.userid;
    this.dataForm.userName = this.name;
  },
  methods: {
    init() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields();
        Promise.all([]).then(() => {
          console.log(this.dataForm.id);
          if (this.dataForm.id) {
            this.getInfo();
          }
        });
      });
    },

    // 获取信息
    getInfo() {
      let data = {
        id: this.dataForm.id,
      };
      getDeviceInfo(data)
        .then((res) => {
          console.log(res);
          if (res.code !== 1) {
            return this.$message.error(res.msg);
          }
          this.dataForm = {
            ...this.dataForm,
            ...res.data,
          };

          // console.log(this.dataForm);
        })
        .catch(() => {});
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false;
          }

          let data = {
            id: this.dataForm.id,
            userId: this.dataForm.userId,
            userName: this.dataForm.userName,
            cpuWarnThreshold: Number(this.dataForm.cpuWarnThreshold), //    cpu警告阈值
            cpuSevereThreshold: Number(this.dataForm.cpuSevereThreshold), //  cpu严重阈值
            memoryWarnThreshold: Number(this.dataForm.memoryWarnThreshold), //   内存警告阈值
            memorySevereThreshold: Number(this.dataForm.memorySevereThreshold), //内存严重阈值
            diskWarnThreshold: Number(this.dataForm.diskWarnThreshold), //   磁盘警告阈值
            diskSevereThreshold: Number(this.dataForm.diskSevereThreshold), //  磁盘严重阈值
          };
          console.log(data);
          this.$confirm(
            '若单独设置此项，再次设置全局阈值时对该主机的阈值设置无效, 是否继续?',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              modifyTheHostRate(data)
                .then((res) => {
                  if (res.code !== 1) {
                    return this.$message.error(res.msg);
                  }
                  this.$message({
                    message: '自定义阈值修改成功',
                    type: 'success',
                    duration: 500,
                    onClose: () => {
                      this.visible = false;
                      this.$emit('refreshDataList');
                    },
                  });
                })
                .catch(() => {});
            })
            .catch(() => {
              // this.$message({
              //   type: 'info',
              //   message: '已取消自定义设置',
              // });
              this.visible = false;
            });
        });
      },
      1000,
      { leading: true, trailing: false }
    ),
  },
};
</script>
<style lang="scss" scoped>
.rateName {
  width: 110px;
  text-align: right;
  margin-bottom: 20px;
}
.color-blue {
  color: #108ff4;
}
.color-red {
  color: #f95b6c;
}
.form-group {
  .el-form-item {
    width: 50%;
    float: left;
  }
}
</style>
