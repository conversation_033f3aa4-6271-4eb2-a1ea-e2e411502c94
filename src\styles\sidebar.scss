#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    padding: 0 12px;
    font-size: 0px;
    top: 48px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    border-right: 1px solid #eee;
    overflow: hidden;

    .change-nav {
      width: 239px;
      border-top: 1px solid #eee;
      padding: 12px 0;
      text-align: center;
      cursor: pointer;
      height: 40px;
      position: fixed;
      left: 0;
      bottom: 0;
    }

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 88px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 10px;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      color: #000;


    }


    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    .el-submenu__title{
      height: auto;
      padding: 6px 12px !important;
      margin: 12px 0 4px;
      line-height: 20px;
      font-size: 12px;
      color:#707275 !important;
      &:hover{
        background: none !important;
        cursor: text;
      }
    }

    // menu hover
    // el-submenu__title
    .submenu-title-noDropdown,
    .el-menu-item{
      &:hover {
        background-color: $menuHover !important;
      } 
      font-size: 14px !important;
    }

    .is-active>.el-submenu__title {
      color: #707275 !important;
    }

    .is-active>.svg-icon {
      color: #db2e43 !important;


    }
    & .nest-menu{
      margin-bottom: 4px;
      &:last-child{
        margin-bottom: 0;
      }
    }
    & .el-submenu .el-menu-item{
      padding: 0 0 0 12px !important;
      margin-top: 0;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      
    }
    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
  
      &:hover {
        background-color: $subMenuHover !important;
        color: $menuText !important;
      }

      &.is-active {
        background-color: #fff2ea !important;

      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .change-nav {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
      height:32px;
      line-height: 32px;
      margin-bottom: 4px;
      &.is-active .svg-icon {
        color: #db2e43 !important;
  
  
      }
      .el-tooltip {
        padding: 0 !important;
        text-align: center;

        .svg-icon {
          margin: 0;
        }
        

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }

        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }

    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}