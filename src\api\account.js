import request from '@/utils/request'
//用户管理接口
// 租户管理
export function tenant(data) {
  // debugger
  return request({
    url: '/api/user/userList',
    method: 'post',
    params:data 
  })
}
// 账号管理
//租户角色
export function roleList(roledata) {
  // debugger
  return request({
    url: '/api/role/roleMenuList',
    method: 'post',
    params:roledata
  })
}
//账号-租户添加
export function addAccount(data) {
  // debugger
  return request({
    url: '/api/user/addUser',
    method: 'post',
    params:data
  })
}

//账号修改
export function editAccount(data) {
  // debugger
  return request({
    url: '/api/user/editUser',
    method: 'post',
    params:data
  })
}
//租户管理修改
export function editTenant(data) {
  // debugger
  return request({
    url: '/api/user/modifyTenantInfoById',
    method: 'post',
    params:data
  })
}
//租户-账号管理删除
export function delTenant(id) {
  // debugger
  return request({
    url: '/api/user/deleteUserById',
    method: 'post',
    params:{id:id}
  })
}
//账号管理-重置密码
export function resetPwd(data) {
  // debugger
  return request({
    url: '/api/user/resetPassword',
    method: 'post',
    params:data
  })
}





