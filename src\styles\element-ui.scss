// cover some element-ui styles

$border_color:#e6e6e6;
$light_gray:#fff;
$cursor: #fff;

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  .el-dropdown-menu__item {
    font-size: 14px;
  }

  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-carousel__button {
  background-color: #1989fa !important;
}

.el-carousel__indicators--outside button {
  background: #eee !important;
}

.el-table {
  // 解决table组件内容滚动时页面滚动条同步滚动
  overflow: auto;
  // 必须设置
  position: relative;
}

//解决在IE下 table 表格宽度不是100%的问题
.el-table__header {
  width: 100% !important;
}

.el-table__body {
  width: 100% !important;
}

.el-table th,
.el-table td {
  font-size: 14px;

}

.el-table th {
  background: #fff;
}

.el-table .el-table__row:hover {
  background-color: #f3faff !important;

}

.el-input__inner {
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  border: 1px solid #dae0e6;
  border-radius: 2px;
}

.el-input__inner:focus {
  border-color: #db2e43;
}

.el-button {
  font-size: 14px;
  padding: 0 14px;
  height: 34px;
  line-height: 1.5;
  border-radius: 2px;
}

.el-table--border,
.el-table--group,
.el-table td,
.el-table th.is-leaf,
.el-table--border td,
.el-table--border th,
.el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
  border-color: $border_color;
}

.el-form-item__label {
  font-size: 14px;
  font-weight: 400;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input__icon {
  line-height: 34px;
}

.el-table .cell {
  white-space: nowrap;
  line-height: 1.5;
}

.el-form-item__content,
.el-form-item__label {
  line-height: 34px;
}

.el-button--default {
  background: #e5e5e5;
  border: none;
  color: #4a4c4f
}

.el-button--default:focus,
.el-button--default:hover {
  background-color: #dbdde0;
  color: #4a4c4f
}

.el-button--primary:link {
  background-color: #db2e43;
  border-color: #db2e43;
}

.el-button--primary:focus,
.el-button--primary,
.el-button--primary:hover,
.el-button--primary.is-active,
.el-button--primary:active {
  color: #fff;
  background-color: #db2e43;
  border-color: #db2e43;
}



.el-button+.el-button {
  margin-left: 0;
}

.search-container {
  display: inline-block;
  font-size: 14px;
  height: 34px;

  border: 1px solid #dae0e6;
  border-radius: 2px;
  width: 230px;
  position: relative;
  box-sizing: border-box;

  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: none;
    outline: none
  }

  .search-btn {
    width: 30px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: #999;
  }

}

.el-pagination__sizes .el-input .el-input__inner {
  font-size: 14px;
}

.el-input--mini .el-input__inner {
  height: 34px;
  line-height: 34px;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  width: 34px;
  height: 34px;
  background: #fff;
  border: 1px solid #d7dce2;
  border-radius: 2px;
  padding: 8px;
  font-size: 14px;
  color: #db2e43;
}

.btn-prev .el-icon-arrow-left:before {
  content: "\e792";
}

.btn-next .el-icon-arrow-right:before {
  content: "\e791";
}

.el-pagination .btn-next .el-icon:hover,
.el-pagination .btn-prev .el-icon:hover {
  color: #db2e43;

}

.el-pager li {
  line-height: 34px;
}

.el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  ;
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  margin-left: 10px;
}

.dialog-footer {
  .el-button+.el-button {
    margin-left: 10px;
  }

}

.el-dialog__body {
  .el-form {
    padding-right: 30px;
  }
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #db2e43;
}

// .el-input__prefix{
//   left: inherit;
//   right: 5px;
// }
.el-tabs--border-card>.el-tabs__header {
  background: #fff;
  border: none;

  .el-tabs__nav {
    margin-top: 2px;
  }
}

.el-tabs--border-card .el-tabs__item {
  border: 1px solid #D9D9D9 !important;

  border-right: 0 !important;
}

.el-tabs--border-card .el-tabs__item:last-child {
  border-right: 1px solid #D9D9D9 !important;

}

.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  color: #005EA4;
  background: rgba(218, 239, 255, 1);



}

.el-tabs--border-card>.el-tabs__header .el-tabs__item:first-child {
  margin-left: 0;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #005EA4;
}

.el-tabs--border-card>.el-tabs__content {
  padding: 0;
}

.el-tooltip__popper.is-light {
  color: #fff;
  border: 1px solid #0090FE;
  background: rgba(0, 81, 141, 0.3);
}

.el-tooltip__popper[x-placement^=top].is-light .popper__arrow::after {

  border-top-color: #0090FE !important;


}

.el-tooltip__popper[x-placement^=top].is-light .popper__arrow {

  border-bottom-color: #0090FE !important;

}

.el-tooltip__popper[x-placement^=bottom].is-light .popper__arrow::after {

  border-bottom-color: #0090FE !important;


}

.el-tooltip__popper[x-placement^=bottom].is-light .popper__arrow {

  border-bottom-color: #0090FE !important;

}

.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
  content: '';
  margin-right: 0;
}

.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label::after {
  content: '*';
  color: #db2e43;
  margin-left: 4px;
}

.el-dialog__footer {
  border-top: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

}

.el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

.el-popup-parent--hidden .fixed-header {
  padding-right: 0 !important;
}

.menudata-box .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 10px;
}

.menudata-box .avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.menudata-box .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 64px;
  height: 64px;
  line-height: 64px;
  text-align: center;
}

.menudata-box .avatar {
  width: 64px;
  height: 64px;
  display: block;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #fff2ea;
  color: #db2e43;
}

.el-switch {
  &.is-checked {
    .el-switch__core {
      border-color: #db2e43;
      background-color: #db2e43;
    }
  }
}
