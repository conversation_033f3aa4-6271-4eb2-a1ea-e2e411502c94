<template>
  <div :id="id" :style="style"></div>
</template>

<script>
import axios from 'axios';
import { worldList } from '@/api/world';

export default {
  name: 'WorldMap',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
      dataArr: [],
      geoCoordMap: {}, //世界地图经纬度集合
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(val) {
        this.$nextTick(() => {
          this.getWorld(val);
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.getWorld(this.chartData);
    });
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    getWorld(chartData) {
      let that = this;
      // console.log('攻击点数据');
      // console.log(chartData);
      // axios.get('/world.json').then(function (res) {
      // console.log(res);
      // })
      let dataArr = chartData.data;
      let nameMap = {};
      that.geoCoordMap = {};
      worldList()
        .then((res) => {
          console.log('世界地图');
          console.log(res);
          res.data.forEach((item) => {
            nameMap[item.country] = item.cityZh;
            that.geoCoordMap[item.cityZh] = [item.lng, item.lat];
          });
          // console.log(nameMap);
          // console.log(that.geoCoordMap);
          that.init(nameMap, dataArr);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    init(name, data) {
      this.chart = this.$echarts.init(document.getElementById(this.id));

      this.$nextTick(() => {
        this.setOption(name, data);
      });
      // 监听屏幕变化自动缩放图表
      window.addEventListener('resize', this.chart.resize);
    },
    setOption(name, data) {
      const that = this;
      var convertData = function (data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var geoCoord = that.geoCoordMap[data[i].cityZh];
          if (geoCoord) {
            res.push({
              name: data[i].cityZh,
              value: geoCoord.concat(data[i].attackCount),
            });
          }
        }
        // console.log(res);
        return res;
      };

      let option = {};
      option = {
        grid: {
          width: '100%',
          height: '100%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true,
        },
        // 提示框组件
        tooltip: {
          trigger: 'item', // 触发类型, 数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 使用函数模板  传入的数据值 -> value: number | Array
          formatter: function (val) {
            // console.log(val);
            if (val.data == null) return;
            return '地区：' + val.data.name + '</br>' + '攻击次数: ' + val.data.value[2];
          },
        },
        // 视觉映射组件
        visualMap: {
          show: false,
          min: 0,
          max: 1000,
          text: ['High', 'Low'],
          realtime: false,
          calculable: true,
          zlevel: 2,
          textStyle: {
            color: '#fff',
          },
          seriesIndex: 0,
          // color: ['#23346A', '#334F99', '#3E5DAB','#497AC4'],
          inRange: {
            //定义 在选中范围中 的视觉元素
            color: ['rgba(80,138,203,1)', 'rgba(56,89,166,1)', 'rgba(33,48,99,1)'],
          },
          outOfRange: {
            //定义 在选中范围外 的视觉元素。
            color: ['rgba(35,51,106,1)', 'rgba(31,45,90,1)', 'rgba(19,30,66,1)'],
          },
        },
        geo: [
          {
            map: 'world',
            label: {
              show: false,
              color: '#fff',
            },
            zoom: 1.2,
            roam: true,
            silent: false,
            nameMap: name,
            zlevel: 1,
            itemStyle: {
              areaColor: {
                type: 'radial',
                x: 0.6,
                y: 0.6,
                r: 1.3,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(55,85,160,1)',
                  },

                  {
                    offset: 1,
                    color: 'rgba(49,67,124,1)',
                  },
                ],
                global: false,
              },
              borderWidth: 0.5,
              borderColor: 'rgba(255, 1255, 255, 1)',
              // shadowColor: 'rgba(55,85,160, 0.3)',
              // shadowBlur: 1,
              // shadowOffsetX: 10,
              // shadowOffsetY: 12,
              // opacity: 0.8,
            },
            emphasis: {
              label: {
                show: true, // 是否显示标签
                color: '#fff', // 文字的颜色 如果设置为 'auto'，则为视觉映射得到的颜色，如系列色
              },
              itemStyle: {
                areaColor: '#5086c9', // 地图区域的颜色
                shadowColor: 'none',
              },
            },
          },
          // {
          //   map: 'world',
          //   label: {
          //     show: false,
          //     color: '#fff',
          //   },
          //   zoom: 1.2,
          //   roam: true,
          //   silent: false,
          //   nameMap: name,
          //   zlevel: 0,
          //   itemStyle: {
          //     areaColor: 'rgba(255,255,255,.8)',
          //     borderWidth: 0.5,
          //     borderColor: 'rgba(255, 1255, 255, 1)',
          //     shadowColor: 'rgba(55,85,160, 0.3)',
          //     shadowBlur: 1,
          //     shadowOffsetX: 10,
          //     shadowOffsetY: 12,
          //     opacity: 0.8,
          //   },
          //   emphasis: {
          //     label: {
          //       show: false, // 是否显示标签
          //       color: '#fff', // 文字的颜色 如果设置为 'auto'，则为视觉映射得到的颜色，如系列色
          //     },
          //     itemStyle: {
          //       areaColor: 'none', // 地图区域的颜色
          //       shadowColor: 'none',
          //     },
          //   },
          // },
        ],
        series: [
          {
            type: 'map', // 类型
            // 系列名称，用于tooltip的显示，legend 的图例筛选 在 setOption 更新数据和配置项时用于指定对应的系列
            name: '世界地图',
            mapType: 'world', // 地图类型
            // 是否开启鼠标缩放和平移漫游 默认不开启 如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move' 设置成 true 为都开启
            roam: true,
            zoom: 1.2,
            // 图形上的文本标签
            label: {
              show: false, // 是否显示对应地名
              color: '#fff',
            },
            geoIndex: 0,
            zlevel: 2,
            // 地图区域的多边形 图形样式
            // itemStyle: {
            //   areaColor: '#fff', // 地图区域的颜色 如果设置了visualMap，areaColor属性将不起作用
            //   borderWidth: 0.5, // 描边线宽 为 0 时无描边
            //   borderColor: 'rgba(255,255,255,1)', // 图形的描边颜色 支持的颜色格式同 color，不支持回调函数
            //   borderType: 'solid', // 描边类型，默认为实线，支持 'solid', 'dashed', 'dotted'
            // },
            // 高亮状态下的多边形和标签样式
            emphasis: {
              label: {
                show: true, // 是否显示标签
                color: '#fff', // 文字的颜色 如果设置为 'auto'，则为视觉映射得到的颜色，如系列色
              },
              itemStyle: {
                areaColor: '#5086c9', // 地图区域的颜色
              },
            },
            // 自定义地区的名称映射
            nameMap: name,
            // 地图系列中的数据内容数组 数组项可以为单个数值
            data: convertData(data),
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            zlevel: 3,
            symbolSize: function (val) {
              // console.log(val);
              return val[2] / 10;
            },
            visualMap: false,
            showEffectOn: 'render',
            rippleEffect: {
              //涟漪特效
              period: 4, //动画时间，值越小速度越快
              brushType: 'stroke', //波纹绘制方式 stroke, fill
              scale: 3, //波纹圆环最大限制，值越大波纹越大
              color: '#f4e925',
            },
            symbolSize: 10,
            Symbol: 'circle',
            // symbolRotate: 45,
            emphasis: {
              scale: false,
            },
            label: {
              formatter: '{b}',
              position: 'right',
              show: false,
            },
            itemStyle: {
              shadowBlur: 10,
              shadowColor: '#fff',
              color: '#f4e925',
            },
            data: convertData(data),
          },
        ],
      };

      this.chart.setOption(option, true);
      // this.chart.on('georoam', function (params) {
      //   var option = that.chart.getOption(); //获得option对象
      //   if (params.zoom != null && params.zoom != undefined) {
      //     //捕捉到缩放时
      //     option.geo[1].zoom = option.geo[0].zoom; //下层geo的缩放等级跟着上层的geo一起改变
      //     option.geo[1].center = option.geo[0].center; //下层的geo的中心位置随着上层geo一起改变
      //   } else {
      //     //捕捉到拖曳时
      //     option.geo[1].center = option.geo[0].center; //下层的geo的中心位置随着上层geo一起改变
      //   }
      //   that.chart.setOption(option); //设置option
      // });
    },
  },
};
</script>
