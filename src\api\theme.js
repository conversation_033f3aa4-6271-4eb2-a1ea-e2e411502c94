import request from '@/utils/request'
//主题外观-查找所有
export function getAllTheme() {
  return request({
    url: '/api/getAll',
    method: 'post',

  })
}
//主题外观-修改主题
export function update(data) {
  // debugger
  return request({
    url: '/api/update',
    method: 'post',
    params:data
  })
}
//主题外观-轮播时间设置
export function addTime(data) {
  // debugger
  return request({
    url: '/api/addTime',
    method: 'post',
    params:data
  })
}


