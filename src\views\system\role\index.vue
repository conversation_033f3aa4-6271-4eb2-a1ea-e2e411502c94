<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">角色管理</h3>
        </div>
        <div class="serch-box clearfix">
          <div class="filter-container">
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              @click="handleRefresh()"
            >
              <svg-icon icon-class="refresh" />
            </el-button>
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-plus"
              @click="addButClick()"
              >添加</el-button
            >
            <div class="search-container">
              <el-input
                v-model="listQuery.title"
                placeholder="角色名称"
                style="width: 200px"
                class="filter-item"
                v-on:input="search"
              />
              <span
                class="el-icon-search search-btn"
                @click="handleSearch()"
              ></span>
            </div>
          </div>
          <div class="page-box">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="10"
              layout="sizes, prev,slot,next,total"
              :total="total"
            >
              <span class="pageNum">
                {{ this.listQuery.page }}
                <i class="divider">/</i>
                {{ totalPage }}
              </span>
            </el-pagination>
          </div>
        </div>
        <div class="table-box">
          <el-table-bar>
            <el-table
              :data="tableData"
              style="width: 100%; margin-bottom: 20px"
            >
              <el-table-column
                prop="roleName"
                label="名称"
                sortable
              ></el-table-column>
              <el-table-column
                prop="remark"
                label="别名"
                sortable
              ></el-table-column>
              <el-table-column prop="tenant" label="租户名称"></el-table-column>

              <el-table-column label="操作" width="260" align="center">
                <template slot-scope="scope">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <div @click="handleUpdate(scope.row)" class="opt">
                        修改
                      </div>
                      <div @click="handleDel(scope.row)" class="opt">删除</div>
                      <div @click="handleAuth(scope.row)" class="opt">
                        权限配置
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </el-table-bar>
        </div>
      </div>
    </div>

    <el-dialog
      title="修改角色"
      :visible.sync="dialogEditVisible"
      top="0"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" ref="editForm" :rules="accountRules">
        <el-form-item
          label="角色名称"
          :label-width="formLabelWidth"
          prop="roleName"
        >
          <el-input v-model="editForm.roleName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="别名" :label-width="formLabelWidth" prop="remark">
          <el-input v-model="editForm.remark" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit('editForm')" v-dbClick
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="权限配置"
      :visible.sync="dialogAuthVisible"
      width="30%"
      top="0"
      :close-on-click-modal="false"
    >
      <div class="menudata-box">
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="treedata-content">
            <el-input
              placeholder="输入关键字搜索"
              v-model="filterText"
              @click.stop.native="dialogAuthVisible = true"
            ></el-input>

            <el-tree
              class="filter-tree"
              :data="data"
              show-checkbox
              :props="defaultProps"
              node-key="id"
              default-expand-all
              highlight-current
              :default-checked-keys="checkedArr"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              ref="tree"
              :check-strictly="false"
            ></el-tree>
          </div>
        </el-scrollbar>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAuthVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveAuth()" v-dbClick
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import {
  roleList,
  roleAdd,
  roleEdit,
  roleDel,
  roleAuth,
  menuList,
  roleMenuList,
} from '@/api/system.js';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { mapGetters } from 'vuex';
import { Loading } from 'element-ui';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: '',
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: '',
        type: '',
        logType: '',
        sort: '+id',
      },
      currentPage: 1,
      totalPage: 2,
      formLabelWidth: '120px',
      dialogEditVisible: false,
      dialogAuthVisible: false,
      editForm: {
        id: '', // 角色id
        roleName: '', //角色key
        remark: '', //角色名称
      },
      accountRules: {
        roleName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '别名不能为空', trigger: 'blur' }],
      },
      roleId: '', // 角色id
      roleName: '',
      menuId: '', //菜单id 逗号分割： 1，2
      menuList: [],
      menuArr: [],
      filterText: '',
      data: [], //菜单列表
      defaultProps: {
        children: 'children',
        label: (data, node) => {
          //console.log(data, node);
          return data.name;
        },
      },
      rolesList: [], //菜单
      checkedArr: [],
    };
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'tenantid']),
  },
  created() {
    this.getData();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      let data = {
        userId: this.userid, //用戶id
        userType: this.usertype, //用戶类型
        keyWord: this.listQuery.title,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
        logType: this.listQuery.logType,
      };
      //  console.log(data);
      //角色管理列表

      roleList(data)
        .then((res) => {
          // console.log(res);
          setTimeout(() => {
            this.listLoading.close();
          }, 200);

          this.tableData = res.data.rows;
          this.total = res.data.total_rows;
          this.currentPage = res.data.page;
          if (res.data.total_rows == 0) {
            this.totalPage = 1;
          } else {
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);
          }
        })
        .catch((error) => {
          console.log(error);
          this.listLoading.close();
        });
      //角色权限菜单
      let roleData = {
        userId: this.userid,
        tenantId: this.tenantid,
      };
      roleMenuList(roleData)
        .then((res) => {
          // console.log(res);
          this.rolesList = res.obj;
          // console.log(this.rolesList);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    addButClick() {
      this.$router.push({ path: `/system/role/create` });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
    handleRefresh() {
      this.getData();
    },

    //角色修改按钮
    handleUpdate(row) {
      // console.log(row);
      this.dialogEditVisible = true;
      this.editForm.id = row.id;
      this.editForm.roleName = row.roleName;
      this.editForm.remark = row.remark;
    },
    //角色修改
    handleSaveEdit(editForm) {
      this.$refs[editForm].validate((valid) => {
        if (valid) {
          // console.log(this.editForm);
          roleEdit(this.editForm)
            .then((res) => {
              // console.log(res);
              if (res.code == 1) {
                this.dialogEditVisible = false;
                this.getData();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                });
              }
            })
            .catch((error) => {
              this.dialogEditVisible = false;
              console.log(error);
            });
        } else {
          this.$message.error({
            message: '修改失败',
          });
          return false;
        }
      });
    },
    //角色删除
    handleDel(row) {
      // console.log(row);
      let roleId = row.id;
      this.$confirm('确认要删除吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then((e) => {
          // console.log(e);
          if (e === 'confirm') {
            roleDel(roleId)
              .then((res) => {
                // console.log(res);
                if (res.code == 1) {
                  this.getData();
                  this.$message({
                    message: '删除成功',
                    type: 'success',
                  });
                }
              })
              .catch((error) => {
                this.$message.error({
                  message: '删除失败',
                });
              });
          }
        })
        .catch((e) => { });
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
    lookForAllId(data = [], arrIds = [], treeNames = []) {
      for (let item of data) {
        arrIds.push(item.id);
        treeNames.push(item.name);
        if (item.children && item.children.length)
          this.lookForAllId(item.children, arrIds, treeNames);
      }

      return { arrIds, treeNames };
    },
    //菜单列表
    getMenuData() {
      menuList()
        .then((res) => {
          console.log(res.data);
          this.data = res.data;
          this.$nextTick(() => {
            let defaultCheckedKeys = [];
            if (this.rolesList.length > 0) {
              this.rolesList.forEach((item) => {
                if (item.roleName == this.roleName) {
                  item.menuList.forEach((item) => {
                    let checkedItem = item;
                    //  console.log(checkedItem);
                    let arrIds = this.lookForAllId(this.data).arrIds;
                    let treeNames = this.lookForAllId(this.data).treeNames;
                    for (let i = 0; i < treeNames.length; i++) {
                      if (checkedItem == treeNames[i]) {
                        defaultCheckedKeys.push(arrIds[i]);
                      }
                    }
                  });
                }
              });
              const treeDataRef = this.$refs.tree;
              console.log(defaultCheckedKeys);

              for (const key of defaultCheckedKeys) {
                // getNode（获取tree中对应的节点）
                console.log(key);
                const node = treeDataRef.getNode(key);
                // isLeaf（判断节点是否为叶子节点）
                // 如果存在isLeaf 代表是叶子节点为最后一级那么就选中即可 不是则不选择
                // console.log(node);
                if (node.isLeaf) {
                  // setChecked （设置tree中对应的节点为选中状态）
                  treeDataRef.setChecked(node, true);
                }
              }
            }
          });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    filterNode(value, data) {
      // console.log(value);
      // console.log(data);
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      // console.log(data);
    },
    //通过 node 获取菜单
    getCheckedNodes() {
      // console.log(this.$refs.tree.getCheckedNodes());
      this.menuList = this.$refs.tree
        .getHalfCheckedNodes()
        .concat(this.$refs.tree.getCheckedNodes());
      // console.log(this.menuList);
    },
    //权限配置
    handleAuth(row) {
      this.menuArr = [];
      this.checkedArr = [];
      // console.log(row);
      this.roleId = row.id;
      this.roleName = row.roleName;
      this.dialogAuthVisible = true;
      this.filterText = '';
      this.getMenuData();
    },
    //确定权限配置
    handleSaveAuth() {
      this.getCheckedNodes();
      this.menuArr = [];
      for (let i = 0; i < this.menuList.length; i++) {
        this.menuArr.push(this.menuList[i].id);
      }
      // console.log(this.menuArr);
      this.menuId = this.menuArr.join(',');
      // console.log(this.menuId);
      let data = {
        roleId: this.roleId,
        menuId: this.menuId,
      };
      // console.log(data);
      roleAuth(data)
        .then((res) => {
          // console.log(res.data);
          this.dialogAuthVisible = false;
          this.getData();
        })
        .catch((error) => {
          console.log(error);
          this.dialogAuthVisible = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 204px);
}

.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .filter-container {
      .filter-item {
        margin-right: 20px;
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.menudata-box {
  width: 100%;
  height: 360px;
  box-sizing: border-box;

  background: #fff;
  .el-scrollbar {
    height: 100%;
    .treedata-content {
      padding: 0 30px;
    }
    .el-input {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}
</style>
