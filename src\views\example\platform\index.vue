<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
      
      <div >
        <div class="container">
        <div class="filter-container">
          <el-input
            v-model="listQuery.title"
            placeholder="产品/租户名"
            style="width: 200px;"
            class="filter-item"
          />

          <el-button v-waves class="filter-item2" type="primary" icon="el-icon-search">搜索</el-button>
        </div>
        <el-table :data="tableData" style="width: 100%;" row-key="id" border>
         
          <el-table-column prop="name" label="产品名称" sortable width="180"></el-table-column>
          <el-table-column prop="brand" label="品牌" sortable></el-table-column>
          <el-table-column prop="tentant" label="租户" sortable></el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="50"
        ></el-pagination>
      </div>

      </div>
    </div>
  </el-scrollbar>
</template>

<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
export default {
     components: { Pagination },
  directives: { waves },
  data() {
    return {
     
       account: "",
      
      tableData: [
        {
            id:"1",
          name: "云WAF",
          brand:'山石网科',
          tentant:'test001'

        },
        {
         id:"2",
          name: "云堡垒机",
          brand:'云安保',
          tentant:'test001'
        },
        {
             id:"3",
          name: "云主机安全",
          brand:'奇安信',
          tentant:'test006'
        },
        {
          id:"4",
          name: "云日志审计",
          brand:'山石网科',
          tentant:'test007'
        },
        {
         id:"5",
          name: "云防火墙",
          brand:'山石网科',
          tentant:'test008'
        },
        {
             id:"6",
          name: "云数据库审计",
          brand:'闪捷',
          tentant:'test008'
        },
          {
               id:"7",
          name: "云WAF",
          brand:'山石网科',
          tentant:'test002'

        },
        {
         id:"8",
          name: "云堡垒机",
          brand:'云安保',
          tentant:'test009'
        },
        {
             id:"9",
          name: "云主机安全",
          brand:'奇安信',
          tentant:'test0010'
        },
      ],
       total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+id"
      },
      currentPage4: 1,
      downloadLoading: false,
      formLabelWidth: "120px",
     
    };
  },
  created() {},
  methods: {
      handleClick(row){
         console.log(row);
         // this.$router.push({path:`/nested/account/edit/${row.id}`})
         if(row.name=='云防火墙'){
            
            this.$router.push({path:`/example/platform/firewall/${row.id}`})

         }else if(row.name=='云WAF'){
             this.$router.push({path:`/example/platform/waf/${row.id}`})

         }else if(row.name=='云日志审计'){
              this.$router.push({path:`/example/platform/logAudit/${row.id}`})
             
         }else if(row.name=='云堡垒机'){
              this.$router.push({path:`/example/platform/fortress/${row.id}`})
             
         }else if(row.name=='云数据库审计'){
              this.$router.push({path:`/example/platform/database/${row.id}`})
             
         }else if(row.name=='云主机安全'){
              this.$router.push({path:`/example/platform/host/${row.id}`})
             
         }

      },
        handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

  .container {
    padding: 20px 10px;
    background: #fff;
    border-radius: 3px;
    height:auto;
    .filter-container {
      margin-bottom: 10px;
      .filter-item {
        margin-right: 10px;
      }
    }
    .el-pagination {
      margin-top: 10px;
    }
    .normal{
        color: #409EFF;
    }
    .abnormal{
        color: #F56C6C;
    }
  }
}
</style>
