<template>
  <div :id="id" :style="style"></div>
</template>

<script>
import moment from 'moment';
import { unitConvert } from '@/utils/calculate';
export default {
  name: 'FlowStatistics',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.init();
    });
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      this.$nextTick(() => {
        this.setOption();
        // console.log(this.chartData)
      });
      window.addEventListener('resize', this.chart.resize);
    },
    setOption() {
      const that = this;
      let option = {};
      option = {
        color: '#1389E1',
        grid: {
          top: '30px',
          left: '30px',
          right: '20px',
          bottom: '20px',
          // containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var result = '';
            // console.log(params);
            params.forEach(function (item,i) {
              // console.log(item,i);
              result +=
                item.marker +
                ' ' +
                item.seriesName +
                ' : ' +
                unitConvert(item.data) +
                '</br>'
                // item.marker +
                // ''+
                // '最大下行流量'+
                // ':'+
                // unitConvert(that.chartData.yAxisMaxDeviceIn[i])+
                // '</br>'+
                // item.marker +
                // ''+
                // '最大上行流量'+
                // ':'+
                // unitConvert(that.chartData.yAxisMaxDeviceOut[i])+
                // '</br>'
                
            });
            return result;
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [2,3,4,5,6,7,8,9,0,1],
          // data: this.chartData.xAxisData,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            showMaxLabel: true,
            color: '#97A4B6',
            fontSize: 16,
            align: 'center',
            interval: 6,
            formatter: function (value, index) {
              // console.log(value);
              let time;

              time = moment(value).format('MM/DD ');

              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
            lineStyle: {
              color: '#003660',
            },
          },

          axisTick: {
            // show: true,
            lineStyle: {
              color: '#D9D9D9',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#97A4B6',
            fontSize: 14,
            formatter: function (value, index) {
              // console.log(value);
              let capacity;
              capacity = unitConvert(value);
              return capacity;
            },
          },
          smooth: true,
          splitLine: {
            // show: true,
            lineStyle: {
              type: 'solid', //设置网格线类型 dotted：虚线   solid:实线
              color: '#003660',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            name:'下行',
            data: this.chartData.yAxisDeviceIn,
            // symbol: 'none',
            // type: 'line',
            // itemStyle:{
            //   opacity:0
            // }
          },
          {
            name:'上行',
            data: this.chartData.yAxisDeviceOut,
            // symbol: 'none',
            // type: 'line',
            // itemStyle:{
            //   opacity:0
            // }
          },
          {
            name:'最大下行流量',
            data: this.chartData.yAxisMaxDeviceIn,
            // symbol: 'none',
            // type: 'line',
            // show:false,
            itemStyle:{
              opacity:0
            }
          },
          {
            name:'最大上行流量',
            data: this.chartData.yAxisMaxDeviceOut,
            // symbol: 'none',
            // type: 'line',
            // show:false,
            itemStyle:{
              opacity:0
            }
          },
          {
            name:'最小下行流量',
            data: this.chartData.yAxisMinDeviceIn,
            // symbol: 'none',
            // type: 'line',
            // show:false,
            itemStyle:{
              opacity:0
            }
          },
          {
            name:'最小上行流量',
            data: this.chartData.yAxisMinDeviceOut,
            // symbol: 'none',
            // type: 'line',
            // show:false,
            itemStyle:{
              opacity:0
            }
          },
        ],
      };

      this.chart.setOption(option, true);
    },
  },
};
</script>
