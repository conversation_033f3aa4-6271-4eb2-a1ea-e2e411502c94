<template>
  <div class="login-container">
    <div class="login-box clearfix">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
        label-position="left"
      >
        <div class="top"></div>
        <div class="bottom"></div>
        <div class="title-container">
          <h3 class="title" :style="{ fontSize: titleSize + 'px' }">
            {{ loginTitle }}
          </h3>
          <h5 class="subtitle">welcome！</h5>
        </div>

        <el-form-item prop="username">
          <el-input
            ref="username"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            name="username"
            type="text"
            tabindex="1"
            auto-complete="off"
          >
            <span slot="prefix">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
            </span>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="请输入密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            @keyup.enter.native="handleLogin"
          >
            <span slot="prefix">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
            </span>
          </el-input>
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
        <div class="vcode-box clearfix">
          <el-form-item prop="vcode">
            <el-input
              ref="vcode"
              v-model="loginForm.vcode"
              placeholder="请输入验证码"
              name="vcode"
              type="text"
              tabindex="3"
              auto-complete="off"
            >
              <span slot="prefix">
                <span class="svg-container">
                  <svg-icon icon-class="vcode" />
                </span>
              </span>
            </el-input>
          </el-form-item>
          <div class="vcode-img-box">
            <img
              :src="vcodeImg"
              alt
              title="看不清？换一张"
              @click="changeCode()"
              ref="vcodeImg"
              v-if="showCode"
            />
          </div>
        </div>

        <el-button
          :loading="loading"
          type="primary"
          style="width: 100%; margin-top: 30px"
          @click.native.prevent="handleLogin"
          >登录</el-button
        >

        <!-- <div class="tips">
        <span style="margin-right:20px;">username: admin</span>
        <span>password:123456</span>
      </div> -->
      </el-form>
    </div>
    <div class="copyright-box">
      Copyright © 2022 中国电信安徽公司 All rights reserved.
    </div>
    <el-dialog
      top="0"
      width="640px"
      :visible.sync="dialogAuthVisible"
      :append-to-body="true"
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div slot="title">
        <span class="el-dialog__title">
          <img src="@/assets/auth-vcode.png" alt="" class="auth-img" />用户授权
        </span>
      </div>
      <el-form
        :model="authForm"
        :rules="accountRules"
        :label-position="labelPosition"
        style="padding: 0 30px"
      >
        <el-form-item label="申请码" :label-width="formLabelWidth">
          <!-- <el-input
                v-model="authForm.license"
                autocomplete="off"
              ></el-input> -->
          <div style="padding-right: 30px; display: block; position: relative">
            {{ authForm.license }}
            <img
              src="@/assets/copy.png"
              alt=""
              class="copy-img"
              :data-clipboard-text="authForm.license"
              title="复制"
              @click="copyLicence"
            />
          </div>
        </el-form-item>
        <el-form-item label="授权码" :label-width="formLabelWidth" prop="code">
          <el-input v-model="authForm.code" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth">
          <!-- <el-input
                v-model="authForm.license"
                autocomplete="off"
              ></el-input> -->
          <div style="display: block; position: relative; color: #8c97a8">
            产品功能暂不可用，请联系管理员提供授权码进行激活 !
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAuth">确定</el-button>
      </div>
    </el-dialog>
    <!-- <el-dialog
      top="0"
      width="480px"
      title="Ukey身份认证"
      :visible.sync="ukeyVisible"
      :append-to-body="true"
      :show-close="false"
      :close-on-click-modal="false"
      custom-class="ukey-dialog"
    >
      <el-alert
        title="检测到未插入UKey设备，请检测UKey设备是否成功插入"
        type="warning"
        show-icon
        :closable="false"
        v-show="ukeyCheck"
      >
      </el-alert>
      <el-form
        :model="ukeyForm"
        :rules="ukeyFormRules"
        :label-position="labelPosition"
        class="ruleForm-wrap"
        label-width="120px"
        ref="ukeyForm"
      >
        <el-form-item label="当前用户" prop="account">
          <el-input v-model="ukeyForm.account" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item label="设备列表" prop="deviceId" hidden>
          <el-input v-model="ukeyForm.deviceId"></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="pucSignature" hidden>
          <el-input v-model="ukeyForm.pucSignature"></el-input>
        </el-form-item>
        <el-form-item label="Ukey PIN码" prop="ukeyPin">
          <el-input
            v-model="ukeyForm.ukeyPin"
            :key="pinType"
            ref="password"
            :type="pinType"
            @focus="checkState"
          ></el-input>
          <span class="show-pwd" @click="showPwdKey">
            <svg-icon :icon-class="pinType === 'password' ? 'eye2' : 'eye-open'" />
          </span>
          <p>若遗忘UKey PIN码，请联系UKey厂家</p>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="hanleConfirmUkey()">确定</el-button>
        <el-button type="default" @click="cancleUkey">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      top="0"
      width="420px"
      title="控件加载"
      :visible.sync="setupVisible"
      :close-on-click-modal="false"
      custom-class="ukey-dialog"
    >
      <div style="padding: 20px">
        <div class="set-tips">
          <i class="el-icon-warning color_red"></i>
          已开启UKey设备登录，控件未安装,请先
          <router-link
            @click.native="jumpTo('https://*************/down/mPlugin_SKF_Setup.exe')"
            to=""
          >
            <span class="color_blue">点击安装</span></router-link
          >
        </div>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { validUsername } from "@/utils/validate";
import {
  login,
  vcode,
  isAuth,
  license,
  ukeyLogin,
  selectUkeyByUserName,
} from "@/api/user.js";
import { getAllTheme } from "@/api/theme.js";
import Clipboard from "clipboard";
import { mapGetters } from "vuex";

export default {
  name: "Login",
  data() {
    const validateUsername = (rule, value, callback) => {
      // if (!validUsername(value)) {

      //   callback(new Error("请输入正确的用户名"));
      // } else {
      //   callback();
      // }
      if (value == "") {
        callback(new Error("请输入用户名"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码不能少于6位"));
      } else {
        callback();
      }
    };
    const validateVode = (rule, value, callback) => {
      if (value.length < 4) {
        callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      loginTitle: "",
      loginImg: "",
      titleSize: "",
      labelPosition: "left",
      loginForm: {
        username: "",
        password: "",
        vcode: "",
        pucSignature: "",
        key: "",
      },
      authForm: {
        license: "",
        code: "",
      },
      dialogAuthVisible: false,
      vcodeImg: "/api/kaptcha/defaultKaptcha?",
      showCode: true,
      formLabelWidth: "90px",
      loginRules: {
        username: [{ required: true, trigger: "blur", validator: validateUsername }],
        password: [{ required: true, trigger: "blur", validator: validatePassword }],
        vcode: [{ required: true, trigger: "blur", validator: validateVode }],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      accountRules: {
        code: [{ required: true, message: "授权码不能为空", trigger: "blur" }],
      },
      ukeyForm: {
        account: "",
        ukeyPin: "",
        pucSignature: "",
        key: "",
        deviceId: "",
      },
      ukeyFormRules: {
        account: [{ required: true, trigger: "blur", message: "用户不能为空" }],
        ukeyPin: [{ required: true, trigger: "blur", message: "PIN码不能为空" }],
      },
      ukeyVisible: false,
      pinType: "password",
      ukeyCheck: false,
      seleDevices: "", //ukey设备
      ukeyState: "",
      setupVisible: false,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
        // console.log(this.redirect);
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["userid", "menus"]),
  },
  created() {
    this.getData();
    this.auth();
    let that = this;
    document.onkeydown = function (e) {
      e = window.event || e;
      if (that.$route.path == "/login" && (e.code == "Enter" || e.code == "enter")) {
        //验证在登录界面和按得键是回车键enter
        that.handleLogin(); //登录函数
      }
    };
  },
  methods: {
    jumpTo(url) {
      window.open(url, "_blank");
    },
    //验证ukey 状态
    // checkLoginStatus() {
    //   selectUkeyByUserName(this.loginForm.username)
    //     .then((res) => {
    //       console.log(res);
    //       if (res.code == 1) {
    //         this.ukeyState = res.data.state;
    //         if (res.data.state == 1) {
    //           this.ukeyForm.account = this.loginForm.username;
    //           this.checkUkey();
    //           this.ukeyVisible = true;
    //         } else {
    //         }
    //       } else {
    //         this.$message({
    //           message: res.data,
    //           type: "error",
    //         });
    //         return false;
    //       }
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    // },
    //ukey验签
    // checkUkey() {
    //   const sele_devices = enumDevice("GM3000");
    //   console.log(sele_devices);
    //   if (sele_devices == -1) {
    //     this.setupVisible = true;
    //   } else if (sele_devices != null || sele_devices != undefined) {
    //     this.seleDevices = sele_devices;
    //     this.ukeyCheck = false;
    //     this.setupVisible = false;
    //   } else {
    //     this.ukeyCheck = true;
    //     this.setupVisible = false;
    //   }
    // },
    // checkState() {
    //   this.checkUkey();
    // },
    // autograph() {
    //   //查找设备列表
    //   if (this.seleDevices != null || this.seleDevices != undefined) {
    //     this.ukeyForm.deviceId = this.seleDevices[0];
    //     this.ukeyForm.key = "12345678123456781234567812345678";
    //     //验证密码
    //     const passwordTip = verifyUserPin(this.seleDevices[0], this.ukeyForm.ukeyPin);
    //     console.log(passwordTip);
    //     if (passwordTip == "1") {
    //       // this.$message({
    //       //   message: "验证用户密码成功",
    //       //   type: "success",
    //       // });
    //       //列举容器证书getUserList()
    //       const userList = getUserList();
    //       console.log(userList);
    //       //签名数据
    //       const signdata = signData_P7(
    //         1,
    //         1,
    //         "1234567812345678",
    //         "12345678123456781234567812345678",
    //         userList[0]
    //       );
    //       console.log(signdata);
    //       this.ukeyForm.pucSignature = signdata;
    //       this.$nextTick(() => {
    //         console.log(this.ukeyForm);
    //         this.ukeyLoginPort();
    //       });
    //     } else {
    //       this.$message({
    //         message: passwordTip,
    //         type: "error",
    //       });
    //       return false;
    //     }
    //   } else {
    //     // this.$message({
    //     //   message: "未找到任何key,请检查Ukey设备是否插入",
    //     //   type: "warning",
    //     // });
    //     this.ukeyCheck = true;
    //   }
    // },
    // ukey登录
    // ukeyLoginPort() {
    //   ukeyLogin(this.ukeyForm)
    //     .then((res) => {
    //       console.log(res);
    //       let { data, code } = res;
    //       if (code == 1) {
    //         if (data.ukeyType == 1) {
    //           this.ukeyVisible = false;
    //           console.log(data.token);
    //           this.$store.dispatch("user/setToken", data.token);
    //           this.$router.push({ path: "/" });
    //         } else {
    //           this.$message({
    //             message: res.msg,
    //             type: "error",
    //           });
    //           return;
    //         }
    //       } else {
    //         this.$message({
    //           message: res.msg,
    //           type: "error",
    //         });
    //       }
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    // },
    //取消Ukey弹框
    // cancleUkey() {
    //   this.ukeyVisible = false;
    //   this.loginForm.username = "";
    //   this.ukeyForm.account = this.loginForm.username;
    //   this.$refs.ukeyForm.resetFields();
    //   console.log(this.ukeyForm);
    // },
    // hanleConfirmUkey() {
    //   this.$refs.ukeyForm.validate((callback) => {
    //     if (callback == false) {
    //       return false;
    //     }
    //   });
    //   this.autograph();
    // },

    getData() {
      getAllTheme()
        .then((res) => {
          // console.log(res);
          let that = this;
          if (res.length > 0) {
            res.forEach((item) => {
              if (item.type == 4) {
                //登录页
                that.loginImg = item.image;
                that.loginTitle = item.title;
                let arr = that.loginTitle.split("");
                //console.log(arr);
                if (arr.length > 26) {
                  that.titleSize = "10px";
                } else if (arr.length > 23) {
                  that.titleSize = "12px";
                } else if (arr.length > 20) {
                  that.titleSize = "14px";
                } else if (arr.length > 18) {
                  that.titleSize = "16px";
                } else if (arr.length > 12) {
                  that.titleSize = "18px";
                } else {
                  that.titleSize = "26px";
                }
              } else if (item.type == 1) {
                //浏览器
                localStorage.setItem("browseImg", item.image);
                localStorage.setItem("browseTitle", item.title);
              } else if (item.type == 2) {
                //网络大屏
                localStorage.setItem("screenImg", item.image);
                localStorage.setItem("screenTitle", item.title);
              } else if (item.type == 3) {
                //主页面
                localStorage.setItem("mainImg", item.image);
                localStorage.setItem("mainTitle", item.title);
              } else if (item.type == 6) {
                //密码大屏
                localStorage.setItem("psdScreenImg", item.image);
                localStorage.setItem("psdScreenTitle", item.title);
              }
            });
          }
          // if (res.code == 1) {

          // }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //验证码图片接口
    changeCode() {
      this.showCode = false;

      setTimeout(() => {
        this.vcodeImg = "/api/kaptcha/defaultKaptcha";
        this.showCode = true;
      }, 300);
    },
    showPwdKey() {
      if (this.pinType === "password") {
        this.pinType = "";
      } else {
        this.pinType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    //复制验证码
    copyLicence() {
      var clipboard = new Clipboard(".copy-img");
      clipboard.on("success", (e) => {
        // console.log('复制成功')
        this.$message.success("复制成功");
        //  释放内存
        clipboard.destroy();
      });
      clipboard.on("error", (e) => {
        // 不支持复制
        //  console.log('该浏览器不支持复制');
        this.$message.error("该浏览器不支持复制");
        // 释放内存
        clipboard.destroy();
      });
    },
    //授权
    handleAuth() {
      license(this.authForm.code)
        .then((res) => {
          // console.log(res);
          if (res.success == true) {
            this.dialogAuthVisible = false;
            this.$message.success("授权成功");
            // this.handleLogin();
          } else {
            this.$message.error("授权码错误");
            this.loading = false;
          }
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    auth() {
      isAuth()
        .then((res) => {
          // console.log(res);
          this.loading = false;
          //success等于false的时候代表第一次登录未授权，等于1是已经登陆过了
          if (res.success == false) {
            this.dialogAuthVisible = true;
            this.authForm.license = res.license;
          } else if (res.success == true) {
            // this.handleLogin();
            this.dialogAuthVisible = false;
          }
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    //菜单筛选
    // filterMenu(menu) {
    //   if (menu.length > 0) {
    //     if (menu[0].children.length > 0) {
    //       let child = menu[0].children;
    //       // console.log(child)
    //       this.filterMenu(child);
    //     } else {
    //       return menu[0].url;
    //     }
    //   }
    // },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let _this = this;
          let verificationCode = this.loginForm.vcode;
          vcode(verificationCode)
            .then((res) => {
              console.log(res);
              if (res.code == 1) {
                this.$store
                  .dispatch("user/login", _this.loginForm)
                  .then((response) => {
                    console.log(response);
                    this.loading = false;
                    // let { obj } = response;
                    // console.log(obj);
                    console.log("登录页");
                    this.$router.push({ path: "/" });
                    // if (obj.ukeyType == "1") {
                    //   this.$router.push({ path: "/" });
                    // } else {
                    //   this.ukeyVisible = true;
                    // }

                    // this.$router.push({ path: this.redirect || "/" });
                  })
                  .catch(() => {
                    this.loading = false;
                  });
              } else {
                this.$message.error("验证码错误，或已失效");
                this.loading = false;
              }
            })
            .catch((error) => {
              this.loading = false;
            });
        } else {
          console.log("error submit!!");
          this.loading = false;
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #eee;
$light_black: #333;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    input {
      border: 1px solid #ddd;
      background: none;
      border-radius: 3px;
      color: #fff;
      padding: 0 15px 0 40px;
      color: $cursor;
      height: 40px;
      line-height: 40px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    background: none;
    width: 320px;
    // border: 1px solid #ddd;
    // background: none;
    // border-radius: 3px;
    // color: #fff;
  }
  .vcode-box {
    width: 320px;
    margin: 0 auto;
    .el-form-item {
      width: 60%;
      float: left;
      .el-input {
        width: 100%;
      }
    }
    .vcode-img-box {
      width: 33%;
      float: right;
      text-align: right;
      height: 40px;
      line-height: 40px;
      img {
        cursor: pointer;
        vertical-align: middle;
        width: 100%;
        height: 40px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$bg: #003359;
$dark_gray: #889aa4;
$light_gray: #eee;
$light_black: #333;

.login-container {
  position: relative;
  width: 100%;
  min-height: 100%;
  background: url("../../assets/login-bg.png");
  background-size: 100% 100%;
  overflow: hidden;

  .login-box {
    position: absolute;
    top: 50%;
    left: 54%;
    transform: translateY(-50%);
    background: rgba(0, 30, 180, 0.08);
  }

  .login-form {
    position: relative;
    padding: 40px 50px;
    color: #fff;
    background: rgba(0, 30, 180, 0.08);
    border: 2px solid #dcdcdc;
    &::before {
      content: "";
      position: absolute;
      left: -4px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 189px;
      background: rgba(255, 255, 255, 0.39);
      box-shadow: 0px 0px 14px rgba(255, 255, 255, 0.88);
      border-radius: 50%;
      z-index: 2;
    }
    &::after {
      content: "";
      position: absolute;
      right: -4px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 189px;
      background: rgba(255, 255, 255, 0.39);
      box-shadow: 0px 0px 14px rgba(255, 255, 255, 0.88);
      border-radius: 50%;
      z-index: 2;
    }
    .top {
      content: "";
      position: absolute;
      top: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 189px;
      height: 4px;
      background: rgba(255, 255, 255, 0.39);
      box-shadow: 0px 0px 14px rgba(255, 255, 255, 0.88);
      border-radius: 50%;
      z-index: 2;
    }
    .bottom {
      position: absolute;

      bottom: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 189px;
      height: 4px;
      background: rgba(255, 255, 255, 0.39);
      box-shadow: 0px 0px 14px rgba(255, 255, 255, 0.88);
      border-radius: 50%;
      z-index: 2;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    color: #bcbcbc;
    vertical-align: middle;
    width: 30px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: #fff;
      margin-bottom: 10px;
      text-align: center;
      font-weight: 400;
    }
    .subtitle {
      font-size: 16px;
      text-align: center;
      font-weight: normal;
      margin-bottom: 26px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 5px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .el-button--primary::v-deep {
    height: 46px;
    line-height: 46px;
    background: #005fee;
    border-color: #005fee;
    &.is-disabled {
      color: #fff;
      background-color: #a0cfff;
      border-color: #a0cfff;
    }
  }
  .el-form-item::v-deep {
    margin: 0 auto 22px auto;
  }
  .copyright-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 50px;
    text-align: center;
    color: #e6e2e2;
  }
}
.ruleForm-wrap {
  padding: 30px;

  p {
    color: #999;
    font-size: 12px;
  }
  .show-pwd {
    position: absolute;
    right: 10px;
  }
}
.set-tips {
  line-height: 36px;
}
</style>
