<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <meta name="referrer" content="no-referrer">
    <meta name="renderer"  content="webkit">
    <link rel="icon" type="image/x-icon" href="<%= BASE_URL %>favicon.ico">
    <!-- <title><%= webpackConfig.name %></title> -->
    <!-- <link rel="stylesheet" type="text/css" href="css/animate.min.css" />
    <link rel="stylesheet" type="text/css" href="css/syalert.min.css" /> -->

    
  </head>
  <body>
    
    <noscript>
      <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
   
    <!-- built files will be auto injected -->

    <!-- <script src="https://unpkg.com/axios/dist/axios.min.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/es6-promise/dist/es6-promise.auto.min.js"></script>
    <script src="axios.min.js"></script>

  <script >
       axios.post('/api/getAll')
                .then(function (res) {
                    var data=res.data;
                    // console.log(data);
                     //浏览器标题
                     for(var i=0;i<data.length;i++){
                        var item=data[i];
                        if (item.type == 1) {
                          //浏览器
                          localStorage.setItem("browseImg", item.image);
                          localStorage.setItem("browseTitle", item.title);
                          var browseImg = item.image;
                          var browseTitle = item.title;
                          // console.log(browseTitle);
                            var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
                            link.type = 'image/x-icon';
                            link.rel = ' icon';
                            link.href = browseImg;
                            document.getElementsByTagName('head')[0].appendChild(link);
                            document.title=browseTitle;
                        }else if(item.type==2){
                          //大屏
                          localStorage.setItem("screenImg", item.image);
                          localStorage.setItem("screenTitle", item.title);
                        }else if(item.type==3){
                          //主页面
                          localStorage.setItem("mainImg", item.image);
                          localStorage.setItem("mainTitle", item.title);
                        }else if (item.type == 6) {
                          //密码大屏
                          localStorage.setItem("psdScreenImg", item.image);
                          localStorage.setItem("psdScreenTitle", item.title);
                        }
                      }

                })
                .catch(function (error) {
                    console.log(error);
                });



  </script>
  </body>
</html>
