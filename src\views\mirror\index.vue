<template>
  <div>

      <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">镜像管理</h3>
          </div>
          <div class="serch-box clearfix">
            <div class="filter-container">
              <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
                <svg-icon icon-class="refresh" />
              </el-button>
              <el-button
                v-waves
                class="filter-item"
                type="primary"
                icon="el-icon-plus"
                @click="handleAddBtn()"
              >添加</el-button>
              <div class="search-container">
                <el-input
                  v-model="listQuery.title"
                  placeholder="名称/产品/镜像类型"
                  style="width: 200px;"
                  class="filter-item"
                  v-on:input="search"
                />
                <span class="el-icon-search search-btn" @click="handleSearch()"></span>
              </div>
            </div>
            <div class="page-box">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="sizes, prev,slot, next,total"
                :total="total"
              >
                <span class="pageNum">
                  {{this.listQuery.page}}
                  <i class="divider">/</i>
                  {{totalPage}}
                </span>
              </el-pagination>
            </div>
          </div>
          <div class="table-box">
             <el-table-bar>
               <el-table :data="tableData" style="width: 100%;margin-bottom:20px;">
              <el-table-column prop="mirrorName" label="名称" sortable></el-table-column>
              <el-table-column prop="path" label="镜像存储位置"></el-table-column>
              <el-table-column prop="product" label="产品" sortable></el-table-column>
              <el-table-column prop="brand" label="品牌"  sortable></el-table-column>
              <el-table-column prop="mirrorType" label="镜像类型" sortable></el-table-column>
              <el-table-column prop="mirrorFormat" label="镜像格式"></el-table-column>
              <el-table-column prop="mirrorSize" label="容量(G)" ></el-table-column>
              <el-table-column prop="mirrorPort" label="端口"></el-table-column>
              <el-table-column prop="time" label="创建时间" ></el-table-column>

              <el-table-column label="操作"  align="center">
                <template slot-scope="scope">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      <i class="el-icon-more" style="transform:rotate(90deg)"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <div @click="handleEditClick(scope.row)" class="opt">修改</div>

                      <div @click="handleDelClick(scope.row)" class="opt">删除</div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
             </el-table-bar>

          </div>
        </div>
      </div>


    <el-dialog title="修改镜像" :visible.sync="dialogEditVisible"  top="0" width="30%">
      <el-form :model="editForm" ref="editForm" :rules="accountRules">
        <el-form-item label="名称" :label-width="formLabelWidth" prop="mirrorName">
          <el-input v-model="editForm.mirrorName " autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="镜像存储位置" :label-width="formLabelWidth" prop="path">
          <el-input v-model="editForm.path" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="产品" :label-width="formLabelWidth" prop="product">
          <el-input v-model="editForm.product" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="品牌" :label-width="formLabelWidth" prop="brand">
          <el-select v-model="editForm.brand" placeholder="请选择品牌">
            <el-option label="奇安信" value="奇安信"></el-option>
            <el-option label="山石" value="山石"></el-option>
            <el-option label="闪捷" value="闪捷"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="镜像类型" :label-width="formLabelWidth" prop="mirrorType">
          <el-select v-model="editForm.mirrorType" placeholder="请选择类型">
            <el-option label="系统镜像" value="系统镜像"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="镜像格式" :label-width="formLabelWidth" prop="mirrorFormat">
          <el-input v-model="editForm.mirrorFormat" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="容量" :label-width="formLabelWidth" prop="mirrorSize">
          <el-input v-model="editForm.mirrorSize" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="端口" :label-width="formLabelWidth" prop="mirrorPort ">
          <el-input v-model="editForm.mirrorPort " autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEditVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit('editForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { fetchList, addMirror, delMirror } from "@/api/mirror.js";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      tableData: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        title: "",
      },
      currentPage: 1,
      totalPage: 2,
      downloadLoading: false,
      dialogFormVisible: false,
      formLabelWidth: "120px",
      dialogEditVisible: false,
      editForm: {
        id: "",
        mirrorName: "", //名称
        path: "", //镜像存储位置
        product: "", //产品
        brand: "", // 品牌
        mirrorType: "", ///镜像类型
        mirrorFormat: "", //镜像格式
        mirrorSize: "", //   容量
        mirrorPort: "", //   端口
      },
      accountRules: {
        mirrorName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "镜像存储位置不能为空", trigger: "blur" },
        ],
        product: [{ required: true, message: "产品不能为空", trigger: "blur" }],
        brand: [{ required: true, message: "品牌不能为空", trigger: "blur" }],
        mirrorType: [
          { required: true, message: "类型不能为空", trigger: "blur" },
        ],
        mirrorFormat: [
          { required: true, message: "格式不能为空", trigger: "blur" },
        ],
        mirrorSize: [
          { required: true, message: "容量不能为空", trigger: "blur" },
        ],
        mirrorPort: [
          { required: true, message: "端口不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = {
        keyWord: this.listQuery.title,
        limit: this.listQuery.limit,
        page: this.listQuery.page,
      };
      // console.log(data);
      //镜像列表
      fetchList(data)
        .then((res) => {
          // console.log(res);
         setTimeout(() => {
            this.listLoading.close();
          }, 200);

          this.tableData = res.obj.rows;
          this.total = res.obj.total_rows;
          this.currentPage = res.obj.page;
          if( res.obj.total_rows==0){
             this.totalPage=1;
          }else{
            this.totalPage = Math.ceil(this.total / this.listQuery.limit);

          }

        })
        .catch((error) => {
          this.listLoading.close();
        });
    },
    //input实时搜索
    search() {
      this.getData();
    },
    //关键词搜索
    handleSearch() {
      this.getData();
    },
     //刷新
    handleRefresh() {
      this.getData();
    },
    //添加按钮
    handleAddBtn() {
       this.$router.push({
        path: `/mirror/create`,
        query: {},
      });
    },

    //镜像修改按钮
    handleEditClick(row) {
      this.dialogEditVisible = true;
      // console.log(row);

      this.editForm.id = row.mirrorId;
      this.editForm.mirrorName = row.mirrorName; //名称
      this.editForm.path = row.path; //镜像存储位置
      this.editForm.product = row.product; //产品
      this.editForm.brand = row.brand; // 品牌
      this.editForm.mirrorType = row.mirrorType; ///镜像类型
      this.editForm.mirrorFormat = row.mirrorFormat; //镜像格式
      this.editForm.mirrorSize = row.mirrorSize; //   容量
      this.editForm.mirrorPort = row.mirrorPort; //   端口
    },
    //镜像修改
    handleSaveEdit(editForm) {
      this.$refs[editForm].validate((valid) => {
        if (valid) {
          // console.log(this.editForm);
          addMirror(this.editForm)
            .then((res) => {
              // console.log(res);
              if (res.ok == true) {
                this.dialogEditVisible = false;
                this.getData();
              }
            })
            .catch((error) => {
              this.dialogEditVisible = false;
              console.log(error);
            });
        } else {
          console.log("添加失败");
          return false;
        }
      });
    },
    //镜像删除
    handleDelClick(row) {
      // console.log(row);
      let id = row.mirrorId;
      this.$confirm("确认要删除吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((e) => {
          // console.log(e);
          if (e === "confirm") {
            delMirror(id)
              .then((res) => {
                // console.log(res);
                if (res.ok == true) {
                  this.getData();
                }
              })
              .catch((error) => {
                console.log(error);
              });
          }
        })
        .catch((e) => {});
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.listQuery.limit = val;
      this.getData();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.listQuery.page = val;
      this.getData();
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 204px);
}

.mainWrapper {
    height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .filter-container {

      .filter-item {
        margin-right: 20px;
      }
    }

  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>

