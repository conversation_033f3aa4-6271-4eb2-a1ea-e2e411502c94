<template>
  <div :id="id" :style="style"></div>
</template>

<script>
export default {
  name: 'PolycyclicGraph',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  
  mounted() {
    console.log(this.chartData, 'chartData');
    this.init();

    // this.$nextTick(() => {
    //   if (this.charts) {
    //     // 先销毁，释放内存
    //     this.charts.dispose();
    //   }
    // });
    setTimeout(() => {
      this.init();
      console.log(111111111111111);
    },1000)

  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    // if (this.charts) {
    //   this.charts.dispose();
    // }
  },

  methods: {
    init() {
      setTimeout(()=>{
        this.chart = this.$echarts.init(document.getElementById(this.id));
        this.setOption();
      },1000)
      
      
      window.addEventListener('resize', this.chart.resize);
    },
    setOption() {
      const that = this;

      let option = {};
      option = {

tooltip: {
  formatter: function (params) {
    // console.log(params);
    var result = '';
    result += params.marker + params.name + '使用率：' + Number(params.value).toFixed(2) + '%';
    return result;
  },
},
polar: {
  radius: ['36%', '72%'],
  center: ['50%', '50%'],
},
angleAxis: {
  max: 100,
  startAngle: 90,
  axisLine: {
    show: false,
  },
  axisTick: {
    show: false,
  },
  axisLabel: {
    show: false,
  },
  splitLine: {
    show: false,
  },
},
radiusAxis: {
  type: 'category',
  data: props.chartData.xData,
  axisLabel: {
    show: false,
  },
  axisTick: {
    show: false,
  },
  splitLine: {
    show: false,
  },
  axisLine: {
    show: false,
  },
},
series: {
  type: 'bar',
  showBackground: true, // 展示背景阴影
  color: ['#32D16D', '#FF8723', '#2251F8',],
  colorBy: 'data',
  barWidth: 10,
  barCategoryGap: "20%", // 柱形间距
  data: props.chartData.yData,
  roundCap: true, //是否在环形柱条两侧使用圆弧效果
  coordinateSystem: 'polar',
},
}

      this.chart.setOption(option, true);
    },
  },
};
</script>
