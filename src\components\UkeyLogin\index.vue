<template>
  <el-dialog
    top="0"
    :title="title"
    width="600px"
    :visible.sync="dialogUkeyVisible"
    :append-to-body="true"
    :close-on-press-escape="false"
    :before-close="cancle"
    custom-class="cerDialog-wrap"
  >
    <ul class="info-wrap">
      <!-- <i class="el-icon-info"></i> -->
      <li>1.开启用户认证登录前，请确保已经安装Ukey软件或下载，并插入Ukey。</li>
      <li>2.1个USBkey只能绑定一个用户。</li>
      <li>
        3.为用户开启证书登录并绑定证书后，该用户需要重新登录，且登录是电脑需要验证Ukey。
      </li>
    </ul>
    <el-form
      :model="formData"
      :rules="formRules"
      ref="ruleForm"
      label-width="120px"
      class="ruleForm-wrap"
    >
      <el-form-item label="证书登录" prop="state">
        <el-switch
          v-model="formData.state"
          @change="stateChang"
          active-value="1"
          inactive-value="-1"
        ></el-switch>
        <p>开启该功能并绑定证书后，需要重新登录,且登录时需验证UKey设备。</p>
      </el-form-item>
      <el-form-item label="设备列表" prop="deviceId" hidden>
        <el-input v-model="formData.deviceId"  :disabled="formData.id != '' ? true : false" ></el-input>
      </el-form-item>
       
       
       
     
      <el-form-item label="绑定证书" prop="certificate">
        <el-input v-model="formData.certificate"  :disabled="formData.id != '' ? true : false"></el-input>
        <el-button @click.prevent="binding" type="info"  :disabled="formData.id != '' ? true : false">绑定</el-button>
        <p>绑定证书前，请确保已插入UKey设备</p>
      </el-form-item>
      <el-form-item label="Ukey PIN码" prop="ukeyPin">
        <el-input
          v-model="formData.ukeyPin"
          :key="pinType"
          ref="password"
          :type="pinType"
          :disabled="formData.id != '' ? true : false"
        ></el-input>
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="pinType === 'password' ? 'eye2' : 'eye-open'" />
        </span>
        <p>若遗忘UKey PIN码，请联系UKey厂家</p>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确定</el-button>
      <el-button type="default" @click="cancle">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    title: String,
    dialogUkeyVisible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    formRules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    formData(oldVal, newVal) {
      // console.log(oldVal);
      // console.log(newVal);
      // return val;
    },
  },
  mounted() {
    console.log("ukey子组件");
    console.log(this.formData);
  },
  data() {
    return {
      pinType: "password",
    };
  },
  methods: {
    showPwd() {
      if (this.pinType === "password") {
        this.pinType = "";
      } else {
        this.pinType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    stateChang(val) {
      // console.log(val);
     
    },
    //取消
    cancle() {
      this.$emit("cancle", true);
    },
    //确认提交
    submit() {
      this.$refs.ruleForm.validate();
      this.$emit("uKeyConfirm", this.formData);
    },
    //绑定
    binding() {
      // console.log('绑定');
      if (this.formData.ukeyPin == "") {
        this.$message({
          message: "请先输入Ukey PIN码",
          type: "warning",
        });
      } else {
        this.autograph();
      }
    },
    //ukey验签
    autograph() {
      //查找设备列表
      const sele_devices = enumDevice("GM3000");
      console.log(sele_devices);
      if (sele_devices != null || sele_devices != undefined) {
        this.formData.deviceId = sele_devices[0];
        //验证密码
        const passwordTip = verifyUserPin(sele_devices[0], this.formData.ukeyPin);
        console.log(passwordTip);
        if (passwordTip == "1") {
          this.$message({
            message: "验证用户密码成功",
            type: "success",
          });
        } else {
          this.$message({
            message: passwordTip,
            type: "error",
          });
          return false;
        }
        const userList = getUserList();
        console.log(userList);
        //导出
        const exportUserCertData = exportUserCert(userList[0], 0);
        // console.log(exportUserCertData);
        //获取证书
        let cerInfo = getCertInfo(exportUserCertData);
        // console.log(cerInfo);
        cerInfo = cerInfo.replace(/\ +/g, "").replace(/[\r\n]/g, "");
        const reg = /(?<=Subject:).*?(?=Subject_CN)/g;
        let cer = cerInfo.match(reg);
        // console.log(cer);
        if (cer.length > 0) {
          this.formData.certificate = cer[0];
        }
      } else {
        this.$message({
          message: "未找到任何key,请检查Ukey设备是否插入",
          type: "warning",
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.info-wrap {
  padding: 10px 30px;
  background-color: #e6f6ff;
  position: relative;
  & > li {
    line-height: 28px;
    padding-left: 26px;
  }
  i {
    display: inline-block;

    position: absolute;
    top: 18px;
    left: 30px;
    &.el-icon-info {
      color: #0076f7;
    }
  }
}
</style>
<style lang="scss" scoped>
.cerDialog-wrap .el-dialog__body {
  padding: 0 0 30px 0;
}
.ruleForm-wrap {
  padding: 20px 40px;
  .el-input {
    width: 70%;
    margin-right: 20px;
  }
  p {
    color: #999;
    font-size: 12px;
  }
  .show-pwd {
    position: absolute;
    right: 130px;
  }
}
</style>
