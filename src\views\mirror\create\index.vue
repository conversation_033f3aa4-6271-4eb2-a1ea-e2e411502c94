<template>
  <div>

      <div class="mainWrapper">
        <div class="mainBox">
          <div class="header">
            <h3 class="title">
              <span class="el-icon-arrow-left back-icon" @click="$router.back(-1);"></span>添加镜像
            </h3>
          </div>
            <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <el-form :model="addform" ref="addform" :rules="accountRules">
                  <el-form-item label="名称" :label-width="formLabelWidth" prop="mirrorName">
                    <el-input
                      v-model="addform.mirrorName"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="镜像存储位置" :label-width="formLabelWidth" prop="path">
                    <el-input v-model="addform.path " autocomplete="off" :class="forminputWidth"></el-input>
                  </el-form-item>
                  <el-form-item label="产品" :label-width="formLabelWidth" prop="product">
                    <el-input v-model="addform.product" autocomplete="off" :class="forminputWidth"></el-input>
                  </el-form-item>
                  <el-form-item label="品牌" :label-width="formLabelWidth" prop="brand">
                    <!-- <el-input v-model="addform.brand" autocomplete="off"></el-input> -->
                    <el-select v-model="addform.brand" placeholder="请选择品牌">
                      <el-option label="奇安信" value="奇安信"></el-option>
                      <el-option label="山石" value="山石"></el-option>
                      <el-option label="闪捷" value="闪捷"></el-option>
                      <el-option label="云安宝" value="云安宝"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="镜像类型" :label-width="formLabelWidth" prop="mirrorType">
                    <!-- <el-input v-model="addform.mirrorType" autocomplete="off"></el-input> -->
                    <el-select v-model="addform.mirrorType" placeholder="请选择类型">
                      <el-option label="系统镜像" value="系统镜像"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="镜像格式" :label-width="formLabelWidth" prop="mirrorFormat">
                    <el-input
                      v-model="addform.mirrorFormat"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="容量" :label-width="formLabelWidth" prop="mirrorSize">
                    <el-input
                      v-model="addform.mirrorSize"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="端口" :label-width="formLabelWidth" prop="mirrorPort">
                    <el-input
                      v-model="addform.mirrorPort"
                      autocomplete="off"
                      :class="forminputWidth"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
            </el-scrollbar>
        </div>
      </div>

    <div class="form-foot">
      <el-button type="primary" @click="handleAddClick('addform')">确 定</el-button>
    </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { fetchList, addMirror, delMirror } from "@/api/mirror.js";
import { mapGetters } from "vuex";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: "",
      listLoading: false,
      downloadLoading: false,
      userId: "",
      tenantId: "", //账号id
      formLabelWidth: "120px",
      forminputWidth: "forminputWidth",
      addform: {
        id: "",
        mirrorName: "", //名称
        path: "", //镜像存储位置
        product: "", //产品
        brand: "", // 品牌
        mirrorType: "", ///镜像类型
        mirrorFormat: "", //镜像格式
        mirrorSize: "", //   容量
        mirrorPort: "", //   端口
      },
      emptyForm: {
        id: "",
        mirrorName: "", //名称
        path: "", //镜像存储位置
        product: "", //产品
        brand: "", // 品牌
        mirrorType: "", ///镜像类型
        mirrorFormat: "", //镜像格式
        mirrorSize: "", //   容量
        mirrorPort: "", //   端口
      },
      accountRules: {
        mirrorName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "镜像存储位置不能为空", trigger: "blur" },
        ],
        product: [{ required: true, message: "产品不能为空", trigger: "blur" }],
        brand: [{ required: true, message: "品牌不能为空", trigger: "blur" }],
        mirrorType: [
          { required: true, message: "类型不能为空", trigger: "blur" },
        ],
        mirrorFormat: [
          { required: true, message: "格式不能为空", trigger: "blur" },
        ],
        mirrorSize: [
          { required: true, message: "容量不能为空", trigger: "blur" },
        ],
        mirrorPort: [
          { required: true, message: "端口不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
    //添加镜像
    handleAddClick(addform) {
      this.$refs[addform].validate((valid) => {
        if (valid) {
          // console.log(this.addform);
          addMirror(this.addform)
            .then((res) => {
              // console.log(res);
              if (res.ok == true) {
               this.$router.push({ path: `/mirror/index` });
              }
            })
            .catch((error) => {
              console.log(error);
            });
        } else {
          console.log("添加失败");
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
   height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
    background: #F1F6FA;
  }
}
.el-select::v-deep {
  width: 300px;
}
</style>

