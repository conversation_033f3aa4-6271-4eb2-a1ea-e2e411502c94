<template>
  <div class="box">
    <div class="mainWrapper">
      <div class="wrap" :style="{
        transform: `scale(${scalseNum},${scalseNum}) translateX(-50%)`
      }">
        <top-head :title="screenTitle" :img-src="screenImg" />
        <div class="mainbox">
          <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <div class="grid-content grid-content-md2" v-if="versionSreen == 1">
                <screen-title title="安全雷达" />
                <div class="attack-box">
                  <radar id="radar" :style="{
        width: '100%',
        height: '380px',
        backgroundImage:
          'url(' +
          require('@/assets/security/radaar-bg.png') +
          ')'
      }" :chart-data="radarData" v-if="radarVisible == true"></radar>
                  <null-data v-else width="100%" height="380px" />
                </div>
              </div>
              <div class="grid-content grid-content-lg" v-if="versionSreen == 2 || versionSreen == 3">
                <screen-title title="外部威胁" />
                <div class="attack-box">
                  <img src="@/assets/security/out_alarm.png" alt />
                  <div class="attack-hd clearfix">
                    <div class="attack-title">
                      <p>今日</p>
                      <p>总攻击源数量</p>
                    </div>
                    <div class="attack-number">
                      <ul>
                        <li v-for="(num, index) in attackList" :key="index" class="number">
                          {{ num }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="attack-source">
                  <h3 class="title">攻击源 TOP5</h3>
                  <attack-source :attackSourceList="attackSourceList" :attackSourceNullList="attackSourceNullList" />
                </div>
                <div class="attack-source">
                  <h3 class="title">攻击类型 TOP5</h3>
                  <attack-type id="attackTypes_out" :style="{ width: '100%', height: '190px' }"
                    :chart-data="outThreatData" v-if="outThreatTypeVisible == true" />
                  <null-data v-else width="100%" height="190px" />
                </div>
              </div>
              <div class="grid-content" :class="versionSreen == 1 ? 'grid-content-lg2' : 'grid-content-md'
        ">
                <screen-title title="流量统计" />
                <div class="attack-box">
                  <flow-statistic id="bandWidth" width="100%" :height="versionSreen == 1 ? '480px' : '310px'"
                    v-if="totalBwShow == true" :chart-data="totalFlowData1" />
                  <null-data v-else width="100%" :height="versionSreen == 1 ? '480px' : '310px'" />
                </div>
              </div>
              <!-- <div v-else class="grid-content" :class="versionSreen == 1 ? 'grid-content-lg2' : 'grid-content-md'
        ">
                <screen-title title="流量统计" />
                <div class="attack-box">
                  <flow-statistics id="bandWidth" width="100%" :height="versionSreen == 1 ? '480px' : '310px'"
                    v-if="totalBwShow == true" :chart-data="totalFlowData" />
                  <null-data v-else width="100%" :height="versionSreen == 1 ? '480px' : '310px'" />
                </div>
              </div> -->
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <div class="grid-content grid-content-lg" style="padding: 0 20px">
                <!-- <net-middle /> -->
                <world-map id="worldMap" width="100%" height="610px" :chart-data="mapData" v-if="mapVisible == true" />
              </div>
              <div class="grid-content grid-content-md" style="padding: 0 20px">
                <screen-title title="攻击日志" />
                <attack-log :tableData="tableAttack" />
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <div class="grid-content">
                <screen-title title="内部负载" />
              </div>
              <div>
                <div class="grid-content grid-content-ms">
                  <div class="title">
                    云主机流量 TOP5
                    <div class="sort-nav">
                      <span class="upstream"><i class="dot"></i> 上行流量</span>
                      <span class="downstream"><i class="dot"></i> 下行流量</span>
                    </div>
                  </div>
                  <div class="grid-body">
                    <ul class="grid-list" v-if="userFlowVisible == true">
                      <li v-for="(item, index) in userStreamsData" :key="index">
                        <div class="rank">{{ index + 1 }}</div>
                        <div class="name">{{ item.user }}</div>
                        <div class="streams-box">
                          <span class="down" v-if="item.user != '无'" :style="{
        width:
          (Number(
            item.upStream / item.totalStream
          ).toFixed(4) *
            10000) /
          100 +
          '%'
      }"></span>
                          <span class="up" v-if="item.user != '无'" :style="{
        width:
          (Number(
            item.downStream / item.totalStream
          ).toFixed(4) *
            10000) /
          100 +
          '%'
      }"></span>
                          <span class="text" v-if="item.user != '无'">上 {{ item.upStream | diskSize }} / 下
                            {{ item.downStream | diskSize }}</span>
                        </div>
                      </li>
                    </ul>

                    <null-data v-else width="100%" height="200px" />
                  </div>
                </div>
                <div class="grid-content grid-content-ms">
                  <div class="title">应用流量 TOP5</div>
                  <div class="grid-body">
                    <ul class="grid-list" v-if="appFlowVisible == true">
                      <li v-for="(item, index) in appStreamsData" :key="index">
                        <div class="rank">{{ index + 1 }}</div>
                        <div class="name">{{ item.appName }}</div>
                        <div class="streams-box">
                          <span class="up" v-if="item.appName != '无' && index == 0" :style="{ width: '80%' }"></span>
                          <span class="up" v-if="item.appName != '无' && index == 1" :style="{ width: '70%' }"></span>
                          <span class="up" v-if="item.appName != '无' && index == 2" :style="{ width: '60%' }"></span>
                          <span class="up" v-if="item.appName != '无' && index == 3" :style="{ width: '50%' }"></span>
                          <span class="up" v-if="item.appName != '无' && index == 4" :style="{ width: '30%' }"></span>

                          <span class="text" v-if="item.appName != '无'">{{ item.totalStream | diskSize }}
                          </span>
                          <span class="text" v-else> </span>
                        </div>
                      </li>
                    </ul>
                    <null-data v-else width="100%" height="200px" />
                  </div>
                </div>
                <div v-if="versionSreen == 1 || versionSreen == 2">
                  <div class="grid-content grid-content-ms grid-content-ms2">
                    <div class="title clearfix">云主机并发连接 TOP5</div>
                    <div class="grid-body">
                      <bar-vertical v-if="userBfVisible == true" id="userBf" height="200px"
                        :chart-data="userBfChartData" />
                      <null-data v-else width="100%" height="200px" />
                    </div>
                  </div>
                  <div class="grid-content grid-content-ms grid-content-ms2">
                    <div class="title">应用并发连接 TOP5</div>
                    <div class="grid-body">
                      <bar-vertical v-if="appBfVisible == true" id="applicationBf" height="200px"
                        :chart-data="applicationChartData" />
                      <null-data v-else width="100%" height="200px" />
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div class="grid-content grid-content-ms grid-content-ms2">
                    <!-- <div class="title clearfix">接口信息统计 TOP5</div> -->
                    <div class="title">
                      接口信息统计 TOP5
                      <div class="sort-nav">
                        <span class="upstream"><i class="dot"></i> 接收速率</span>
                        <span class="downstream"><i class="dot"></i> 发送速率</span>
                      </div>
                    </div>
                    <div class="grid-body">
                    <ul class="grid-list" v-if="interfaceListShow == true">
                      <li v-for="(item, index) in interfaceList" :key="index">
                        <div class="rank">{{ index + 1 }}</div>
                        <div class="name">{{ item.name }}</div>
                        <div class="streams-box">
                          <span class="down" v-if="item.name != '无'" :style="{
        width:
          `${(Number(
            item.rxSpeed / item.rxSpeed+item.txSpeed
          ).toFixed(4) *
            10000) /
          100 +
          '%'}`
      }"></span>
                          <span class="up" v-if="item.name != '无'" :style="{
        width:
         `${
          (Number(
            item.txSpeed / item.rxSpeed+item.txSpeed
          ).toFixed(4) *
            10000) /
          100 +
          '%'
         }`
      }"></span>
                          <span class="text" v-if="item.user != '无'">接收 {{ (Number(
            item.rxSpeed / item.rxSpeed+item.txSpeed
          ).toFixed(4) *
            10000) /
          100 +
          '%' }} / 发送
                            {{(Number(
            item.txSpeed / item.rxSpeed+item.txSpeed
          ).toFixed(4) *
            10000) /
          100 +
          '%'}}</span>
                        </div>
                      </li>
                    </ul>

                    <null-data v-else width="100%" height="200px" />
                  </div>
                    <!-- <div class="grid-body">
                      <interface-statistics id="bandWidth1" width="100%" height="200px" v-if="interfaceListShow == true"
                        :chart-data="interfaceList" />
                      <null-data v-else width="100%" height="200px" />
                    </div> -->
                  </div>
                  <div class="grid-content grid-content-ms grid-content-ms2">
                    <div class="title">设备健康统计</div>
                    <div class="grid-body">
                      <equipment-statistics id="bandWidth2" width="100%" height="200px" v-if="equipmentListShow == true"
                        :chart-data="equipmentList" />
                      <null-data v-else width="100%" height="200px" />
                    </div>
                  </div>
                </div>

              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div id="prompt" ref="prompt" class>
      为获得最佳体验效果，浏览器最小窗口宽度需大于1300px
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { unitConvert } from '@/utils/calculate';
import {
  threatRanking,
  threatType,
  getDeviceList,
  getUserTotalStream,
  getAppTotalStream,
  getUserTotalSession,
  getAppTotalSession,
  getInterfaceList,
  getEquipmentList,
  getFlowList,
} from '@/api/modules/firewall.js';
import { getHostRawlog } from '@/api/modules/hostSecurity.js';
import { getWordAttack } from '@/api/world.js';
import {
  //  wafAttackip,
  // wafProtect,
  loadCpu,
  loadRam,
  getDisk,
  getNetworkCard,
  getNetWorkOut,
  getDiskReadAndOut,
  radar,
} from '@/api/security.js';
import moment from 'moment';
import { getExpireDate } from '@/api/user.js';
import json from 'body-parser/lib/types/json';
import BarVertical from '@/components/BarVertical';
import Radar from '@/components/Radar';
import ScreenTitle from '@/components/ScreenTitle';
import { diskSize } from '@/utils/calculate';
import AttackSource from '../components/AttackSource'; //攻击源
import NetMiddle from '../components/NetMiddle';
import TopHead from '../components/Head'; //
import NullData from '../components/NullData'; //无数据
import FlowStatistics from '../components/FlowStatistics'; //总流量
import FlowStatistic from '../components/FlowStatistic'; //总流量
import AttackType from '../components/AttackType'; //攻击类型
import AttackLog from '../components/AttackLog'; //攻击类型
import WorldMap from '@/components/WorldMap'; //世界地图
import InterfaceStatistics from '../components/InterfaceStatistics'; //
import EquipmentStatistics from '../components/EquipmentStatistics'; //
export default {
  name: 'Dashboard',
  components: {
    TopHead,
    BarVertical,
    Radar,
    ScreenTitle,
    AttackSource,
    NetMiddle,
    NullData,
    FlowStatistics,
    FlowStatistic,
    AttackType,
    AttackLog,
    WorldMap,
    InterfaceStatistics,
    EquipmentStatistics
  },
  data() {
    return {
      rollTime: 5,
      rollPx: 1,
      screenTitle: '', //大屏标题
      screenImg: '', //大屏图片
      scalseNum: 1,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight, // 屏幕尺寸高度
      marginTop: 0,
      timer: null,
      timerZ: null,
      num: 0,
      bodyBgImage: '',
      internalThreatTotal: 0, //内部威胁总量
      internalThreatData: [], //内部威胁数组数值
      internalThreatNames: [], //内部威胁数组数值
      internalThreatNums: [], //内部威胁数组数值
      internalattackList: [],
      outThreatTotal: 0, //外部威胁总量
      attackList: [], //外部威胁总量
      mapBox: require('@/assets/security/lbx.png'),
      defartIndex: 0,
      attackSourceList: [], //外部威胁攻击源ip列表
      outThreatTypeList: [], //外部威胁数组
      outThreatData: {
        xAxisData: [], //outThreatNames: [], //外部威胁数组数值
        yAxisData: [], // outThreatNums: [], //外部威胁数组数值
      },
      outThreatTypeVisible: false,
      tableAttack: [],
      attackSourceNullList: [],
      echartsArr: [],
      uid: [],
      uidDisk: [],
      radarData: {
        radarNames: [],
        radarNums: [],
      }, //雷达数据
      radarVisible: false,
      versionSreen: '',
      totalBwShow: false,
      appNames: [], //应用名称数组
      apptotalStreams: [], //应用流量
      userNames: [], //用户名称数组
      userUpStreams: [], //用户上行流量
      userDownStreams: [], //用户下行流量
      userTotalStreams: [], //用户总流量
      appBfNames: [], //应用并发名称数组
      apptotalSessions: [], //应用并发
      userBfNames: [], //用户并发名称数组
      userSessions: [], //用户并发
      appFlowVisible: false, //应用流量显隐
      userFlowVisible: false, //用户流量显隐
      userBfVisible: false, //用户并发显隐
      appBfVisible: false, //应用并发显隐
      userStreamsData: [], //用户流量数组
      appStreamsData: [], //应用流量数组
      userBfChartData: { //云主机并发连接 TOP5
        xAxisData: [],
        yAxisData: [],
      },
      interfaceList: [],
      interfaceListShow: false,
      equipmentList: { //设备健康统计
        device: [],
        maxDevice: [],
        minDevice: [],
        connect: [],
        maxConnect: [],
        minConnect: [],
        yAxisData: [],
      },
      equipmentListShow: false,
      applicationChartData: {
        xAxisData: [],
        yAxisData: [],
      },
      totalFlowData: {
        xAxisData: [], //totalTimes: []//总流量时间数组
        yAxisData: [], //totalBws: [] //总流量,
      },
      totalFlowData1: {
        xAxisData: [], //totalTimes: []//总流量时间数组
        yAxisData: [],
        yAxisData1: [],
        // yAxisDeviceIn: [],  //上行流量,
        // yAxisDeviceOut: [],  //下行流量,
        // yAxisMaxDeviceIn: [],  //最大上行流量,
        // yAxisMaxDeviceOut: [],  //最大下行流量,
        // yAxisMinDeviceIn: [],  //最小上行流量,
        // yAxisMinDeviceOut: [],  //最小下行流量,
      },
      mapData: {
        data: [],
      },
      mapVisible: false,
      dateType: 1,
    };
  },
  computed: {
    ...mapGetters(['name', 'userid', 'version', 'usertype', 'tenantid']),
  },
  filters: {
    numFilter(value) {
      // 截取当前数据到小数点后两位--四舍五入
      let realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
    //容量转换
    diskSize(num) {
      if (num == 0) return '0 B';
      var k = 1024; //设定基础容量大小
      var sizeStr = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  ��以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + ' ' + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },
  },
  created() {
    this.screenTitle = localStorage.getItem('screenTitle'); //大屏标题
    this.screenImg = localStorage.getItem('screenImg'); //大屏图片
    this.versionSreen = this.version;
    // this.versionSreen = 3;
    console.log(this.versionSreen, 'this.versionSreen');
  },
  methods: {
    getList() {
      let that = this;
      getWordAttack()
        .then((res) => {
          console.log('攻击数据');
          console.log(res);
          this.mapData.data = [];
          if (res.code == 1) {
            this.mapData.data = res.data;
            this.mapVisible = true;
          }
        })
        .catch((error) => {
          console.log(error);
        });
      if (this.versionSreen == 2 || this.versionSreen == 3) {
        //攻击源ip
        threatRanking()
          .then((res) => {
            console.log('攻击源');
            console.log(res);
            if (res.code == 1) {
              that.attackSourceList = res.data.result;
              //console.log(that.attackSourceList);
              that.outThreatTotal = 0;
              that.attackSourceNullList = [];
              if (that.attackSourceList == undefined) {
                //console.log(difference);
                for (let k = 1; k <= 5; k++) {
                  that.attackSourceNullList.push(k);
                  // console.log(that.attackSourceNullList);
                }
                that.attackList = [0];
              } else {
                if (that.attackSourceList.length < 5) {
                  let difference = 5 - that.attackSourceList.length;
                  //console.log(difference);
                  for (let k = 1; k <= difference; k++) {
                    that.attackSourceNullList.push(that.attackSourceList.length + k);
                  }
                  // console.log(that.attackSourceNullList);
                }
                for (var i = 0; i < that.attackSourceList.length; i++) {
                  that.outThreatTotal += that.attackSourceList[i].attackCount;
                }

                that.attackList = String(that.outThreatTotal).split('');
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
        //攻击类型

        threatType()
          .then((res) => {
            console.log('攻击类型');
            console.log(res);
            if (res.code == 1) {
              if (res.data.result.length > 0) {
                that.outThreatTypeList = res.data.result;
                // console.log(that.outThreatTypeList);
                //attackSourceNullList
                that.outThreatData.xAxisData = [];
                that.outThreatData.yAxisData = [];
                for (let i = 0; i < that.outThreatTypeList.length; i++) {
                  that.outThreatData.xAxisData.push(that.outThreatTypeList[i].name);
                  that.outThreatData.yAxisData.push(
                    that.outThreatTypeList[i].attackCount
                  );
                }
                if (that.outThreatTypeList.length < 5) {
                  let difference = 5 - that.outThreatTypeList.length;

                  for (let k = 1; k <= difference; k++) {
                    that.outThreatData.xAxisData.push('无'); //内部威胁数组数值
                    that.outThreatData.yAxisData.push(0); //内部威胁数组数值
                  }
                }
                this.outThreatTypeVisible = true;
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
      } else if (this.versionSreen == 1) {
        //安全雷达
        let radarD = {
          page: '',
          limit: '',
          userId: that.userid,
          date: '',
        };
        radar(radarD)
          .then((res) => {
            // console.log('雷达')
            // console.log(res);
            if (res.code == 1) {
              that.radarData.radarNames = [];
              that.radarData.radarNums = [];
              that.radarVisible = true;
              for (var i = 0; i < res.data.length; i++) {
                that.radarData.radarNames.push(res.data[i][1]);
                that.radarData.radarNums.push(res.data[i][2]);
              }
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }
      getDeviceList(this.dateType)
        .then((res) => {
          console.log('流量统计');
          console.log(res);
          this.totalFlowData1 = {
            xAxisData: [],
            yAxisData: [],
            yAxisData1: [],
            // yAxisDeviceIn: [],
            // yAxisDeviceOut: [],
            // yAxisMaxDeviceIn: [],
            // yAxisMaxDeviceOut: [],
            // yAxisMinDeviceIn: [],
            // yAxisMinDeviceOut: []
          };
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              this.totalBwShow = true;
              res.data.result.forEach(item => {
                console.log(item, 'item');

                this.totalFlowData1.xAxisData.push(
                  moment(item.time).format('YYYY-MM-DD HH:mm')
                ); //总流量时间数组
                this.totalFlowData1.yAxisData.push({
                  value: item.deviceIn,
                  yAxisMaxDeviceIn: item.maxDeviceIn,
                  yAxisMinDeviceIn: item.minDeviceIn,

                })
                this.totalFlowData1.yAxisData1.push({
                  value: item.deviceOut,
                  yAxisMaxDeviceOut: item.maxDeviceOut,
                  yAxisMinDeviceOut: item.minDeviceOut,

                })
                console.log(this.totalFlowData1.yAxisData, 'this.totalFlowData1.yAxis');
                console.log(this.totalFlowData1.yAxisData, 'this.totalFlowData1.yAxis1');
                // this.totalFlowData1.yAxisDeviceIn.push(item.deviceIn)
                // this.totalFlowData1.yAxisDeviceOut.push(item.deviceOut)
                // this.totalFlowData1.yAxisMaxDeviceIn.push(item.maxDeviceIn)
                // this.totalFlowData1.yAxisMaxDeviceOut.push(item.maxDeviceOut)
                // this.totalFlowData1.yAxisMinDeviceIn.push(item.minDeviceIn)
                // this.totalFlowData1.yAxisMinDeviceOut.push(item.minDeviceOut)
                // this.totalFlowData.yAxisData.push(item.totalBw); //总流量
              })
            }
          } else {
            this.totalBwShow = false;
          }
        })
        .catch((error) => {
          console.log(error);
        });
      // getFlowList(this.dateType).then(res=>{
      //   console.log(res,'getFlowList');
      //   this.totalTimes=[]
      //   this.totalBws=[]
      //   if (res.code == 1) {
      //     this.totalBwShow = true;
      //       res.data.result.forEach((item) => {

      //         if(this.versionSreen ==3){
      //           this.totalFlowData.xAxisData.push(moment(item.time).format('YYYY-MM-DD HH:mm')); //总流量时间数组
      //           console.log(this.totalTimes,'totalTimes');
      //           this.totalFlowData.yAxisData.push(item.totalBytes); //总流量

      //         }

      //         // }

      //       });
      //       that.totalFlowVisible = true;
      //       // console.log(this.totalTimes)
      //       that.$nextTick(function () {
      //         that.initTotalFlow();
      //       });
      //   }
      // }).catch(err=>{
      //   console.log(err);
      // })
      //攻击日志

      getHostRawlog()
        .then((res) => {
          console.log('攻击日志');
          console.log(res);
          if (res.code == 1) {
            clearInterval(this.timer);
            that.tableAttack = res.data.results;
            // console.log( that.tableAttack );
            // console.log('攻击日志');
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getZstack() {
      let that = this;
      //云主机用户流量
      //用户top5接口
      getUserTotalStream(this.dateType)
        .then((res) => {
          console.log('用户流量');
          console.log(res);
          this.userStreamsData = [];
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                let obj = {
                  user: item.user,
                  upStream: item.upStream,
                  downStream: item.downStream,
                  totalStream: item.totalStream,
                };
                this.userStreamsData.push(obj);
              });

              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  let obj = {
                    user: '无',
                    upStream: 0,
                    downStream: 0,
                    totalStream: 0,
                  };
                  this.userStreamsData.push(obj);
                }
              }
              console.log('数据');
              console.log(this.userStreamsData);
              this.userFlowVisible = true;
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });

      //应用流量top5

      getAppTotalStream(this.dateType)
        .then((res) => {




          console.log(res);
          // this.appNames = []; //应用名称数组
          // this.apptotalStreams = []; //应用流量
          this.appStreamsData = [];
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                // this.appNames.push(item.appName);
                // this.apptotalStreams.push(item.totalStream);
                let obj = {
                  appName: item.appName,
                  totalStream: item.appTotalStream,
                };
                this.appStreamsData.push(obj);
              });

              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  // this.appNames.push('无');
                  // this.appStreamsData.push(0);
                  let obj = {
                    appName: '无',
                    totalStream: 0,
                  };
                  this.appStreamsData.push(obj);
                }
              }
              console.log('应用流量');
              console.log(this.appStreamsData);
              this.appFlowVisible = true;
              // that.$nextTick(function () {
              //   that.initApplyFlow();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
      // 用户并发连接top5

      getUserTotalSession()
        .then((res) => {
          console.log('用户并发连接');
          console.log(res);
          this.userBfNames = [];
          this.userSessions = [];
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                if (item.user != '' && item.sessions != 0) {
                  this.userBfNames.push(item.user);
                  this.userSessions.push(item.sessions);
                } else {
                  this.userBfNames.push('无');
                  this.userSessions.push(0);
                }
              });
              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  this.userBfNames.push('无');
                  this.userSessions.push(0);
                }
              }
              this.userBfVisible = true;
              this.userBfChartData.xAxisData = this.userBfNames;
              this.userBfChartData.yAxisData = this.userSessions;
              // that.$nextTick(function () {
              //   that.initUserBf();
              // });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
      // 接口信息统计
      getInterfaceList().then(res => {
        console.log(res, '接口信息统计');

        if (res.code == 1) {
          if (res.data.result.length > 0) {
            this.interfaceListShow = true
            // let userBfNames = []
            // let userSessions = []
            // let userSessions1 = []
            res.data.result.forEach((item) => {
              console.log(item);
              this.interfaceList.push({
                name:item.name,
                rxSpeed:item.rxSpeed,
                txSpeed:item.txSpeed
              })
              // userBfNames.push(item.name);
              // if (item.rxSpeed != null) {
              //   userSessions.push(item.rxSpeed);
              // } else {
              //   userSessions.push(0);
              // }
              // if (item.txSpeed != null) {

              //   userSessions1.push(item.txSpeed);
              // } else {
              //   userSessions1.push(0);
              // }


            });

            // this.interfaceList.xAxisData1 = userSessions;
            // this.interfaceList.xAxisData2 = userSessions1;
            // this.interfaceList.yAxisData = userBfNames;
            // console.log(this.interfaceList, 'interfaceList');
            // that.$nextTick(function () {
            //   that.initUserBf();
            // });
          }
        }
      }).catch(error => {
        console.log(error, '接口信息统计');
      })
      // 设备健康统计
      getEquipmentList()
        .then(res => {

          console.log(res, '设备健康统计');
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              this.equipmentListShow = true
              this.equipmentList = { //设备健康统计
                device: [],
                maxDevice: [],
                minDevice: [],
                connect: [],
                maxConnect: [],
                minConnect: [],
                yAxisData: [],
              },
                res.data.result.forEach((item) => {
                  console.log(item);
                  this.equipmentList.yAxisData.push(moment(item.time).format('MM-DD'))
                  this.equipmentList.device.push(item.device)
                  this.equipmentList.maxDevice.push(item.maxDevice)
                  this.equipmentList.minDevice.push(item.minDevice)
                  this.equipmentList.connect.push(item.connect)
                  this.equipmentList.maxConnect.push(item.maxConnect)
                  this.equipmentList.minConnect.push(item.minConnect)


                });

              // applicationChartData
            }
          }
        }).catch(error => {
          console.log(error, '设备健康统计');
        })

      //应用并发连接top5

      getAppTotalSession()
        .then((res) => {
          console.log('应用并发');
          console.log(res);
          this.appBfNames = []; //应用名称数组
          this.apptotalSessions = []; //应用并发
          if (res.code == 1) {
            if (res.data.result.length > 0) {
              res.data.result.forEach((item) => {
                if (item.appName != '' && item.sessions != 0) {
                  this.appBfNames.push(item.appName);
                  this.apptotalSessions.push(item.sessions);
                } else {
                  this.appBfNames.push('无');
                  this.apptotalSessions.push(0);
                }
              });
              if (res.data.result.length < 5) {
                let difference = 5 - res.data.result.length;

                for (let k = 1; k <= difference; k++) {
                  this.appBfNames.push('无');
                  this.apptotalSessions.push(0);
                }
              }
              this.appBfVisible = true;
              this.applicationChartData.xAxisData = this.appBfNames;
              this.applicationChartData.yAxisData = this.apptotalSessions;
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },

    // timerList() {
    //   setInterval(this.getList, 600000);
    // },
    async logout() {
      await this.$store.dispatch('user/logout');
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
    //授权校验
    getExpire() {
      getExpireDate()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            //验证状态 1为已过期，强制退出登录，0为未过期
            if (res.data.status == 1) {
              this.$message.error('授权已过期');
              this.logout();
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    timerZstack() {
      // console.log(this.timerZ);
      if (this.timerZ != null) {
        return;
      }
      this.timerZ = setInterval(() => {
        setTimeout(this.getZstack, 0);
        setTimeout(this.getList, 0);
        setTimeout(this.getExpire, 0); //定时校验
      }, 300000);
    },

    // 添加body图片
    setBodyBackGround() {
      document.body.style.backgroundSize = '100%';
      document.body.style.backgroundImage = this.bodyBgImage;
    },
    // 清除背景图
    clearBodyBackGround() {
      document.body.style.backgroundImage = '';
    },

    resizeWin() {
      window.screenWidth = document.body.clientWidth;
      window.screenHeight = document.body.clientHeight;
      this.screenWidth = window.screenWidth;
      this.screenHeight = window.screenHeight;
      // console.log( this.screenWidth);
      //  console.log( this.screenHeight);
      let scalW = this.screenWidth / 1920;
      let scalH = this.screenHeight / 1080;
      // console.log(  this.scalseNum);
      if (this.screenWidth < 1300) {
        this.$refs.prompt.setAttribute('class', 'active');
      } else {
        this.$refs.prompt.removeAttribute('class', 'active');
      }

      if (scalW >= scalH) {
        this.scalseNum = scalH;
      } else {
        this.scalseNum = scalW;
      }
      //console.log(  this.scalseNum);
    },
  },
  mounted() {
    // 进来的时候调用添加
    this.getExpire();
    this.getList();
    this.getZstack();
    this.setBodyBackGround();
    // this.timerList();
    this.timerZstack();
    // this.huxingW();
    this.resizeWin();
    const that = this;
    window.onresize = () => {
      return (() => {
        that.echartsArr.forEach((item) => {
          item.resize();
        });
        that.resizeWin();
      })();
    };
  },
  beforeDestroy() {
    // 离开页面的时候清除
    this.clearBodyBackGround();
    clearInterval(this.timer);
    this.timer = null;
    clearInterval(this.timerZ);
    this.timerZ == null;
  },
};
</script>

<style lang="scss" scoped>
.box {
  height: 100%;
}

.mainWrapper {
  position: relative;
  flex: 1 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: linear-gradient(180deg, #030e43 0%, #00213a 100%);

  .wrap {
    flex: 1 1;
    position: relative;
    left: 50%;
    transform-origin: left top 0;
    width: 1920px;
  }

  .mainbox {
    margin: 20px 60px 0;

    .grid-content {
      // padding-bottom: 20px;
      position: relative;
      z-index: 10;
      box-sizing: border-box;

      .title {
        font-size: 16px;
        font-weight: 400;
        color: #52c4ff;
        line-height: 22px;
        margin-bottom: 15px;
      }

      .attack-box {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        margin-bottom: 20px;

        img {
          float: left;
          margin-right: 20px;
        }

        .attack-hd {
          flex: 1;
          height: 54px;
          padding: 3px 0;
          box-sizing: border-box;
          border-top: 1px solid #52c4ff;
          border-bottom: 1px solid #52c4ff;

          .attack-title {
            float: left;
            color: #52c4ff;
            font-size: 14px;

            p {
              line-height: 22px;
            }
          }

          .attack-number {
            float: right;

            .number {
              width: 40px;
              height: 46px;
              float: left;
              background: #005bd4;
              text-align: center;
              line-height: 46px;
              color: #fff;
              font-family: DINAlternate-Bold;
              font-size: 40px;
              margin-left: 5px;
              position: relative;

              &::before {
                clear: both;
                content: '';
                position: absolute;
                width: 5px;
                height: 2px;
                background: rgba(0, 42, 74, 0.5);
                left: 0;
                top: 50%;
                transform: translateY(-50%);
              }

              &::after {
                clear: both;
                content: '';
                position: absolute;
                width: 5px;
                height: 2px;
                background: rgba(0, 42, 74, 0.5);
                right: 0;
                top: 50%;
                transform: translateY(-50%);
              }
            }
          }
        }
      }

      .attack-source {
        margin-bottom: 20px;
      }

      .grid-body {
        width: 100%;

        .grid-item {
          width: 100%;
          display: flex;

          .name {
            display: inline-block;
            width: 120px;
            font-size: 14px;
            color: #e0f3ff;
            letter-spacing: 0;
            line-height: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 20px;
            white-space: nowrap;
            margin-bottom: 15px;
          }

          .line-ratio {
            flex: 1;

            //background: #f00;
            .line-container2 {
              width: 240px;
              position: relative;
              margin-bottom: 15px;
              height: 16px;
              display: inline-block;
              overflow: hidden;

              .line-body {
                width: calc(100% - 10px);
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                background: #052e4e;
                border-radius: 5px;
              }

              .line-value {
                position: absolute;
                top: 0;
                left: 0;
                height: 6px;
                transition: all 0.5s;
                border-radius: 5px;
              }
            }

            .line-container {
              width: 240px;
              border: 1px solid #1b3b5f;
              position: relative;
              margin-bottom: 15px;
              height: 16px;
              display: inline-block;
              overflow: hidden;

              .line-body {
                width: calc(100% - 10px);
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                background: rgba(0, 54, 96, 1);
              }

              .line-value {
                position: absolute;
                top: 4px;
                left: 5px;
                height: 6px;
                transition: all 0.5s;
              }

              .line-shelter {
                position: absolute;
                top: 0;
                background-color: #0d1c37;
                width: 2px;
                height: 6px;
                top: 4px;
                left: 9px;
              }
            }

            .value {
              font-weight: 700;
              font-size: 14px;
              color: #ffb412;
              letter-spacing: 0;
              text-align: right;
              line-height: 12px;
              float: right;
              position: relative;
              top: 2px;
            }
          }
        }
      }
    }

    .grid-content-md2 {
      height: 440px;
    }

    .grid-content-lg2 {
      height: 540px;
    }

    .grid-content-lg {
      height: 610px;
      //  background: #f00;
    }

    .grid-content-md {
      height: 370px;

      // background: #0f0;
    }

    .grid-content-ms {
      height: 220px;
      box-sizing: border-box;

      // background: #00f;
      .sort-nav {
        float: right;
        display: flex;
        align-content: center;

        span {
          display: inline-block;
          margin-left: 5px;
          font-size: 12px;

          // opacity: 0.6;
          &.active {
            opacity: 1;
          }

          .dot {
            display: inline-block;
            width: 10px;
            height: 10px;
          }
        }

        .upstream {
          color: #ffb412;

          .dot {
            background: #cf9915;
          }
        }

        .downstream {
          color: #52c4ff;

          .dot {
            background: #0085f1;
          }
        }

        .read {
          color: #52c4ff;

          .dot {}
        }

        .write {
          // color: #8a65d4;
          color: #0091ff;

          .dot {
            // background: #8a65d4;
            background: #0091ff;
          }
        }
      }

      .speed-item {
        display: flex;

        &:last-child {

          .name,
          .speed-container {
            margin-bottom: 0;
          }
        }

        .name {
          margin-bottom: 15px;
        }

        .line-speed {
          flex: 1;
        }

        .speed-container {
          width: 100%;
          position: relative;
          margin-bottom: 15px;
          height: 18px;
          display: block;
          overflow: hidden;
          background: #00213a;

          .read-value,
          .write-value {
            height: 18px;
            display: inline-block;
            font-size: 12px;
            color: #fff;
            position: relative;
            white-space: nowrap;
          }

          .speed-shelter {
            position: absolute;
            top: 0;
            background-color: #0d1c37;
            width: 2px;
            height: 18px;
          }

          .read-value {
            float: left;
            text-align: left;
            background: rgba(82, 196, 255, 0.5);
          }

          .write-value {
            float: right;
            text-align: right;
            background: rgba(138, 101, 212, 0.5);
          }

          .speed {
            position: absolute;
            top: 0;
            font-size: 12px;
            color: #fff;
            line-height: 18px;
          }

          .readSpeed {
            left: 3px;
          }

          .writeSpeed {
            right: 3px;
          }
        }
      }
    }

    .grid-content-ms2 {
      height: 247px;
      box-sizing: border-box;
      // background: #0ff;
    }

    .alarm-box {
      margin-top: 20px;
      padding: 0 10px;
    }
  }

  .grid-list {
    li {
      display: flex;
      align-items: center;
      color: #cbedff;
      margin-bottom: 10px;

      .rank {
        display: inline-block;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        background: #003660;
        color: #52c4ff;
      }

      .name {
        display: inline-block;
        width: 120px;
        margin: 0 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .streams-box {
        flex: 1;
        background-color: rgba(0, 66, 164, 0.2);
        height: 24px;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;

        span {
          display: inline-block;
          height: 100%;
        }

        .up {
          background-color: rgba(0, 66, 164, 0.7);
        }

        .down {
          background-color: rgba(207, 153, 21, 1);
        }

        .text {
          line-height: 24px;
          position: absolute;
          top: 50%;
          left: 20px;
          transform: translateY(-50%);
        }
      }
    }
  }
}

#radar {
  background-repeat: no-repeat;
  background-position: center;
}
</style>
