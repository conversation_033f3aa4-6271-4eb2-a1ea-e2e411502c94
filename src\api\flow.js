import request from '@/utils/request'
//流量管理---总流量
export function device(data) {
  // debugger
  return request({
    url: '/api/safe/device',
    method: 'post',
    params:data
  })
}
//流量管理---应用top10
export function appRankingTopTen(data) {
  // debugger
  return request({
    url: '/api/safe/appRankingTopTen',
    method: 'post',
    params:data
  })
}
//流量管理---用户top10
export function userRankingTopTen(data) {
  // debugger
  return request({
    url: '/api/safe/userRankingTopTen',
    method: 'post',
    params:data
  })
}
