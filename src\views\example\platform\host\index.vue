<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box">
       
        <div class="auth-item">
          <h2 class="title">云主机安全(奇安信)</h2>
          <div class="auth-item-bd">
            <div class="auth-item-block clearfix">
              <el-row>
                <span class="label">授权对象:</span>
                <el-col :xs="20" :sm="20" :md="18" :lg="18" :xl="18">
                  <div class="grid-content">试用授权</div>
                </el-col>
              </el-row>
            </div>
            <div class="auth-item-block clearfix">
              <el-row>
                <span class="label">授权类型:</span>
                <el-col :xs="20" :sm="20" :md="18" :lg="18" :xl="18">
                  <div class="grid-content">试用授权</div>
                </el-col>
              </el-row>
            </div>
            <div class="auth-item-block clearfix">
              <el-row>
                <span class="label">授权内容:</span>
                <el-col :xs="20" :sm="20" :md="18" :lg="18" :xl="18">
                  <div class="auth-grid-content">
                    <div class="content-list">
                      <h2>
                        windows_server10
                        <span>(剩余8)</span>
                      </h2>
                      <el-row>
                        <el-col  :xs="24" :sm="24"  :md="8"  :lg="8"  :xl="8"  v-for="(module,index) in windowList" :key="index" >
                          <div class="content-list-content clearfix">
                            <div class="img-box"> <img :src="module.img" alt /></div>
                           <div class="text-box">
                               <p>
                              <label>模块名称：</label>
                              <span>{{module.name}}</span>
                            </p>
                             <p>
                              <label>到期时间：</label>
                              <span>{{module.time}}</span>
                            </p>

                           </div>
                          
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                 
                 
                    <div class="content-list">
                      <h2>
                        linux_server10
                        <span>(剩余9)</span>
                      </h2>
                      <el-row>
                        <el-col  :xs="24" :sm="24"  :md="8"  :lg="8"  :xl="8"  v-for="(module,index) in windowList" :key="index" >
                          <div class="content-list-content clearfix">
                            <div class="img-box"> <img :src="module.img" alt /></div>
                           <div class="text-box">
                               <p>
                              <label>模块名称：</label>
                              <span>{{module.name}}</span>
                            </p>
                             <p>
                              <label>到期时间：</label>
                              <span>{{module.time}}</span>
                            </p>

                           </div>
                          
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                 
                 
                    <div class="content-list">
                      <h2>
                        CPU数10
                        <span>(剩余10)</span>
                      </h2>
                      <el-row>
                        <el-col  :xs="24" :sm="24"  :md="8"  :lg="8"  :xl="8"  v-for="(module,index) in windowList" :key="index" >
                          <div class="content-list-content clearfix">
                            <div class="img-box"> <img :src="module.img" alt /></div>
                           <div class="text-box">
                               <p>
                              <label>模块名称：</label>
                              <span>{{module.name}}</span>
                            </p>
                             <p>
                              <label>到期时间：</label>
                              <span>{{module.time}}</span>
                            </p>

                           </div>
                          
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                   </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
    
      windowList:[{
        img:require('@/assets/module/bingdu.png'),
        name:'防病毒',
        time:'2020-07-19'
      },
      {
        img:require('@/assets/module/well.png'),
        name:'防火墙',
        time:'2020-07-19'
      },{
        img:require('@/assets/module/well.png'),
        name:'入侵防御',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'放暴力破解',
        time:'2020-07-19'
      },
      {
        img:require('@/assets/module/well.png'),
        name:'webshell扫描',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'安全基线',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'虚拟化加固',
        time:'2020-07-19'
      },
      {
       img:require('@/assets/module/well.png'),
        name:'网卡流量统计',
        time:'2020-07-19'
      }
      ]
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
    
    }
      .auth-item-bd {
        .auth-item-block {
          margin-bottom: 20px;
          .label {
            float: left;
            margin-right: 10px;
          }
          .auth-grid-content {
            width: 100%;
            padding: 10px;
            border: 1px solid #dedede;
            border-radius: 2px;
            .content-list{
              border-bottom: 1px solid #e6e6e6;
              margin-bottom: 10px;
              h2{
                font-size: 14px;
                font-weight: normal;
                margin-bottom: 10px;
              }
              .content-list-content{
               padding-bottom: 20px;
                .img-box{
                  width:36px;
                  height: 36px;
                  line-height: 36px;
                  background: #409eff;
                  border-radius: 3px;
                  text-align: center;
                  float: left;
                  margin-right: 16px;
                  img{
                    width: 24px;
                    height: 24px;
                    vertical-align:middle;
                  }
                  
                }
                .text-box{
                  float: left;
                  p:first-child{
                    margin-bottom:5px;
                  }
                }

              }

            }
          }
        }
      }
  }
}
</style>
