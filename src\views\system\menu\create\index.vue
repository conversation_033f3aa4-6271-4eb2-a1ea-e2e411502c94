<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">
            <span class="el-icon-arrow-left back-icon" @click="$router.back(-1);"></span>添加菜单
          </h3>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box"  @click="showTree=false">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <el-form :model="form" ref="form" :rules="accountRules">
                  <el-form-item label="名称" :label-width="formLabelWidth" prop="name">
                    <el-input v-model="form.name" autocomplete="off"></el-input>
                  </el-form-item>
                  <el-form-item label="父级编号" :label-width="formLabelWidth" prop="pName">
                    <el-input v-model="form.pName" autocomplete="off"  @click.stop.native="selectTree"></el-input>
                    <div class="treedata-box" v-show="showTree">
                      <el-scrollbar wrap-class="scrollbar-wrapper">
                        <div class="treedata-content">
                          <el-input placeholder="输入关键字搜索" v-model="filterText" @click.stop.native="showTree=true"></el-input>

                          <el-tree
                            class="filter-tree"
                            :data="data"
                            :props="defaultProps"
                            default-expand-all
                            :filter-node-method="filterNode"
                            @node-click="handleNodeClick"
                            ref="tree"
                          ></el-tree>
                        </div>
                      </el-scrollbar>
                    </div>
                  </el-form-item>
                  <el-form-item label="请求地址" :label-width="formLabelWidth" prop="url">
                    <el-input v-model="form.url" autocomplete="off"></el-input>
                  </el-form-item>
                  <el-form-item label="图标" :label-width="formLabelWidth" prop="icon">
                    <el-input v-model="form.icon" autocomplete="off"></el-input>
                  </el-form-item>
                  <el-form-item label="排序" :label-width="formLabelWidth" prop="orderNum">
                    <el-input v-model="form.orderNum" autocomplete="off"></el-input>
                  </el-form-item>
                  <el-form-item label="菜单类型" :label-width="formLabelWidth" prop="radio">
                    <el-radio-group v-model="form.radio" @change="selectRadio">
                      <el-radio :label="1">平台</el-radio>
                      <el-radio :label="2">租户</el-radio>
                      <el-radio :label="3">公用</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div class="form-foot">
      <el-button type="primary" @click="handleAddClick('form')">确 定</el-button>
    </div>
  </div>
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { menuList, deleteMenuById, editorUpdateMenu } from "@/api/system.js";
import { Loading } from "element-ui";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {

      account: "",
      showTree: false,
      filterText: "",
      data: [],
      defaultProps: {
        children: "children",
        label: (data, node) => {
          //console.log(data, node);
          return data.name;
        },
      },
      listLoading: false,
      form: {
        name: "",
        pId: "",
        pName: "",
        url: "",
        icon: "",
        orderNum: "",
        radio: '',
      },
      formLabelWidth: "120px",
      accountRules: {
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        // pName: [
        //   { required: true, message: "父级编号不能为空", trigger: "change" },
        // ],
        url: [{ required: true, message: "地址不能为空", trigger: "blur" }],
        icon: [],
        orderNum: [],
        radio: [
          { required: true, message: "选择菜单类型", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getData();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    //菜单列表
    getData() {
      menuList()
        .then((res) => {
          // console.log(res.data);
          this.data = res.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    selectTree() {
      this.showTree = true;
      this.filterText='';
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      // console.log(data);
      this.form.pId = data.id;
      this.form.pName = data.name;
      // console.log(this.form.pId);
      this.showTree = false;
    },
    hideTree() {
      this.showTree = false;
    },
    selectRadio(value){
      // console.log(value);
      this.radio=value;

    },

    //菜单添加
    handleAddClick(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let addForm = {
            name: this.form.name,
            pId: this.form.pId,
            url: this.form.url,
            icon: this.form.icon,
            orderNum: this.form.orderNum,
            menuType: this.form.radio,
          };
          editorUpdateMenu(addForm)
            .then((res) => {
              // console.log(res);
              this.$message({
                message: "添加成功",
                type: "success",
              });
              this.$router.push({
                path: `/system/menu`,
                query: {},
              });
              this.quit(form);
            })
            .catch((error) => {
              console.log(error);
            });


        } else {
          this.$message.error({
            message: "添加失败",
          });
          return false;
        }
      });
    },

    // 退出清空数据
    quit(form) {
      this.dialogFormVisible = false;
      this.form.name = "";
      this.form.pid = "";
      this.form.url = "";
      this.form.icon = "";
      this.form.orderNum = "";
      this.radio = 1;
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
    background: #f1f6fa;
    .treedata-box {
      width: 300px;
      height: 200px;

      box-sizing: border-box;
      border: 1px solid #dae0e6;
      border-radius: 2px;
      position: absolute;
      top: 36px;
      left: 0px;
      z-index: 888;
      background: #fff;
      .el-scrollbar {
        height: 100%;
        .treedata-content {
          padding: 15px;
        }
        .el-input {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
</style>
