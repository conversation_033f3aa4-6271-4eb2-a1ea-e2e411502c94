<template>
  <!-- <div class="mainWrapper">
    <div class="mainBox"> -->
  <!-- <div class="header clearfix">
        <h3 class="title">主机威胁</h3>
      </div> -->
  <div>
    <div class="serch-box clearfix">
      <div class="filter-container">
        <el-button
          v-waves
          class="filter-item"
          type="primary"
          @click="hostThreatRefresh"
        >
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-input
          v-model="listQueryHostThreat.keyWord"
          placeholder="输入警报关键词查询"
          style="width: 210px"
          class="filter-item"
          v-on:input="hostThreatSearch"
        />
      </div>
      <div class="page-box">
        <el-pagination
          @size-change="handleSizeChangeHostThreat"
          @current-change="handleCurrentChangHostThreat"
          :current-page="listQueryHostThreat.currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="sizes, prev,slot, next,total"
          :total="listQueryHostThreat.total"
        >
          <span class="pageNum">
            {{ this.listQueryHostThreat.page }}
            <i class="divider">/</i>
            {{ this.listQueryHostThreat.totalPage }}
          </span>
        </el-pagination>
      </div>
    </div>
    <div class="table-box">
      <el-table-bar>
        <el-table
          :data="hostThreatData"
          style="width: 100%; margin-bottom: 20px"
          row-key="id"
        >
          <el-table-column
            prop="0"
            label="时间"
            width="210"
            :formatter="dateFormat"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="4"
            label="警报"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="2"
            label="对象"
            sortable
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="6"
            label="描述"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </el-table-bar>
    </div>
  </div>
  <!-- </div>
  </div> -->
</template>
<script>
import waves from "@/directive/waves"; // waves directive
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import { Loading } from "element-ui";
import { mapGetters } from "vuex";
import moment from "moment"; //时间格式转化
import { hostAlert } from "@/api/log.js";
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      hostThreatData: [], //云防火墙威胁日志

      listQueryHostThreat: {
        page: 1,
        limit: 10,
        date: "",
        currentPage: 1,
        total: 0,
        totalPage: 1,
        keyWord: "",
      },
    };
  },
  created() {
    this.getHostThreatData();
  },
  computed: {
    ...mapGetters(["userid", "usertype", "tenantid"]),
  },
  methods: {
    hostThreatSearch() {
      this.getHostThreatData();
    },
    dateFormat(row, column) {
      var moment = require("moment");
      var date = row[column.property];
      return moment(date).format("YYYY-MM-DD hh:mm:ss");
    },
    getHostThreatData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let hostData = {
        limit: this.listQueryHostThreat.limit,
        page: this.listQueryHostThreat.page,
        userId: this.userid,
        keyWord: this.listQueryHostThreat.keyWord,
      };
      // console.log(hostData);
      hostAlert(hostData)
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            setTimeout(() => {
              this.listLoading.close();
            }, 200);
            if (res.data != null) {
              this.hostThreatData = res.data.data;
              this.listQueryHostThreat.total = res.data.recordsTotal;
              if (res.data.recordsTotal == 0) {
                this.listQueryHostThreat.totalPage = 1;
              } else {
                this.listQueryHostThreat.totalPage = Math.ceil(
                  this.listQueryHostThreat.total /
                  this.listQueryHostThreat.limit
                );
              }
            }
          } else {
            this.listLoading.close();
          }
        })
        .catch((error) => {
          this.listLoading.close();
          console.log(error);
        });
    },
    //云防火墙威胁日志刷新
    hostThreatRefresh() {
      this.getHostThreatData();
    },
    //云防火墙---威胁日志
    handleSizeChangeHostThreat(val) {
      // console.log(`每页 ${val} 条`);
      this.listQueryHostThreat.limit = val;
      this.getHostThreatData();
    },
    handleCurrentChangHostThreat(val) {
      // console.log(`当前页: ${val}`);
      this.listQueryHostThreat.page = val;
      this.getHostThreatData();
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 230px);
}

.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.el-tabs--border-card {
  border: none;
  box-shadow: none;
}
.el-date-editor::v-deep .el-range__icon {
  position: absolute;
  right: 5px;
  top: 2px;
}
.el-date-editor::v-deep .el-input__inner {
  padding-left: 15px;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 5px;
}
.bg-warm {
  background: rgba(242, 174, 27, 1);
}
.bg-warn {
  background: rgba(236, 89, 96, 1);
}
.bg-normal {
  background: rgba(0, 94, 164, 1);
}
.bg-danger {
  background: rgba(188, 78, 115, 1);
}
.loginSelect {
  width: 120px;
  border-right: 1px solid #dae0e6;
}
.page-btn {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  border: none;
  outline: none;
  padding: 0 6px;
  background: none;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }

  i {
    width: 34px;
    height: 34px;
    background: #fff;
    border: 1px solid #d7dce2;
    border-radius: 2px;
    padding: 8px;
    font-size: 14px;
    color: #005ea4;
  }
}
</style>
