<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>
import { getAllTheme } from "@/api/theme.js";
export default {
  name: "App",
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      isRouterAlive: true,
    };
  },
  mounted() {
    // this.getData();
    //关闭浏览器tab页清除token
    // window.addEventListener("beforeunload", () => {
    //  this.$store.dispatch('user/resetToken');
    // });
  },
  methods: {
    //通过声明reload方法，控制router-view的显示或隐藏，从而控制页面的再次加载，
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    
  },

  /**
   * 当页面刷新时vuex存储的数据会消失，因此一般使用localStorage处理
   */
  // created() {
  //     //在页面加载时读取localStorage里的状态信息
  //   localStorage.getItem("state") && this.$store.replaceState(Object.assign(this.$store.state,JSON.parse(localStorage.getItem("state"))));

  //   //在页面刷新时将vuex里的信息保存到localStorage里
  //   window.addEventListener("beforeunload",()=>{
  //       localStorage.setItem("state",JSON.stringify(this.$store.state))
  //   })
  // }
};
window.onload = function () {
  getRem(750, 100);
};
window.onresize = function () {
  getRem(750, 100);
};
function getRem(pwidth, prem) {
  var html = document.getElementsByTagName("html")[0];
  var oWidth = document.body.clientWidth || document.documentElement.clientWidth;
  html.style.fontSize = (oWidth / pwidth) * prem + "px";
}
</script>
