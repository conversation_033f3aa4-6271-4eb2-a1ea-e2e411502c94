<template>
  <el-dialog
    width="460px"
    :visible.sync="visible"
    title="阈值修改"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="120px"
      @keyup.enter.native="dataFormSubmitHandle()"
    >
      <el-form-item label="名称" prop="NAME">
        <el-input v-model="dataForm.NAME" disabled />
      </el-form-item>
      <el-form-item label="阈值条件">
        <el-input value="大于等于" disabled />
      </el-form-item>
      <el-form-item label="警告阈值" prop="waring_threshold">
        <el-input v-model="dataForm.waring_threshold" />
      </el-form-item>
      <el-form-item label="严重阈值" prop="severe_threshold">
        <el-input v-model="dataForm.severe_threshold" />
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()"
        >确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce';
import { updateThreshold } from '@/api/modules/monitor';
var validNumberPass1 = (rule, value, callback) => {
  //包含小数的数字
  let reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g;
  if (value === '') {
    callback(new Error('告警阈值不能为空'));
  } else if (!reg.test(value)) {
    callback(new Error('请输入数字'));
  } else {
    callback();
  }
};

export default {
  data() {
    return {
      visible: false,
      dataForm: {},
    };
  },
  computed: {
    dataRule() {
      return {
        waring_threshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
        severe_threshold: [
          { required: true, validator: validNumberPass1, trigger: 'blur' },
        ],
      };
    },
  },
  created() { },
  mounted() { },
  methods: {
    closeDialog() {
      this.$emit('dialogClose');
    },
    init() {
      this.visible = true;
      // this.$refs['dataForm'].resetFields();
      this.$nextTick(() => {
        Promise.all([]).then(() => {
          // console.log(this.dataForm);
        });
      });
    },

    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false;
          }
          let data = {
            warningName: this.dataForm.warning_name,
            waringThreshold: Number(this.dataForm.waring_threshold),
            severeThreshold: Number(this.dataForm.severe_threshold),
          };
          console.log(data);

          updateThreshold(data)
            .then((res) => {
              console.log(res);
              if (res.code !== 1) {
                return this.$message.error(res.msg);
              }
              this.$message({
                message: '修改成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false;
                  this.$emit('refreshDataList');
                },
              });
            })
            .catch(() => { });
        });
      },
      1000,
      { leading: true, trailing: false }
    ),
  },
};
</script>
