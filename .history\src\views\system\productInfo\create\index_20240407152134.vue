<template>
  <div>
    <div class="mainWrapper">
      <div class="mainBox">
        <div class="header">
          <h3 class="title">
            <span class="el-icon-arrow-left back-icon" @click="$router.back(-1)"></span
            >添加产品
          </h3>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <div class="form-box" @click="showTree = false">
            <div class="form-box-hd clearfix">
              <div class="form-box-left">
                <h3 class="text">基本信息</h3>
              </div>
              <div class="form-box-right">
                <el-form :model="dataForm" ref="dataForm" :rules="accountRules">
                  <el-form-item label="产品" :label-width="formLabelWidth" prop="key">
                    <el-select v-model="dataForm.key" placeholder="请选择">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.key"
                        :label="item.productName"
                        :value="item.key"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                <el-form-item label="品牌" :label-width="formLabelWidth" prop="brand">
                    <el-input v-model="dataForm.brand" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="图片" :label-width="formLabelWidth" prop="brand">
                    <el-upload class="avatar-uploader" action="/api/uploadFile/upload" :show-file-list="false"
                        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                        <img v-if="dataForm.url" :src="dataForm.url" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <span style="color: #f00">上传图片大小不能超过 2MB!</span>

                    <span style="color: #f00">(图片只能上传jpg/png格式)</span>
                </el-form-item>

                <el-form-item label="状态" :label-width="formLabelWidth" prop="status">
                    <el-switch v-model="value"  active-color="#13ce66" @change="switchChange">
                    </el-switch>
                </el-form-item>

                <el-form-item label="介绍" :label-width="formLabelWidth" prop="des">
                    <el-input type="textarea" v-model="dataForm.des" autocomplete="off"></el-input>
                </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div class="form-foot">
      <el-button type="primary" @click="handleAddClick('dataForm')">确 定</el-button>
    </div>
  </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { modifyProductInfo, getProduct,fileDown } from '@/api/system.js';
import { Loading } from 'element-ui';
import { mapGetters } from 'vuex';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      account: '',
      filterText: '',
      dataForm: {
        url: '',
                productId: '',
                userId: '',
                key: '',
                brand: '',
                productName: '',
                des: '',
                status: 0,
      },
      value:'',
      formLabelWidth: '120px',
      productOptions: [],
      accountRules: {
        userName: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
        url: [{ required: true, message: '请求地址不能为空', trigger: 'blur' }],
        key: [{ required: true, message: 'key不能为空', trigger: 'blur' }],
        brand: [{ required: true, message: '品牌不能为空', trigger: 'blur' }],
        productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
        des: [{ required: true, message: '介绍不能为空', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.product();
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'tenantid']),
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    product() {
      getProduct()
        .then((res) => {
          // console.log(res);
          this.productOptions = res.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    switchChange(val){
            console.log(val);
            if(val){
                this.dataForm.status = 1
            }else{
                this.dataForm.status = 1
            }
        },
    handleAvatarSuccess(res, file) {
            this.dataForm.url = URL.createObjectURL(file.raw);
            let ishttps = 'https:' == document.location.protocol ? true : false;
            let reg = new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
            let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
            //  console.log(res);
            if (res.code == 1) {
                // if(ishttps){
                //   let ip=res.data.fileUrl.match(reg)[0]+'/api';
                //   this.form.imageUrl = res.data.fileUrl.replace(spat,ip) 
                // }else{
                //     this.form.imageUrl = res.data.fileUrl;
                // }
                fileDown({path: res.data.filePath})
                .then(res=>{
                  console.log(res,'res')
                  this.dataForm.url = res.data.filePath;
                })
                // this.dataForm.url = res.data.filePath;
                console.log("图片上传测试地址：" + this.dataForm.url);
                this.$message.success("图片上传成功");
            } else {
                this.$message.error("图片上传失败");
            }
        },
        beforeAvatarUpload(file) {
            console.log(file.type, 'file.type');
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;
            const isIcon = file.type === "image/x-icon";
            if (!isLt2M) {
                this.$message.error("上传图片大小不能超过 2MB!");
            }

            if (!isJPG) {
                this.$message.error("上传图片只能上传jpg/png格式!");
            }
            return isJPG && isLt2M;
        },
    //产品添加
    handleAddClick(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          for (let i = 0; i < this.productOptions.length; i++) {
            if (this.dataForm.key == this.productOptions[i].key) {
              this.dataForm.productId = this.productOptions[i].productId;
              this.dataForm.productName = this.productOptions[i].productName;
              this.dataForm.brand = this.productOptions[i].brand;
            }
          }
          this.dataForm.userId = this.userid
          // let addAccountArry = {
          //   url: this.dataForm.url,
          //   userName: this.dataForm.userName,
          //   passWord: this.dataForm.password,
          //   id: '',
          //   productId: this.dataForm.productId,
          //   userId: this.userid,
          //   key: this.dataForm.key,
          //   brand: this.dataForm.brand,
          //   productName: this.dataForm.productName,
          //   des: this.dataForm.des,
          //   expiredDate: this.dataForm.expiredDate,
          // };
          // console.log(addAccountArry);

          modifyProductInfo(this.dataForm)
            .then((res) => {
              // console.log(res);
              if (res.code == 1) {
                this.$message({
                  message: '添加成功',
                  type: 'success',
                });
                this.$router.push({
                  path: `/system/productInfo`,
                  query: {},
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                });
              }
            })
            .catch((error) => {
              console.log(error);
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 214px);
}
.mainWrapper {
  .mainBox {
    background: #f1f6fa;
    .treedata-box {
      width: 300px;
      height: 200px;

      box-sizing: border-box;
      border: 1px solid #dae0e6;
      border-radius: 2px;
      position: absolute;
      top: 36px;
      left: 0px;
      z-index: 888;
      background: #fff;
      .el-scrollbar {
        height: 100%;
        .treedata-content {
          padding: 15px;
        }
        .el-input {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.el-textarea::v-deep textarea {
  width: 320px !important;
  height: 120px !important;
}
</style>
