import request from '@/utils/request'
//登录日志接口
export function loginLog(data) {
  return request({
    url: '/api/log/systemLoginLog',
    method: 'post',
    params:data
  })
}
//操作日志接口
export function oprateLog(data) {
    return request({
      url: '/api/log/systemOperationLog',
      method: 'post',
      params:data
    })
  }
//菜单管理列表
export function menuList() {
  return request({
    url: '/api/menu/listTree',
    method: 'post',

  })
}
//菜单删除
export function deleteMenuById(id) {
  return request({
    url: '/api/menu/deleteMenuById',
    method: 'post',
    params:{id:id}
  })
}
// 菜单修改或新增
export function editorUpdateMenu(data) {
  return request({
    url: '/api/menu/editorUpdateMenu',
    method: 'post',
    params:data
  })
}

//角色管理
export function roleList(data) {
  // debugger
  return request({
    url: '/api/role/list',
    method: 'post',
    params:data

  })
}
//角色添加
export function roleAdd(data) {
  // debugger
  return request({
    url: '/api/role/addRoleInfo',
    method: 'post',
    params:data

  })
}
//角色编辑
export function roleEdit(data) {
  // debugger
  return request({
    url: '/api/role/editRoleByRoleId',
    method: 'post',
    params:data

  })
}
//角色删除
export function roleDel(roleId) {
  // debugger
  return request({
    url: '/api/role/deleteRoleById',
    method: 'post',
    params:{roleId:roleId}

  })
}
//角色授权
export function roleAuth(data) {
  // debugger
  return request({
    url: '/api/role/addMenuByRole',
    method: 'post',
    params:data

  })
}
//产品管理列表
export function productList(data) {
  // debugger
  return request({
    url: '/api/product/getList',
    method: 'post',
    params:data

  })
}
//产品管理修改-添加
export function modifyProduct(data) {
  // debugger
  return request({
    url: '/api/product/modifyProduct',
    method: 'post',
    params:data

  })
}
//产品管理删除
export function deleteProduct(data) {
  // debugger
  return request({
    url: '/api/product/deleteProductById',
    method: 'post',
    params:data

  })
}
//产品管理-选择产品
export function getProduct(data) {
  // debugger
  return request({
    url: '/api/product/getProductList',
    method: 'post',
    params:data

  })
}

//产品管理列表
export function producIInfotList(data) {
  // debugger
  return request({
    url: '/api/productInfo/list',
    method: 'get',
    params:data

  })
}
//产品管理修改-添加
export function modifyProductInfo(data) {
  // debugger
  return request({
    url: '/api/productInfo/modifyProduct',
    method: 'get',
    params:data

  })
}
//产品管理删除
export function deleteProductInfo(data) {
  // debugger
  return request({
    url: '/api/productInfo/deleteProducts',
    method: 'get',
    params:data

  })
}

//第一次进入默认添加的产品
// export function newDefaultP(data) {
//   // debugger
//   return request({
//     url: '/api/product/newDefaultProduct',
//     method: 'post',
//     params:data

//   })
// }
//判断用户是否是第一次登录接口
// export function isFirstLogin(data) {
//   return request({
//     url: '/api/getIsFirstLogin',
//     method: 'post',

//   })
// }

//使用许可
export function getLicense(data) {
  // debugger
  return request({
    url: '/api/license/getSnInfo',
    method: 'post',
    params:data

  })
}
//用户权限菜单
export function roleMenuList(data) {
  // debugger
  return request({
    url: '/api/role/roleMenuList',
    method: 'post',
    params:data
  })
}


