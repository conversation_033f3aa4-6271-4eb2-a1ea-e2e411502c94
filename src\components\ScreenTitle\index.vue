<template>
  <div class="all-title">
    <span class="name">{{ title }}</span>
    <div class="icon">
      <i></i>
      <i></i>
      <i></i>
    </div>
    <i class="line"></i>
  </div>
</template>

<script>
export default {
  name: 'ScreenTitle',
  props: {
    title: {
      type: String,
      default: ''
    },



  },
}
</script>

<style lang="scss" scoped>
.all-title {
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: left;
  margin-bottom: 20px;
  .name {
    font-family: PingFangSC-Semibold;
    font-weight: bold;
    font-size: 24px;
    color: #0091ff;
    letter-spacing: 0;
    text-shadow: 0 0 6px rgba(0, 145, 255, 0.3);
  }
  .icon {
    margin-left: 8px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    i {
      display: inline-block;
      margin: 0 3px;
      width: 8px;
      height: 12px;
      background-color: #0091ff;
      &:first-child {
        animation: icon_animation 1.2s linear 0s infinite;
      }
      &:nth-child(2) {
        animation: icon_animation 1.2s linear 0.4s infinite;
      }
      &:last-child {
        margin: 0 0 0 3px;
        background-color: #52c4ff;
      }
      &:nth-child(3) {
        animation: icon_animation 1.2s linear 0.8s infinite;
      }
    }
  }
  .line {
    height: 2px;
    flex-grow: 1;
    background: linear-gradient(90deg, #52c4ff 0, #0091ff 30%);
  }
}
</style>