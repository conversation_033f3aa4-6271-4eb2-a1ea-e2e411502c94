<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
      <div class="mainBox">
          字典管理
      
      </div>
    </div>
  </el-scrollbar>
</template>
<script>

export default {
  
  data() {
    return {
     
    };
  },
  created() {},
  methods: {
    
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  padding: 10px;
  .mainBox {
    padding: 20px 10px;
    background-color: #fff;
    border-radius: 3px;
    
  }
}
</style>

