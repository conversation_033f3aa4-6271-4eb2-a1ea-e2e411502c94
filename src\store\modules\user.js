import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import { Message } from 'element-ui';
import Vue from 'vue'
import qs from 'qs'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',//登录名
    roles: [],//菜单
    avatar: '',
    userid:'',//用户id
    menus:[],//菜单
    userType:'',//用户类型
    tenantId:'',//账号id
    version:'',//大屏版本号,
    openMenus:[],//打开一级菜单

  }
}

const state =getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    console.log(state)
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
   SET_ROLES: (state, roles) => {
      state.roles = roles
    },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_USERID: (state, userid) => {
    localStorage.setItem("userid",userid);
    state.userid = userid
  },
  SET_MENUS: (state,menus) => {
    state.menus = menus
  },
  SET_USERTYPE: (state,userType) => {
    localStorage.setItem("userType",userType);
    state.userType = userType
  },
  SET_TENANTID: (state,tenantId) => {
    localStorage.setItem("tenantId", tenantId);
    state.tenantId = tenantId
  },
  SET_VERSION: (state,version) => {
    state.version = version
  },
  SET_OPENMENUS: (state,openMenus) =>{
    state.openMenus=openMenus;
  }

}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password,pucSignature,key } = userInfo
    let data={
      account: username,
       passWord: password,
       pucSignature:pucSignature,
       key:key
    };
    console.log(data);
    return new Promise((resolve, reject) => {
 
      login(data).then(response => {
        const {obj}= response
      //  console.log(response);
      //  debugger
      //  commit('SET_USERID', obj.userId)
      //  commit('SET_USERTYPE', obj.userType)
      //  commit('SET_TENANTID', obj.tenantId)

      if(response.ok==true){
        commit('SET_TOKEN',obj.token)
        setToken(obj.token)
        resolve(response)
      }else{
        // console.log(response.errorMsg);
        Vue.prototype.$message.error(response.errorMsg);
        reject()
      }


      }).catch(error => {
        reject(error)
     // debugger
      })
    })
  },
  setToken({commit},token){
    // console.log(token);
    commit('SET_TOKEN',token)
    setToken(token)
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
    //   debugger
    //  console.log(state.token);
      getInfo(state.token).then(response => {
        const data = response.obj
        if (!data) {
          reject('验证失败，请重新登录。')
        }

        const { loginName,avatar,roles ,userId} = data
        // console.log(data.menuList);
        if (data.menuList && data.menuList.length > 0) { // 验证返回的menus是否是一个非空数组
          commit('SET_MENUS',data.menuList);
          let menuArr=[];
          data.menuList.forEach(menu => {
            menuArr.push(menu.url);
          })
          commit('SET_OPENMENUS',menuArr);
         }
        //  console.log(data)

        commit('SET_VERSION',data.version)
        commit('SET_NAME',data.loginName)
        commit('SET_AVATAR',data.avatar)
        commit('SET_USERID', data.userId)
        commit('SET_USERTYPE',data.userType)
        commit('SET_TENANTID',data.tenantId)
       // commit('SET_ROLES', data.roles)

        resolve(response)
      }).catch(error => {
        reject(error)
        // debugger
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // debugger
      // console.log(state.name)
      logout(state.name).then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        localStorage.clear()
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        reject(error)
      })

    })
   
  },
  // 前端 登出
  FedLogOut ({ commit }) {
    return new Promise(resolve => {
      // commit('SET_TOKEN', '')
      // commit('SET_ROLES', [])
      // commit('SET_NAME','')
      // commit('SET_AVATAR','')
      // commit('SET_USERID', '')
      // commit('SET_USERTYPE','')
      // commit('SET_TENANTID','')
      removeToken()
      commit('RESET_STATE')
      localStorage.clear()
      resolve()
    })
  },


  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

