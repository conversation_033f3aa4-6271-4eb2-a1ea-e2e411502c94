<template>
    <div>
        <div class="mainWrapper">
            <div class="mainBox">
                <div class="header">
                    <h3 class="title">产品管理</h3>
                </div>
                <div class="serch-box clearfix">
                    <div class="filter-container">
                        <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
                            <svg-icon icon-class="refresh" />
                        </el-button>
                        <el-button v-waves class="filter-item" type="primary" icon="el-icon-plus"
                            @click="handleAddBtn()">添加</el-button>
                        <div class="search-container">
                            <el-input v-model="listQuery.title" placeholder="产品名称" style="width: 200px"
                                class="filter-item" v-on:input="search" />
                            <span class="el-icon-search search-btn" @click="handleSearch()"></span>
                        </div>
                    </div>
                    <div class="page-box">
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="10"
                            layout="sizes, prev,slot, next,total" :total="total">
                            <span class="pageNum">
                                {{ this.listQuery.page }}
                                <i class="divider">/</i>
                                {{ totalPage }}
                            </span>
                        </el-pagination>
                    </div>
                </div>
                <div class="table-box">
                    <el-table-bar>
                        <el-table :data="tableData" style="width: 100%; margin-bottom: 20px">
                            <el-table-column prop="productName" label="产品名称" sortable
                                show-overflow-tooltip></el-table-column>
                            <el-table-column prop="brand" label="所属品牌" sortable show-overflow-tooltip></el-table-column>
                            <el-table-column prop="des" label="介绍" sortable show-overflow-tooltip></el-table-column>
                            <el-table-column prop="url" label="请求地址" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="status" label="状态" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div v-if="scope.row.status == 1">启用</div>
                                    <div v-else> 禁用</div>
                                </template>

                            </el-table-column>
                            <el-table-column prop="version" label="版本号" show-overflow-tooltip></el-table-column>

                            <el-table-column label="操作" align="center" width="80">
                                <template slot-scope="scope">
                                    <el-dropdown>
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-more"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <div @click="handleUpdate(scope.row)" class="opt">
                                                修改
                                            </div>
                                            <div @click="handleDel(scope.row)" class="opt">删除</div>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-table-bar>
                </div>
            </div>
        </div>

        <el-dialog top="0" title="修改产品" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
            <el-form :model="dataForm" ref="dataForm" :rules="accountRules">
                <el-form-item label="key" :label-width="formLabelWidth" prop="key">
                    <el-input v-model="dataForm.key" autocomplete="off" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="品牌" :label-width="formLabelWidth" prop="brand">
                    <el-input v-model="dataForm.brand" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="产品名称" :label-width="formLabelWidth" prop="productName">
                    <el-input v-model="dataForm.productName" autocomplete="off"></el-input>
                </el-form-item>

                <el-form-item label="图片" :label-width="formLabelWidth" prop="brand">
                    <el-upload class="avatar-uploader" action="/api/uploadFile/upload" :show-file-list="false"
                        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                        <img v-if="dataForm.url" :src="dataForm.url" class="avatar" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <span style="color: #f00">上传图片大小不能超过 2MB!</span>

                    <span style="color: #f00">(图片只能上传jpg/png格式)</span>
                </el-form-item>

                <el-form-item label="状态" :label-width="formLabelWidth" prop="status">
                    <el-switch v-model="dataForm.status"  active-color="#13ce66"
     >
                    </el-switch>
                </el-form-item>

                <el-form-item label="介绍" :label-width="formLabelWidth" prop="des">
                    <el-input type="textarea" v-model="dataForm.des" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSaveClick('dataForm')" v-dbClick>确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import { producIInfotList, modifyProduct, isFirstLogin, deleteProduct } from '@/api/system.js';
import { mapGetters } from 'vuex';
import { Loading } from 'element-ui';
export default {
    components: { Pagination },
    directives: { waves },
    data() {
        return {
            account: '',
            tableData: [],
            total: 0,
            listLoading: false,
            listQuery: {
                page: 1,
                limit: 10,
                title: '',
            },
            currentPage: 1,
            totalPage: 2,
            downloadLoading: false,
            dialogFormVisible: false,
            dialogResetVisible: false,
            dataForm: {
                url: '',
                productId: '',
                userId: '',
                key: '',
                brand: '',
                productName: '',
                des: '',
                status: '',
            },
            formLabelWidth: '120px',
            accountRules: {
                userName: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],

                password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
                url: [{ required: true, message: '请求地址不能为空', trigger: 'blur' }],
                key: [{ required: true, message: 'key不能为空', trigger: 'blur' }],
                brand: [{ required: true, message: '品牌不能为空', trigger: 'blur' }],
                productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
                des: [{ required: true, message: '介绍不能为空', trigger: 'blur' }],
            },
        };
    },
    created() {
        this.getData();
    },
    activated() { },
    computed: {
        ...mapGetters(['userid', 'usertype', 'tenantid']),
    },
    methods: {
        getData() {
            this.listLoading = Loading.service({
                lock: true,
                text: '加载中……',
                background: 'rgba(0, 0, 0, 0.7)',
            });

            let data = {
                keyWord: this.listQuery.title,

                limit: this.listQuery.limit,
                page: this.listQuery.page,
            };
            // console.log(this.userid);
            //账号管理列表
            producIInfotList(data)
                .then((res) => {
                    console.log(res);
                    setTimeout(() => {
                        this.listLoading.close();
                    }, 200);
                    this.tableData = res.obj;
                    this.total = res.obj.length;
                    if (res.obj.length == 0) {
                        this.totalPage = 1;
                    } else {
                        this.totalPage = Math.ceil(this.total / this.listQuery.limit);
                    }
                })
                .catch((error) => {
                    this.listLoading.close();
                });
        },
        handleAvatarSuccess(res, file) {
            this.dataForm.url = URL.createObjectURL(file.raw);
            let ishttps = 'https:' == document.location.protocol ? true : false;
            let reg = new RegExp(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
            let spat = /((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(:\d{0,5})?/g;
            //  console.log(res);
            if (res.code == 1) {
                // if(ishttps){
                //   let ip=res.data.fileUrl.match(reg)[0]+'/api';
                //   this.form.imageUrl = res.data.fileUrl.replace(spat,ip) 
                // }else{
                //     this.form.imageUrl = res.data.fileUrl;
                // }
                this.dataForm.url = res.data.fileUrl;
                console.log("图片上传测试地址：" + this.dataForm.url);
                this.$message.success("图片上传成功");
            } else {
                this.$message.error("图片上传失败");
            }
        },
        beforeAvatarUpload(file) {
            console.log(file.type, 'file.type');
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;
            const isIcon = file.type === "image/x-icon";
            if (!isLt2M) {
                this.$message.error("上传图片大小不能超过 2MB!");
            }

            if (!isJPG) {
                this.$message.error("上传图片只能上传jpg/png格式!");
            }
            return isJPG && isLt2M;
        },
        //刷新
        handleRefresh() {
            this.getData();
        },
        //input实时搜索
        search() {
            this.getData();
        },
        //关键词搜索
        handleSearch() {
            this.getData();
        },

        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.listQuery.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.listQuery.page = val;
            this.getData();
        },
        //删除
        handleDel(row) {
            //  console.log(row);
            let data = {
                id: row.id,
                userId: this.userid,
            };
            this.$confirm('确认要删除吗？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then((e) => {
                    // console.log(e);
                    if (e === 'confirm') {
                        deleteProduct(data)
                            .then((res) => {
                                // console.log(res);
                                if (res.code == 1) {
                                    this.getData();
                                    this.$message({
                                        message: '刪除成功',
                                        type: 'success',
                                    });
                                }
                            })
                            .catch((error) => {
                                this.$message.error({
                                    message: '删除失败',
                                });
                            });
                    }
                })
                .catch((e) => { });
        },

        //账号修改
        handleUpdate(row) {
            console.log(row, 'row');
            this.dialogFormVisible = true;
            this.dataForm.productId  = row.productId ; //接收参数
            this.dataForm.url = row.url;
            this.dataForm.userId = this.userid;
            this.dataForm.key = row.key;
            this.dataForm.brand = row.brand;
            this.dataForm.productName = row.productName;
            this.dataForm.des = row.des;
        },
        //确定修改
        handleSaveClick(dataForm) {
            this.$refs[dataForm].validate((valid) => {
                if (valid) {
                    let addAccountArry = {
                        url: this.dataForm.url,
                        userName: this.dataForm.userName,
                        passWord: this.dataForm.password,
                        id: this.dataForm.id,
                        productId: this.dataForm.productId,
                        userId: this.dataForm.userId,
                        key: this.dataForm.key,
                        brand: this.dataForm.brand,
                        productName: this.dataForm.productName,
                        des: this.dataForm.des,
                        expiredDate: this.dataForm.expiredDate,
                    };
                    // console.log(addAccountArry);

                    modifyProduct(addAccountArry)
                        .then((res) => {
                            console.log(res);
                            if (res.code !== 1) {
                                return this.$message.error(res.msg);
                            }

                            this.$message({
                                message: '修改成功',
                                type: 'success',
                                onClose: () => { },
                            });
                            this.dialogFormVisible = false;
                            this.getData();
                        })
                        .catch((error) => {
                            console.log(error);
                            this.dialogFormVisible = false;
                        });
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        //添加产品
        handleAddBtn() {
            this.$router.push({
                path: `/system/product/create`,
                query: {},
            });
        },
    },
};
</script>
<style>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
}

.avatar {
    width: 100px;
    height: 100px;
    display: block;
}
</style>
<style lang="scss" scoped>
.elTableBar {
    height: calc(100vh - 204px);
}

.mainWrapper {
    height: calc(100vh - 48px);
    background: #fff;

    .mainBox {
        .filter-container {
            .filter-item {
                margin-right: 20px;
            }
        }

        .opt {
            display: inline-block;
            padding: 0 5px;
            color: #409efd;
            cursor: pointer;
        }

        .line {
            color: #409efd;
        }
    }
}

.role-permission-box {
    background: rgba(245, 248, 251, 1);
    border: 1px dashed #d8dadb;
    padding: 16px 20px;
    margin: 0 30px;

    .title {
        font-size: 14px;
        font-weight: normal;
        margin-bottom: 10px;
    }

    .role-name {
        font-weight: normal;
        font-size: 14px;
        color: #005ea4;
        padding-left: 5px;
        border-left: 2px solid #005ea4;
        margin-bottom: 10px;
    }

    .role-item {
        float: left;
        margin-bottom: 10px;
        margin-right: 16px;

        .role-checkbox {
            margin-right: 5px;
        }
    }
}

.el-select::v-deep {
    width: 100%;
}

.el-form-item__error::v-deep {
    top: 0 !important;
    left: 460px !important;
}

.el-textarea::v-deep textarea {
    height: 120px !important;
}
</style>
