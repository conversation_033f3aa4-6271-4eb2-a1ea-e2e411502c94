<template>
  <el-table
    :data="tableData"
    style="width: 100%"
    tooltip-effect="light"
    ref="rw_table"
    @mouseenter.native="mouseEnter"
    @mouseleave.native="mouseLeave"
    :row-class-name="tableRowClassName"
  >
    <el-table-column label="严重性" width="80">
      <template slot-scope="scope">
        <div>
          <span
            class="bg-level"
            :class="scope.row.severity == '严重' ? 'bg-warn' : 'bg-normal'"
            >{{ scope.row.severity }}</span
          >
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="vmid"
      label="	虚拟机/终端"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="time"
      label="攻击时间"
      show-overflow-tooltip
    ></el-table-column>

    <el-table-column
      prop="srcaddr"
      label="源区域"
      width="130"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="dstaddr"
      label="目的区域"
      width="120"
      show-overflow-tooltip
    ></el-table-column>

    <el-table-column prop="attacktype" label="攻击类型" show-overflow-tooltip>
    </el-table-column>
    <div slot="empty" style="line-height: normal">
      <p>
        <img src="@/assets/security/noData.png" alt />
      </p>

      <span style="display: inline-block; color: #52c4ff; margin-top: 5px"
        >暂无数据</span
      >
    </div>
  </el-table>
</template>

<script>
let rolltimer = ''; // 自动滚动的定时任务
export default {
  name: 'AttackLog',
  props: {
    tableData: {
      type: Array,
      default: [],
    },
  },
  watch: {
    tableData: {
      handler(newVal, oldVal) {
        this.tableData = newVal;
      },
      deep: true,
    },
  },
  data() {
    return {
      rollTime: 5,
      rollPx: 1,
    };
  },
  methods: {
    dateFormat(row, column) {
      var moment = require('moment');
      var date = row[column.property];
      return moment(date).format('YYYY-MM-DD hh:mm:ss');
    },
    // 鼠标进入
    mouseEnter(time) {
      // 鼠标进入停止滚动和切换的定时任务
      this.autoRoll(true);
    },
    // 鼠标离开
    mouseLeave() {
      // 开启
      this.autoRoll(false);
    },
    autoRoll(stop) {
      if (stop) {
        clearInterval(rolltimer);
        return;
      }
      // 拿到表格挂载后的真实DOM
      const table = this.$refs.rw_table;
      // 拿到表格中承载数据的div元素
      const divData = table.bodyWrapper;
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果
      rolltimer = setInterval(() => {
        // 元素自增距离顶部像素
        divData.scrollTop += this.rollPx;
        // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
        if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
          // 重置table距离顶部距离
          divData.scrollTop = 0;
        }
      }, this.rollTime * 10);
    },
    //攻击日志
    tableRowClassName({ row, rowIndex }) {
      // console.log(row);
      if (row.severity === 4) {
        return 'warning-row';
      } else if (row.severity === 3) {
        return 'danger-row';
      } else if (row.severity === 2) {
        return 'warm-row';
      } else if (row.severity === 1) {
        return 'normal-row';
      }
      return '';
    },
  },
};
</script>

<style scoped>
.el-table {
  overflow: hidden;
  background: none;
  border-spacing: 0;
}
.el-table::v-deep .el-table__body-wrapper {
  height: 268px;
  overflow: hidden;
}
.el-table::v-deep th,
.el-table::v-deep tr {
  background: none;
  color: #fff;
  font-size: 14px;
}
.el-table::v-deep td {
  padding: 4px 0;
}
.el-table::v-deep th {
  background-color: none !important;
  padding: 5px 0;
}

.el-table::v-deep td,
.el-table::v-deep th.is-leaf {
  border-bottom: none;
}
.el-table::v-deep tbody tr:hover > td {
  background: none !important;
}

.el-table::v-deep tbody tr:hover {
  background: none !important;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}
.el-table::v-deep::before {
  height: 0;
}
.el-table::v-deep td .cell {
  height: 36px;
  line-height: 36px;
}
.el-table::v-deep tr td:first-child .cell {
  background: none !important;
}
.el-table::v-deep .warning-row td .cell {
  background: rgba(236, 89, 96, 0.2);
}

.el-table::v-deep .warm-row td .cell {
  background: rgba(242, 174, 27, 0.2);
}
.el-table::v-deep .normal-row td .cell {
  background: rgba(0, 94, 164, 0.2);
}
.el-table::v-deep .danger-row td .cell {
  background: rgba(188, 78, 115, 0.2);
}
.el-table::v-deep .el-table_1_column_1 .cell {
  background: none !important;
}
.bg-level {
  display: inline-block;
  width: 100%;
  height: 36px;
  text-align: center;
  line-height: 36px;
}
.bg-warm {
  background: rgba(242, 174, 27, 1);
}
.bg-warn {
  background: rgba(236, 89, 96, 1);
}
.bg-normal {
  background: rgba(0, 94, 164, 1);
}
.bg-danger {
  background: rgba(188, 78, 115, 1);
}
.el-table::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 0;
}
</style>
