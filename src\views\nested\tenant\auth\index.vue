<template>
  <div class="mainWrapper">
    <!-- <div class="login-box">
        <ul class="login-item-container clearfix">
          <li class="login-item" v-for="(item,index) in poductList" :key="index">
            <router-link to="/">
              <div class="login-item-box">
                <div>
                     <h2>{{item.name}}</h2>
                     <span class="status active" v-if="item.status==1">激活</span>
                      <span class="status " v-if="item.status==2">未激活</span>
                </div>
             
                
              </div>
            </router-link>
          </li>
        </ul>
      </div>-->
    <div class="mainBox auth-box" v-if="showTenant">
      <div class="header">
        <h3 class="title">授权周期</h3>
      </div>
      <div class="auth-content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="云防火墙" name="firewallData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="firewallData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column prop="cust" label="客户" sortable width="180"></el-table-column>
              <el-table-column prop="type" label="类型" sortable></el-table-column>
              <el-table-column prop="time" label="有效时间" sortable></el-table-column>
              <el-table-column prop="info" label="其他信息"></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云WAF" name="wafData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="wafData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column
                    prop="cust"
                    label="客户"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="time"
                    label="有效时间"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="info"
                    label="其他信息"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云日志审计" name="logAuditData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="logAuditData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column
                    prop="sn"
                    label="设备SN"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="cust"
                    label="客户"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="capacity"
                    label="存储大小"
                  ></el-table-column>
                  <el-table-column
                    prop="time"
                    label="有效时间"
                    sortable
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云堡垒机" name="fortressData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="fortressData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column
                    prop="customInfo"
                    label="客户信息"
                    sortable
                    width="120"
                  ></el-table-column>
                  <el-table-column
                    prop="authType"
                    label="类型"
                    sortable
                    width="120"
                  ></el-table-column>
                  <el-table-column prop="status" label="状态" width="120">
                    <template slot-scope="scope">
                      <div>
                        <span v-if="scope.row.status == 0" class="normal"
                          >未激活</span
                        >
                        <span v-if="scope.row.status == 1" class="abnormal"
                          >已激活</span
                        >
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="prodId"
                    label="产品ID"
                  ></el-table-column>
                  <el-table-column
                    prop="module"
                    label="授权模块"
                  ></el-table-column>
                  <el-table-column
                    prop="limitResource"
                    label="授权资源数"
                  ></el-table-column>
                  <el-table-column
                    prop="limitConn"
                    label="授权资源并发连接数"
                  ></el-table-column>
                  <el-table-column
                    prop="expireDate"
                    label="过期时间"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云数据库审计" name="databaseData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="databaseData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column
                    prop="customer"
                    label="单位名称"
                    sortable
                    width="120"
                  ></el-table-column>
                  <el-table-column
                    prop="expiredTime"
                    label="审计有效期"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="instanceNum"
                    label="资产数"
                    width="90"
                  ></el-table-column>
                  <el-table-column
                    prop="deviceName"
                    label="设备名称"
                  ></el-table-column>
                  <el-table-column
                    prop="version"
                    label="版本号"
                    width="80"
                  ></el-table-column>
                  <el-table-column
                    prop="publish"
                    label="发布号"
                    width="80"
                  ></el-table-column>
                  <el-table-column
                    prop="mgt"
                    label="管理中心编译号"
                  ></el-table-column>
                  <el-table-column
                    prop="proc"
                    label="数据处理中心编译号"
                  ></el-table-column>
                  <el-table-column
                    prop="buildTime"
                    label="编译时间"
                  ></el-table-column>
                  <el-table-column
                    prop="factory"
                    label="设备厂家"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云主机安全" name="hostData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="hostData"
                  style="width: 100%;margin-bottom:20px;"
                  row-key="id"
                >
                  <el-table-column
                    prop="obj"
                    label="授权对象"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="授权类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="content"
                    label="授权内容"
                  ></el-table-column>
                  <el-table-column
                    prop="moduleName"
                    label="模块名称"
                  ></el-table-column>
                  <el-table-column
                    prop="time"
                    label="到期时间"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
       activeName: 'firewallData',
      showTenant: true,
      firewallData: [
        {
          cust: "Trial user",
          type: "URL DB",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "应用特征库",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "入侵防御",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "病毒过滤",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "SCVPN",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "IPSEC VPN",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Trial user",
          type: "平台",
          time: "有效期剩余29天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "",
          type: "平虚拟机CPU",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "终端防护",
          time: "未授权",
          info: ""
        }
      ],
      wafData: [
        {
          cust: "Hillstone",
          type: "平台",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Hillstone",
          type: "WAF规则库",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "",
          type: "虚拟机CPU",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "反爬虫服务",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "WAF IP信誉库",
          time: "未授权",
          info: ""
        }
      ],
      logAuditData: [
        {
          sn: "0000000000000",
          cust: "Hillstone Network ",
          type: "VHSA",
          capacity: "200G",
          time: "--"
        }
      ],
      fortressData: [
        {
          cust: "-",
          type: "正式版",
          status: "未激活",
          productID: "736010b0e728428db294b6ac7b1ec188",
          module: "基础模块",
          resourcesNum: "50",
          connectionsNum: "20",
          time: "2020-07-17 03:11:10"
        }
      ],
      databaseData: [
        {
          name: "池州市政务云",
          time: "2020-07-29",
          assetsNum: "20",
          deviceName: "数据库审计和防护系统",
          version: "3.0",
          issueNum: "********",
          compilationNum: "14351",
          dataNum: "14470",
          satrtTime: "2020-06-08 14：31",
          factory: "闪捷信息科技有限公司"
        }
      ],
      windowList:[{
        img:require('@/assets/module/bingdu.png'),
        name:'防病毒',
        time:'2020-07-19'
      },
      {
        img:require('@/assets/module/well.png'),
        name:'防火墙',
        time:'2020-07-19'
      },{
        img:require('@/assets/module/well.png'),
        name:'入侵防御',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'放暴力破解',
        time:'2020-07-19'
      },
      {
        img:require('@/assets/module/well.png'),
        name:'webshell扫描',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'安全基线',
        time:'2020-07-19'
      },
      {
         img:require('@/assets/module/well.png'),
        name:'虚拟化加固',
        time:'2020-07-19'
      },
      {
       img:require('@/assets/module/well.png'),
        name:'网卡流量统计',
        time:'2020-07-19'
      }
      ],
       hostData:[{
        obj:'试用授权',
        type:'试用授权',
        content:'windows_server',
        moduleName:' 防病毒',
        time:'2020-07-19',

      },
      {
        obj:'试用授权',
        type:'试用授权',
        content:'windows_server',
        moduleName:' 防火墙',
        time:'2020-07-19',

      },
      {
        obj:'试用授权',
        type:'试用授权',
        content:'windows_server',
        moduleName:'入侵防御',
        time:'2020-07-19',

      },
         {
        obj:'试用授权',
        type:'试用授权',
        content:'linux_server',
        moduleName:' webshell扫描',
        time:'2020-07-19',

      },   {
        obj:'试用授权',
        type:'试用授权',
        content:'linux_server',
        moduleName:'放暴力破解',
        time:'2020-07-19',

      },
      {
        obj:'试用授权',
        type:'试用授权',
        content:'linux_server',
        moduleName:' 防病毒',
        time:'2020-07-19',

      },
      {
        obj:'试用授权',
        type:'试用授权',
        content:'linux_server',
        moduleName:' 防火墙',
        time:'2020-07-19',

      },
      {
        obj:'试用授权',
        type:'试用授权',
        content:'CPU',
        moduleName:'入侵防御',
        time:'2020-07-19',

      },
         {
        obj:'试用授权',
        type:'试用授权',
        content:'CPU',
        moduleName:' webshell扫描',
        time:'2020-07-19',

      },   {
        obj:'试用授权',
        type:'试用授权',
        content:'CPU',
        moduleName:'放暴力破解',
        time:'2020-07-19',

      }
      ]
    };
  },
  created() {},
  methods: {
     handleClick(tab, event) {
        // console.log(tab, event);
      }
  }
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 174px);
}
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .auth-box {
    .auth-content {
      margin-top: 10px;
      .auth-item {
        padding: 0 30px;
      }
    }
  }
}
</style>
