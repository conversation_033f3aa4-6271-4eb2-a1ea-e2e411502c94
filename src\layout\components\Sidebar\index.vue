<template>
  <div :class="{ 'has-logo': showLogo }">
    <!-- <logo v-if="showLogo" :collapse="isCollapse" /> -->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        ref="menus"
        :default-active="routerPath"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        :default-openeds="openeds"
        @close="handleClose"
        mode="vertical"
      >
        <!-- <sidebar-item v-for="route in routes" :key="route.path" :item="route" :base-path="route.path" /> -->
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.id"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div class="change-nav">
      <hamburger
        :is-active="sidebar.opened"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/styles/variables.scss";
import Hamburger from "@/components/Hamburger";


export default {
  components: { SidebarItem, Logo, Hamburger },
  data() {
    return {
      openeds: [],
    };
  },
  mounted() {
    this.openeds=this.openMenus;
    console.log(this.permission_routes);
  },
 
  computed: {
    ...mapGetters(["permission_routes", "sidebar","openMenus"]),
    routes() {
      return this.$router.options.routes;
    },
    activeMenu() {
      const route = this.$route;
      console.log(route);
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
    routerPath() {
      return this.$route.meta.guidePath ? this.$route.meta.jumpPath : this.$route.path;
    },
  },
  watch: {
    openeds(val){
      this.openeds=val;
    }
  },
  methods: {
    toggleSideBar() {
       this.openeds=this.openMenus.reverse();
      // console.log(this.openeds);
      this.$store.dispatch("app/toggleSideBar");
     
    },
    handleClose(key, keyPath) {
      console.log(key);
      this.$refs.menus.open(keyPath);
    },
  },
};
</script>
<style lang="scss" scoped>

</style>
