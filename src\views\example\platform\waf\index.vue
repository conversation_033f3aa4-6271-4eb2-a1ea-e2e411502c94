<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="mainWrapper">
     
      <div class="mainBox auth-box">
        
        <div class="auth-item">
          <h2 class="title">云WAF(山石网科)</h2>
          <el-table :data="wafData" style="width: 100%;margin-bottom:20px;" row-key="id" border>
            <el-table-column prop="cust" label="客户" sortable width="180"></el-table-column>
            <el-table-column prop="type" label="类型" sortable></el-table-column>
            <el-table-column prop="time" label="有效时间" sortable></el-table-column>
            <el-table-column prop="info" label="其他信息"></el-table-column>
          </el-table>
        </div>
       
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
export default {
  data() {
    return {
      showTenant: true,
   
      wafData: [
        {
          cust: "Hillstone",
          type: "平台",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "Hillstone",
          type: "WAF规则库",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证"
        },
        {
          cust: "",
          type: "虚拟机CPU",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "反爬虫服务",
          time: "未授权",
          info: ""
        },
        {
          cust: "",
          type: "WAF IP信誉库",
          time: "未授权",
          info: ""
        }
      ],
     
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.mainWrapper {
  background: #F1F6FA;

 
  .auth-box {
    .auth-item {
      .title {
        font-weight: normal;
        font-size: 14px;
        color: #409eff;
        padding-left: 10px;
        border-left: 2px solid #409eff;
        margin-bottom: 16px;
      }
    
    }
  }
}
</style>
