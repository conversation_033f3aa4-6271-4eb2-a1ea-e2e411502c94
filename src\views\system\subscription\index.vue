<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header">
        <h3 class="title">内容订阅</h3>
      </div>
      <!-- <div class="serch-box clearfix">
        <div class="filter-container">
          <el-button v-waves class="filter-item" type="primary" @click="handleRefresh()">
            <svg-icon icon-class="refresh" />
          </el-button>
        </div>
      </div> -->
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="form-box">
          <div class="form-box-hd clearfix">
            <div class="form-box-left">
              <h3 class="text">基本信息</h3>
            </div>
            <div class="form-box-right">
              <el-form :model="dataForm" ref="dataForm" :rules="dataFormRules"  @keyup.enter.native="dataFormSubmitHandle()">
                <el-form-item
                  label="姓名"
                  :label-width="formLabelWidth"
                  prop="name"
                >
                  <el-input
                    v-model="dataForm.name"
                    autocomplete="off"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="邮箱"
                  :label-width="formLabelWidth"
                  prop="email"
                >
                  <el-input
                    type="text"
                    v-model="dataForm.email"
                    autocomplete="off"
                  ></el-input>
                </el-form-item>

                <el-form-item
                  label="手机号"
                  :label-width="formLabelWidth"
                  prop="phone"
                >
                  <el-input
                    v-model="dataForm.phone"
                    autocomplete="off"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="公司"
                  :label-width="formLabelWidth"
                  prop="corporation"
                >
                  <el-input
                    v-model="dataForm.corporation"
                    autocomplete="off"
                  ></el-input>
                </el-form-item>
                <el-form-item :label-width="formLabelWidth">
                  <div class="tips">请选择以下推送的方式(可多选)</div>
                </el-form-item>
                <el-form-item :label-width="formLabelWidth" >
                
                    <el-checkbox
                      name="type"
                      v-model="dataForm.disposeEmail" :true-label=1 :false-label=0
                      @change="handleChange(value=dataForm.disposeEmail,type='email')" 
                    >
                      <img
                        src="@/assets/email.png"
                        alt=""
                        title="邮箱"
                        class="img-way"
                      />
                    
                    </el-checkbox>
                       <el-checkbox
                      name="type"
                      v-model="dataForm.disposeWechat" :true-label=1 :false-label=0
                       @change="handleChange(value=dataForm.disposeWechat,type='weChat')"
                    >
                    
                      <img
                        src="@/assets/weChat.png"
                        alt=""
                       
                        title="微信"
                        class="img-way"
                      />
                     
                    </el-checkbox>
                      <el-checkbox
                     
                      name="type"
                      v-model="dataForm.disposeDing" :true-label=1 :false-label=0
                      @change="handleChange(value=dataForm.disposeDing,type='ding')"
                    >
                     
                    
                      <img
                        src="@/assets/nail.png"
                        alt=""
                       
                        title="钉钉"
                        class="img-way"
                      />
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item :label-width="formLabelWidth">
                  <el-button type="primary" class="submit-btn"  @click="dataFormSubmitHandle()"
                    >保存设置</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { dispose, modifyDispose } from '@/api/modules/subscription';
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
let validateEmail = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('邮箱不能为空！'));
  } else {
    const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
    if (reg.test(value)) {
      callback();
    } else {
      return callback(new Error('邮箱格式不正确！'));
    }
  }
};
var checkPhone = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('手机号不能为空'));
  } else {
    //验证手机号
    const reg = new RegExp('^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-9])|(18[0-9])|166|198|199|191|(147))\\d{8}$');
    //验证区号
    // const phoneReg = /^\d{3}-\d{8}|\d{4}-\d{7}$/;
    // console.log(reg.test(value));
    if (reg.test(value)) {
      callback();
    } else {
      return callback(new Error('请输入正确的联系方式'));
    }
  }
};
export default {
  data() {
    return {
      formLabelWidth: '120px',
      dataForm: {
        userId: '',
        userName: '',
        name: '',
        email: '',
        phone: '',
        corporation: '',
        disposeEmail: 0, //（选中为1，未选为0）,
        disposeWechat: 0, //（选中为1，未选为0）,
        disposeDing: 0, //（选中为1，未选为0）
      },
      // waysList: [
      //   {
      //     label: '0',
      //     value: 'weChat',
      //   },
      //   {
      //     label: '0',
      //     value: 'dingding',
      //   },
      //   {
      //     label: '0',
      //     value: 'email',
      //   },
      // ],
      dataFormRules: {
        // name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        // email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
        // phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        // corporation: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
        // // checkedWays: [
        // //   {
        // //     type: 'array',
        // //     required: true,
        // //     message: '请至少选择一种推送方式',
        // //     trigger: 'change',
        // //   },
        // // ],
      },
    };
  },
  mounted() {
    this.dataForm.userId = this.userid;
    this.dataForm.userName = this.name;
    this.getData();

  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'name']),
  },
  methods: {
    getData() {
      let data = {
        userId: this.userid,
      };
      dispose(data)
        .then((res) => {
          console.log(res);

          if (res.code !== 1) {
            return this.$message.error(res.msg);
          }
          this.dataForm = {
            ...this.dataForm,
            ...res.data,
          };
          this.setRules()

        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleChange(value, type) {
      console.log(value);
      console.log(type);
      if (type == 'email') {
        this.dataForm.disposeEmail = value
      } else if (type == 'weChat') {
        this.dataForm.disposeWechat = value
      } else if (type == 'ding') {
        this.dataForm.disposeDing = value
      }
      this.setRules()

    },
    setRules() {
      console.log('设置表单验证')
      console.log(this.dataForm)
      if (this.dataForm.disposeEmail == 1 || this.dataForm.disposeWechat == 1 || this.dataForm.disposeDing == 1) {
        this.dataFormRules = {
          name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
          email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
          phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
          corporation: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],

        }
      } else {
        this.dataFormRules = {
          name: [{ required: false }],
          email: [{ required: false }],
          phone: [{ required: false }],
          corporation: [{ required: false, trigger: 'blur' }],
        }
      }
    },

    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false;
          }
          console.log(this.dataForm);
          // let data = {
          //   userId: this.dataForm.userId,
          //   userName: this.dataForm.userName,
          //   name: this.dataForm.name,
          //   email: this.dataForm.email,
          //   phone: this.dataForm.phone,
          //   corporation: this.dataForm.corporation,
          //   disposeEmail: this.dataForm.disposeEmail, //（选中为1，未选为0）,
          //   disposeWechat: this.dataForm.disposeWechat, //（选中为1，未选为0）,
          //   disposeDing: this.dataForm.disposeDing, //（选中为1，未选为0）
          // };
          // console.log(data)

          modifyDispose(this.dataForm)
            .then((res) => {
              if (res.code !== 1) {
                return this.$message.error(res.msg);
              }
              this.$message({
                message: '保存成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getData();
                },
              });
            })
            .catch(() => { });
        });
      },
      1000,
      { leading: true, trailing: false }
    ),

  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}
.mainWrapper {
  height: calc(100vh - 48px);
  background: #fff;
  .mainBox {
    .tips {
      font-size: 13px;
    }
    .img-way {
      width: 28px;
      vertical-align: middle;
    }
    .submit-btn {
      padding: 0 30px;
      margin: 20px 0 40px 0;
    }
  }
}
.el-input::v-deep,
.el-select::v-deep {
  width: 300px;
}
.el-textarea::v-deep textarea {
  width: 320px !important;
}
</style>
