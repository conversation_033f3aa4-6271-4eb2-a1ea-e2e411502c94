<template>
  <div class="box">
    <div class="mainWrapper">
      <div
        class="wrap"
        :style="{
          transform: `scale(${scalseNum},${scalseNum}) translateX(-50%)`
        }"
      >
        <div class="head" :style="{ backgroundImage: 'url(' + headBg + ')' }">
          <span class="title">{{ screenTitle }}</span>
          <!-- <img src="@/assets/security/switch.png" alt="" title="大屏切换" class="switch-btn" /> -->

          <div class="datebox">
            <img :src="screenImg" alt="" style="margin-right: 10px" />
            <!-- 
             -->
            <div class="time">{{ nowTime }}</div>
            <div class="content">
              <p class="week">{{ nowWeek }}</p>
              <p class="date">{{ nowDate }}</p>
            </div>
          </div>
        </div>

        <div class="mainbox">
          <el-row :gutter="15">
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <div class="grid-content grid-content-lg">
                <div class="all-title">
                  <span class="name">系统状态</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="grid-content-lg-item">
                  <div class="title">密服平台服务监控</div>
                  <el-carousel indicator-position="none" height="190px">
                    <el-carousel-item
                      v-for="(item, index) in serviceruninfo"
                      :key="index"
                    >
                      <ul class="grid-box clearfix">
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-type.png"
                              alt=""
                            />
                            <p>服务类型</p>
                          </div>
                          <div class="box-bd">
                            {{ item.name }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-status.png"
                              alt=""
                            />
                            <p>状态</p>
                          </div>
                          <div class="box-bd">
                            <span v-if="item.status == '0'">不通</span>
                            <span v-else-if="item.status == '1'">正常</span>
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-ip.png"
                              alt=""
                            />
                            <p>IP</p>
                          </div>
                          <div class="box-bd time">
                            {{ item.ip }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-time.png"
                              alt=""
                            />
                            <p>时间</p>
                          </div>
                          <div class="box-bd time">
                            {{ item.createTime }}
                          </div>
                        </li>
                      </ul>
                    </el-carousel-item>
                  </el-carousel>
                </div>
                <div class="all-title">
                  <span class="name">设备状态</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="grid-content-lg-item">
                  <div class="title">密码机状态监控</div>
                  <el-carousel indicator-position="none" height="280px">
                    <el-carousel-item
                      v-for="(item, index) in jmjInfoData"
                      :key="index"
                    >
                      <ul class="grid-box clearfix">
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-name.png"
                              alt=""
                            />
                            <p>设备名称</p>
                          </div>
                          <div class="box-bd">
                            {{ item.devName }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-status.png"
                              alt=""
                            />
                            <p>设备状态</p>
                          </div>
                          <div class="box-bd">
                            <span v-if="item.status == 1">录入</span>
                            <span v-else-if="item.status == 2">审批</span>
                            <span v-else-if="item.status == 3">接入</span>
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-total.png"
                              alt=""
                            />
                            <p>对称密钥个数</p>
                          </div>
                          <div class="box-bd">
                            {{ item.dcNum }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img src="@/assets/security/device-yy.png" alt="" />
                            <p>SM2密钥个数</p>
                          </div>
                          <div class="box-bd">
                            {{ item.sm2Num }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-type.png"
                              alt=""
                            />
                            <p>内存使用率</p>
                          </div>
                          <div class="box-bd">
                            {{ arrFormat(item.devFunction)[3] }}/{{
                              arrFormat(item.devFunction)[1]
                            }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-status.png"
                              alt=""
                            />
                            <p>CPU使用率</p>
                          </div>
                          <div class="box-bd">
                            {{ arrFormat(item.devFunction)[5] }}
                          </div>
                        </li>
                      </ul>
                    </el-carousel-item>
                  </el-carousel>
                </div>
                <div class="grid-content-lg-item">
                  <div class="title">签名服务器状态监控</div>
                  <el-carousel
                    indicator-position="none"
                    height="280px"
                    v-if="qmfwqData.length > 0"
                  >
                    <el-carousel-item
                      v-for="(item, index) in qmfwqData"
                      :key="index"
                    >
                      <ul class="grid-box clearfix">
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-name.png"
                              alt=""
                            />
                            <p>设备名称</p>
                          </div>
                          <div class="box-bd">
                            {{ item.devName }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-status.png"
                              alt=""
                            />
                            <p>设备状态</p>
                          </div>
                          <div class="box-bd">
                            <span v-if="item.status == 1">录入</span>
                            <span v-else-if="item.status == 2">审批</span>
                            <span v-else-if="item.status == 3">接入</span>
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-total.png"
                              alt=""
                            />
                            <p>密钥总容量</p>
                          </div>
                          <div class="box-bd">
                            {{ item.keyVol }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img src="@/assets/security/device-yy.png" alt="" />
                            <p>已用的SM2密钥数</p>
                          </div>
                          <div class="box-bd">
                            {{ item.sm2Used }}
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/device-type.png"
                              alt=""
                            />
                            <p>内存使用率</p>
                          </div>
                          <div class="box-bd">
                            {{
                              (Number(item.memUsed) / 1024 / 1024).toFixed(2)
                            }}G/{{
                              (Number(item.memTotal) / 1024 / 1024).toFixed(2)
                            }}G
                          </div>
                        </li>
                        <li>
                          <div class="box-top">
                            <img
                              src="@/assets/security/service-status.png"
                              alt=""
                            />
                            <p>CPU使用率</p>
                          </div>
                          <div class="box-bd">
                            {{
                              ((
                                Number(item.cpuUsed) / Number(item.cpuTotal)
                              ).toFixed(4) *
                                10000) /
                              100
                            }}%
                          </div>
                        </li>
                      </ul>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <div
                class="grid-content grid-content-md"
                style="padding: 0 20px; height: 530px"
              >
                <div class="nav-box">
                  <img src="@/assets/security/passwordSafe_1.png" alt />
                </div>
                <div class="dot1-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot2-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot3-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot4-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot5-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="dot6-box">
                  <img src="@/assets/security/circle-dot.png" alt class="dot" />
                </div>
                <div class="middle-box">
                  <img src="@/assets/security/passwordSafe_3.png" alt />
                </div>
                <div class="serve-box">
                  <img src="@/assets/security/passwordSafe_4.png" alt />
                </div>
                <div class="person-box">
                  <img src="@/assets/security/passwordSafe_2.png" alt />
                </div>
              </div>
              <div class="grid-content grid-content-md" style="padding: 0 20px">
                <div class="all-title">
                  <span class="name">密服平台状态监控</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="ip-box">
                  <img src="@/assets/security/border.png" alt="" class="left" />
                  <img
                    src="@/assets/security/border.png"
                    alt=""
                    class="right"
                  />
                  {{ sysrunIp }}
                </div>
                <div class="chart-box">
                  <div
                    class="chart-box-item"
                    v-for="(item, index) in sysData"
                    :key="index"
                  >
                    <pie-chart
                      :id="'chart_' + index"
                      :chartData="item"
                      v-if="flag.pie"
                    />
                  </div>
                  <!-- <div class="chart-box-item">
                    <pie-chart
                      :id="chart1"
                      :colorArr="cpuColor"
                      :chartData="cpuData"
                       type="cpu"
                      v-if="flag.pie"
                    />
                  </div>
                  <div class="chart-box-item">
                    <pie-chart
                      :id="chart2"
                      :colorArr="memoryColor"
                      :chartData="memoryData"
                       type="memory"
                      v-if="flag.pie"
                    />
                  </div>
                  <div class="chart-box-item">
                    <pie-chart
                      :id="chart3"
                      :colorArr="diskColor"
                      :chartData="diskData"
                      type="disk"
                      v-if="flag.pie"
                    />
                  </div> -->
                </div>
                <!-- <div class="title">密钥库类型</div>
                <div class="bar-box">
                  <bar-chart :id="barChart" :chartData="barData" v-if="flag.bar" />
                </div> -->
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="6" :lg="6">
              <!-- <div class="grid-content">
                <div class="all-title">
                  <span class="name">汇总</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="summary-box">
                  <div class="summary-box-item left">
                    <div class="text mb">
                      <span>{{ devtotalinfo.serviceTotal }}</span> 次
                    </div>
                    <div class="text">系统服务数量</div>
                  </div>
                  <div class="summary-box-item">
                    <div class="text">数量汇总</div>
                  </div>
                  <div class="summary-box-item left">
                    <div class="text mb">
                      <span>{{ devtotalinfo.deviceTotal }}</span> 个
                    </div>
                    <div class="text">设备数量</div>
                  </div>
                </div>
              </div> -->
              <div class="grid-content">
                <div class="all-title">
                  <span class="name">业务调用数据</span>
                  <div class="icon">
                    <i></i>
                    <i></i>
                    <i></i>
                  </div>
                  <i class="line"></i>
                </div>
                <div class="table-box" v-if="businessInfo.length > 0">
                  <swiper ref="mySwiper" :options="swiperOptions">
                    <swiper-slide
                      v-for="(item, index) in businessInfo"
                      :key="index"
                    >
                      <div class="table-cell table-cell-bd">
                        <span class="nickName">应用名称：</span>
                        <span class="content">{{ item.appName }}</span>
                      </div>
                      <div class="table-cell">
                        <div class="table-cell-bd cell table-cell-space">
                          <span class="nickName">接口类型：</span>
                          <span class="content">{{ item.apiType }}</span>
                        </div>
                        <div class="table-cell-bd cell">
                          <span class="nickName">调用次数：</span>
                          <span class="content">{{ item.callNum }}</span>
                        </div>
                      </div>
                      <div class="table-cell table-cell-bd">
                        <span class="nickName">调用成功次数：</span>
                        <span class="content">{{ item.callBackNum }}</span>
                      </div>
                      <div class="table-cell table-cell-bd">
                        <span class="nickName">调用时间：</span>
                        <span class="content">{{ item.callTime }}</span>
                      </div></swiper-slide
                    >

                    <!-- <div class="swiper-pagination" slot="pagination"></div> -->
                  </swiper>

                  <!-- <el-table :data="businessInfo" height="100%" style="width: 100%" ref="rw_table"
                    @mouseenter.native="mouseEnter" @mouseleave.native="mouseLeave">
                    <el-table-column prop="appName" label="应用名称" width="110"></el-table-column>
                    <el-table-column prop="apiType" label="接口类型" width="116"></el-table-column>
                    <el-table-column prop="callNum" label="调用次数" width="80"></el-table-column>
                    <el-table-column prop="callBackNum" label="调用成功次数" width="110"></el-table-column>
                    <el-table-column prop="callTime" label="调用时间" show-overflow-tooltip></el-table-column>
                  </el-table> -->
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div id="prompt" ref="prompt" class>
      为获得最佳体验效果，浏览器最小窗口宽度需大于1300px
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import headBg2 from '@/assets/security/head_bg.png';
import { device } from '@/api/flow.js';
import moment from 'moment';
import { getExpireDate } from '@/api/user.js';
import pieChart from '@/components/PieChart';
import barChart from '@/components/BarChart';
import { sbfw, mmyw } from '@/api/security.js';
import { qmfwqInfo, jmjInfo } from '@/api/monitoring.js';

let rolltimer = ''; // 自动滚动的定时任务
export default {
  name: 'Password',
  components: {
    pieChart,
    barChart,
  },
  data() {
    return {
      screenTitle: '', //大屏标题
      screenImg: '', //大屏图片
      scalseNum: 1,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight, // 屏幕尺寸高度
      marginTop: 0,
      timer: null,
      timerZ: null,
      num: 0,
      leftDis: 9,
      headBg: headBg2,
      nowDate: '', // 当前日期
      nowTime: '', // 当前时间
      nowWeek: '', // 当前星期
      bodyBgImage: '',
      mapBox: require('@/assets/security/lbx.png'),
      widthnum: '100%',
      defartIndex: 0,
      echartsArr: [],
      // chart1: "chart1",
      // chart2: "chart2",
      // chart3: "chart3",
      // cpuColor: ["#E11FFE ", "#030F42"],
      // memoryColor: ["#2BD0E7", "#030F42"],
      // diskColor: ["#3964F8", "#030F42"],
      // cpuData: {},
      // memoryData: {},
      // diskData: {},
      barChart: 'barChart',
      barData: {
        xArr: [],
        yArr: [],
      },
      businessInfo: [], //业务数据
      rollTime: 5,
      rollPx: 1,
      devinfo: [], //设备数据
      serviceruninfo: [], //服务情况
      devtotalinfo: {}, //系统汇总
      strategyinfo: [], //密码策略
      keyinfo: [], //密钥库类型
      sysruninfo: [], //系统运行数据
      sysrunIp: '', //系统运行数据ip
      sysData: [], //内存、cpu、硬盘数据
      flag: {
        bar: false, //是否开始柱状图渲染子组件
        pie: false,
      },
      swiperOptions: {
        slidesPerView: 3,
        autoplay: {
          delay: 3000, // 5秒切换一次
        },
        // 设置轮播可循环
        loop: true,
        direction: 'vertical',
      },
      qmfwqData: [], //签名服务器信息
      jmjInfoData: [], //加密机信息
    };
  },
  computed: {
    ...mapGetters(['name', 'userid', 'version']),
  },

  created() {
    this.screenTitle = localStorage.getItem('psdScreenTitle'); //大屏标题
    this.screenImg = localStorage.getItem('psdScreenImg'); //大屏图片
    this.versionSreen = this.version;
    // this.versionSreen=1;
    // console.log(this.versionSreen);
    this.getList();

    // this.timerList();
  },
  watch: {
    // sysData(newVal, oldVal) {
    //   console.log(newVal);
    //   console.log('父组件数据');
    //   this.$nextTick(() => {
    //     this.flag.pie = true; //重建组件
    //     this.sysData = newVal; //传入新值
    //   });
    //   console.log(this.flag.pie);
    // }
  },
  methods: {
    //切换网络安全态势
    // changeScreen() {
    //   this.$router.push({ path: `/security/network/index` });
    // },
    dateFormat(row, column) {
      var moment = require('moment');
      var date = row[column.property];
      return moment(date).format('YYYY-MM-DD hh:mm:ss');
    },
    getList() {
      let that = this;
      //设备服务态势
      sbfw()
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            this.devinfo = res.data.devinfo_showlist; //设备数据
            this.serviceruninfo = res.data.serviceruninfo; //服务情况
            this.devtotalinfo = res.data.devtotalinfo[0];
            this.sysruninfo = res.data.sysruninfo; //系统运行数据
            // console.log(this.sysruninfo);
            if (this.sysruninfo.length > 0) {
              this.sysrunIp = this.sysruninfo[0].ip;
              //  this.sysData = this.sysruninfo[0].sysData;
              //  this.flag.pie = true;
              this.sysruninfo.forEach((item) => {
                let that = this;
                this.flag.pie = false;
                this.$nextTick(() => {
                  setTimeout(() => {
                    that.sysData = item.sysData;
                    // console.log(that.sysData);
                    this.flag.pie = true;
                  }, 1000);
                });
              });
            }
          } else {
            this.$message(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
      //密码业务态势
      mmyw()
        .then((res) => {
          console.log(res);
          this.barData.xArr = [];
          this.barData.yArr = [];
          if (res.code == 1) {
            this.strategyinfo = res.data.strategyinfo; //密码策略
            this.keyinfo = res.data.keyinfo; //密钥库类型
            this.businessInfo = res.data.businessinfo; //业务数据
            this.flag.bar = true;
            if (this.keyinfo.length > 0) {
              this.keyinfo.forEach((item) => {
                this.barData.xArr.push(Number(item.num));
                this.barData.yArr.push(item.type);
              });
            }
          } else {
          }
        })
        .catch((error) => {
          console.log(error);
        });
      //签名服务器信息
      qmfwqInfo()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            if (res.data != null) {
              this.qmfwqData = res.data;
              console.log(`签名服务器信息`);
              console.log(this.qmfwqData);
            }
          } else {
          }
        })
        .catch((error) => {
          console.log(error);
        });
      //加密机信息
      jmjInfo()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            if (res.data != null) {
              this.jmjInfoData = res.data;
              // console.log(`加密机信息`);
              console.log(this.jmjInfoData);
            }
          } else {
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    autoRoll(stop) {
      if (stop) {
        clearInterval(rolltimer);
        return;
      }
      // 拿到表格挂载后的真实DOM
      const table = this.$refs.rw_table;
      // 拿到表格中承载数据的div元素
      const divData = table.bodyWrapper;
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果
      rolltimer = setInterval(() => {
        // 元素自增距离顶部像素
        divData.scrollTop += this.rollPx;
        // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
        if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
          // 重置table距离顶部距离
          divData.scrollTop = 0;
        }
      }, this.rollTime * 10);
    },
    // 鼠标进入
    mouseEnter(time) {
      // 鼠标进入停止滚动和切换的定时任务
      this.autoRoll(true);
    },
    // 鼠标离开
    mouseLeave() {
      // 开启
      this.autoRoll();
    },
    currentTime() {
      setInterval(this.getDate, 500);
    },
    timerList() {
      if (this.timer != null) {
        return;
      }
      // 计时器为空，操作
      this.timer = setInterval(() => {
        // console.log("刷新" + new Date());
        this.getList(); //加载数据函数
      }, 1000 * 60 * 2);
    },
    async logout() {
      await this.$store.dispatch('user/logout');
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
    //授权校验
    getExpire() {
      getExpireDate()
        .then((res) => {
          // console.log(res);
          if (res.code == 1) {
            //验证状态 1为已过期，强制退出登录，0为未过期
            if (res.data.status == 1) {
              this.$message.error('授权已过期');
              this.logout();
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    timerZstack() {
      // console.log(this.timerZ);
      if (this.timerZ != null) {
        return;
      }
      this.timerZ = setInterval(() => {
        setTimeout(this.getExpire, 0); //定时校验
      }, 300000);
    },
    getDate() {
      var _this = this;
      let yy = new Date().getFullYear();
      let mm = new Date().getMonth() + 1;
      let dd = new Date().getDate();
      let week = new Date().getDay();
      let hh = new Date().getHours();
      let mf =
        new Date().getMinutes() < 10
          ? '0' + new Date().getMinutes()
          : new Date().getMinutes();
      let ms =
        new Date().getSeconds() < 10
          ? '0' + new Date().getSeconds()
          : new Date().getSeconds();
      if (week == 1) {
        this.nowWeek = '周一';
      } else if (week == 2) {
        this.nowWeek = '周二';
      } else if (week == 3) {
        this.nowWeek = '周三';
      } else if (week == 4) {
        this.nowWeek = '周四';
      } else if (week == 5) {
        this.nowWeek = '周五';
      } else if (week == 6) {
        this.nowWeek = '周六';
      } else {
        this.nowWeek = '周日';
      }
      _this.nowTime = hh + ':' + mf + ':' + ms;
      _this.nowDate = yy + '年' + mm + '月' + dd + '日';
    },
    // 添加body图片
    setBodyBackGround() {
      document.body.style.backgroundSize = '100%';
      document.body.style.backgroundImage = this.bodyBgImage;
    },
    // 清除背景图
    clearBodyBackGround() {
      document.body.style.backgroundImage = '';
    },

    //容量转换
    diskSize(num) {
      if (num == 0) return '0 B';
      var k = 1024; //设定基础容量大小
      var sizeStr = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']; //容量单位
      var i = 0; //单位下标和次幂
      for (var l = 0; l < 8; l++) {
        //因为只有8个单位所以循环八次
        if (num / Math.pow(k, l) < 1) {
          //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
          break; //小于1跳出循环
        }
        i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
      } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  ��以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
      return (num / Math.pow(k, i)).toFixed(2) + ' ' + sizeStr[i]; //循环结束 或 条件成立 返回字符
    },

    resizeWin2() {
      window.screenWidth = document.body.clientWidth;
      window.screenHeight = document.body.clientHeight;
      this.screenWidth = window.screenWidth;
      this.screenHeight = window.screenHeight;
      // console.log( this.screenWidth);
      //  console.log( this.screenHeight);
      let scalW = this.screenWidth / 1920;
      let scalH = this.screenHeight / 1080;
      // console.log(  this.scalseNum);
      if (this.screenWidth < 1300) {
        this.$refs.prompt.setAttribute('class', 'active');
      } else {
        this.$refs.prompt.removeAttribute('class', 'active');
      }

      if (scalW >= scalH) {
        this.scalseNum = scalH;
      } else {
        this.scalseNum = scalW;
      }
      //console.log(  this.scalseNum);
    },
    arrFormat(str) {
      // console.log(str);
      let arr = str.split(/[:,：，]/);
      //  console.log(arr)
      return arr;
    },
    getFloat(number) {
      console.log(number);
      let newNumber = (number.toFixed(4) * 10000) / 100 + '%';
      console.log(newNumber);
      return newNumber;
    },
  },
  mounted() {
    // 进来的时候调用添加
    this.getExpire();
    this.setBodyBackGround();
    this.currentTime();
    this.timerList();
    this.timerZstack();
    // this.huxingW();
    this.resizeWin2();
    const that = this;
    window.addEventListener('resize', this.resizeWin2);
    // var inputString = "总内存:3614MB,已使用内存:1746MB,cpu利用率:10.49%";
    // var mats = new Array();
    // var numbers = new Array();
    // var temp;
    // var elements = inputString.split(",");
    // for(var element in elements){
    //   console.log(element);
    //     temp = elements[element].split(":");
    //     console.log(temp);
    //     mats.push(temp[0]);
    //     numbers.push(parseInt(temp[1]));
    // }
    // console.log(mats); //
    // console.log(numbers); //
  },
  beforeDestroy() {
    // 离开页面的时候清除
    this.clearBodyBackGround();
    clearInterval(this.timer);
    this.timer = null;
    if (this.getDate) {
      //console.log("销毁定时器");
      clearInterval(this.getDate); // 在Vue实例销毁前，清除时间定时器
    }
    clearInterval(this.timerZ);
    this.timerZ == null;
    window.removeEventListener('resize', this.resizeWin2);
  },
};
</script>

<style lang="scss" scoped>
.box {
  height: 100%;
}

.mainWrapper {
  position: relative;
  flex: 1 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: linear-gradient(180deg, #030e43 0%, #00213a 100%);

  .wrap {
    flex: 1 1;
    position: relative;
    left: 50%;
    transform-origin: left top 0;
    width: 1920px;
  }

  .head {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    height: 80px;

    .title {
      font-weight: normal;
      margin-left: 60px;
      font-size: 40px;
      color: #fff;
      // white-space: pre;
      letter-spacing: 1.74px;
      /* line-height: 60px; */
      text-shadow: 0 0 6px #0089f3;

      .switch-btn {
        display: inline-block;
        width: 28px;
        height: 28px;
        margin-left: 20px;
        cursor: pointer;
      }
    }

    .datebox {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      border-radius: 2px;
      margin-right: 60px;

      .time {
        font-family: Roboto, PingFang SC, Noto Sans CJK, Microsoft YaHei;
        cursor: pointer;
        font-size: 48px;
        letter-spacing: 0;
        height: 48px;
        margin-right: 28px;
        color: rgba(82, 196, 255, 1);
      }

      .content {
        font-size: 16px;
        font-family: PingFangSC-Semibold;

        color: rgba(82, 196, 255, 1);

        .week {
          line-height: 24px;
        }

        .date {
          letter-spacing: 1px;

          line-height: 24px;
        }
      }
    }
  }

  .mainbox {
    margin: 20px 60px 0;

    .grid-content {
      // padding-bottom: 20px;
      position: relative;
      z-index: 10;
      box-sizing: border-box;

      .ip-box {
        display: flex;
        width: 60%;
        height: 46px;
        font-size: 18px;
        background: #010e50;
        opacity: 1;
        align-items: center;
        justify-content: center;
        border: 2px dotted #0091ff;
        color: #fff;
        margin: 40px auto 10px;

        position: relative;

        .right {
          position: absolute;
          top: 3px;
          right: 3px;
        }

        .left {
          position: absolute;
          bottom: 3px;
          left: 3px;
          transform: rotate(180deg);
        }
      }

      .chart-box {
        display: flex;
        align-items: center;

        .chart-box-item {
          flex: 1;
          height: 310px;
        }
      }

      .title {
        font-size: 20px;
        font-weight: 400;
        color: #52c4ff;

        margin-bottom: 20px;
      }

      .all-title {
        width: 100%;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: left;
        margin-bottom: 18px;

        .name {
          font-family: PingFangSC-Semibold;
          font-weight: bold;
          font-size: 24px;
          color: #0091ff;
          letter-spacing: 0;
          text-shadow: 0 0 6px rgba(0, 145, 255, 0.3);
        }

        .icon {
          margin-left: 8px;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: space-between;

          i {
            display: inline-block;
            margin: 0 3px;
            width: 8px;
            height: 12px;
            background-color: #0091ff;

            &:first-child {
              animation: icon_animation 1.2s linear 0s infinite;
            }

            &:nth-child(2) {
              animation: icon_animation 1.2s linear 0.4s infinite;
            }

            &:last-child {
              margin: 0 0 0 3px;
              background-color: #52c4ff;
            }

            &:nth-child(3) {
              animation: icon_animation 1.2s linear 0.8s infinite;
            }
          }
        }

        .line {
          height: 2px;
          flex-grow: 1;
          background: linear-gradient(90deg, #52c4ff 0, #0091ff 30%);
        }
      }

      .summary-box {
        display: flex;
        height: 182px;
        margin: 30px 0;
        background: url('../../../assets/security/summary-box-bg.png') no-repeat
          left center;
        background-size: 100% 100%;

        .summary-box-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-content: center;
          justify-content: center;
          color: #a8f6ff;

          &.left {
            justify-content: flex-start;
            margin-top: 40px;
          }

          .text {
            line-height: 22px;
            font-size: 16px;
            text-align: center;

            &.mb {
              margin-bottom: 16px;
            }

            span {
              font-size: 26px;
              color: #fff;
            }
          }
        }
      }
    }

    .grid-content-lg {
      height: 930px;

      .grid-content-lg-item {
        .grid-box {
          box-sizing: border-box;

          li {
            float: left;
            width: 50%;
            padding: 0 15px 16px 15px;
            .box-top {
              display: flex;
              align-items: center;
              width: 100%;
              margin-bottom: 6px;
              height: 22px;

              p {
                font-size: 12px;
                color: #b0d8f2;
                margin-left: 6px;
              }
            }
            .box-bd {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 46px;
              box-sizing: border-box;
              background: #010e50 url('../../../assets/security/border.png')
                no-repeat 164px 3px;
              border: 2px dotted #0091ff;
              position: relative;
              color: #fff;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding: 8px;

              &.time {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .grid-content-md {
      height: 400px;

      .nav-box {
        position: absolute;
        top: -20px;
        left: 10px;
      }

      .dot1-box {
        position: absolute;
        top: 146px;
        left: 386px;
        width: 54px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(32deg);
        transform: rotate(32deg);
      }

      .dot2-box {
        position: absolute;
        top: 250px;
        left: 204px;
        width: 54px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(32deg);
        transform: rotate(32deg);
      }

      .dot3-box {
        position: absolute;
        top: 290px;
        left: 596px;
        width: 56px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(32deg);
        transform: rotate(32deg);
      }

      .dot4-box {
        position: absolute;
        top: 390px;
        left: 418px;
        width: 56px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(32deg);
        transform: rotate(32deg);
      }

      .dot5-box {
        position: absolute;
        top: 363px;
        left: 140px;
        width: 58px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(150deg);
        transform: rotate(150deg);
      }

      .dot6-box {
        position: absolute;
        top: 428px;
        left: 234px;
        width: 58px;
        border: 1px dashed #00518d;
        -webkit-transform: rotate(150deg);
        transform: rotate(150deg);
      }

      .dot {
        position: absolute;
        top: -8px;
        left: -8px;
        transform: rotate(30deg);
        animation: 1.2s rolling3 linear infinite normal;
      }

      .middle-box {
        position: absolute;
        top: 90px;
        left: 160px;
      }

      .serve-box {
        position: absolute;
        top: 220px;
        left: 372px;
      }

      .person-box {
        position: absolute;
        top: 340px;
        left: 36px;
        transform: rotate(5deg);
      }
    }

    .title {
      margin-top: 10px;
    }

    .table-box {
      width: 100%;
      height: 900px;
      padding: 0;
      margin-top: 40px;

      .swiper-container {
        height: 900px;
      }
      .table-cell {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .cell {
          flex: 1;
        }

        .nickName {
          color: #8acaf1;
          display: inline-block;
          margin-right: 20px;
        }
        .content {
          color: #fff;
        }
      }
      .table-cell-bd {
        padding: 18px;
        border: 2px dotted #0091ff;
        background: #010e50;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        // background: linear-gradient(
        //   96.46deg,
        //   rgba(0, 91, 212, 0.5) 9.85%,
        //   rgba(0, 26, 61, 0.4) 88.33%
        // );
        position: relative;
        &::after {
          content: '';
          display: inline-block;
          width: 20px;
          height: 20px;
          background: url('../../../assets/security/border.png') no-repeat left
            center;
          position: absolute;
          right: 3px;
          top: 3px;
        }
        &.table-cell-space {
          &::after {
            background-image: none;
          }
          + .table-cell-bd {
            margin-left: 8px;
          }
        }
      }
    }
  }

  .grid-content-ms {
    height: 220px;
    box-sizing: border-box;
  }
}
</style>
<style scoped>
.el-table {
  overflow: hidden;
  background: none;
  border-spacing: 0;
}

.el-table::v-deep th,
.el-table::v-deep tr {
  background: none;
  color: #fff;
  font-size: 14px;
}

.el-table::v-deep th {
  background-color: none !important;
  color: #8acaf1;
}

.el-table::v-deep td,
.el-table::v-deep th.is-leaf {
  border-bottom: none;
  padding: 12px 0;
}

.el-table::v-deep tbody tr:hover > td {
  background: none !important;
}

.el-table::v-deep tbody tr:hover {
  background: none !important;
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}

.el-table::v-deep::before {
  height: 0;
}

.el-table::v-deep td .cell {
  height: 40px;
  line-height: 40px;
  background: rgba(0, 91, 212, 0.2);
}

.el-table::v-deep tr td:first-child {
  padding: 12px 8px;
}

.el-table::v-deep tr td:first-child .cell {
  background: linear-gradient(96deg, #005bd4 0%, #001a3d 100%);
}

.el-table::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 0;
}
</style>
