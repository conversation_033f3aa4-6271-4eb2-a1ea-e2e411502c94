:export {
  menuText: #4a4c4f;
  menuActiveText: #db2e43;
  subMenuActiveText: #db2e43;
  menuBg: #fff;
  menuHover: #fff;
  subMenuBg: #fff;
  subMenuHover: rgb(245, 247, 255);
  sideBarWidth: 239px;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type=file] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}
.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu .el-dropdown-menu__item {
  font-size: 14px;
}
.el-dropdown-menu a {
  display: block;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-carousel__button {
  background-color: #1989fa !important;
}

.el-carousel__indicators--outside button {
  background: #eee !important;
}

.el-table {
  overflow: auto;
  position: relative;
}

.el-table__header {
  width: 100% !important;
}

.el-table__body {
  width: 100% !important;
}

.el-table th,
.el-table td {
  font-size: 14px;
}

.el-table th {
  background: #fff;
}

.el-table .el-table__row:hover {
  background-color: #f3faff !important;
}

.el-input__inner {
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  border: 1px solid #dae0e6;
  border-radius: 2px;
}

.el-input__inner:focus {
  border-color: #db2e43;
}

.el-button {
  font-size: 14px;
  padding: 0 14px;
  height: 34px;
  line-height: 1.5;
  border-radius: 2px;
}

.el-table--border,
.el-table--group,
.el-table td,
.el-table th.is-leaf,
.el-table--border td,
.el-table--border th,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-color: #e6e6e6;
}

.el-form-item__label {
  font-size: 14px;
  font-weight: 400;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input__icon {
  line-height: 34px;
}

.el-table .cell {
  white-space: nowrap;
  line-height: 1.5;
}

.el-form-item__content,
.el-form-item__label {
  line-height: 34px;
}

.el-button--default {
  background: #e5e5e5;
  border: none;
  color: #4a4c4f;
}

.el-button--default:focus,
.el-button--default:hover {
  background-color: #dbdde0;
  color: #4a4c4f;
}

.el-button--primary:link {
  background-color: #db2e43;
  border-color: #db2e43;
}

.el-button--primary:focus,
.el-button--primary,
.el-button--primary:hover,
.el-button--primary.is-active,
.el-button--primary:active {
  color: #fff;
  background-color: #db2e43;
  border-color: #db2e43;
}

.el-button + .el-button {
  margin-left: 0;
}

.search-container {
  display: inline-block;
  font-size: 14px;
  height: 34px;
  border: 1px solid #dae0e6;
  border-radius: 2px;
  width: 230px;
  position: relative;
  box-sizing: border-box;
}
.search-container .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: none;
  outline: none;
}
.search-container .search-btn {
  width: 30px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  color: #999;
}

.el-pagination__sizes .el-input .el-input__inner {
  font-size: 14px;
}

.el-input--mini .el-input__inner {
  height: 34px;
  line-height: 34px;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  width: 34px;
  height: 34px;
  background: #fff;
  border: 1px solid #d7dce2;
  border-radius: 2px;
  padding: 8px;
  font-size: 14px;
  color: #db2e43;
}

.btn-prev .el-icon-arrow-left:before {
  content: "\e792";
}

.btn-next .el-icon-arrow-right:before {
  content: "\e791";
}

.el-pagination .btn-next .el-icon:hover,
.el-pagination .btn-prev .el-icon:hover {
  color: #db2e43;
}

.el-pager li {
  line-height: 34px;
}

.el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  margin-left: 10px;
}

.dialog-footer .el-button + .el-button {
  margin-left: 10px;
}

.el-dialog__body .el-form {
  padding-right: 30px;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #db2e43;
}

.el-tabs--border-card > .el-tabs__header {
  background: #fff;
  border: none;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__nav {
  margin-top: 2px;
}

.el-tabs--border-card .el-tabs__item {
  border: 1px solid #D9D9D9 !important;
  border-right: 0 !important;
}

.el-tabs--border-card .el-tabs__item:last-child {
  border-right: 1px solid #D9D9D9 !important;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #005EA4;
  background: rgb(218, 239, 255);
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item:first-child {
  margin-left: 0;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #005EA4;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 0;
}

.el-tooltip__popper.is-light {
  color: #fff;
  border: 1px solid #0090FE;
  background: rgba(0, 81, 141, 0.3);
}

.el-tooltip__popper[x-placement^=top].is-light .popper__arrow::after {
  border-top-color: #0090FE !important;
}

.el-tooltip__popper[x-placement^=top].is-light .popper__arrow {
  border-bottom-color: #0090FE !important;
}

.el-tooltip__popper[x-placement^=bottom].is-light .popper__arrow::after {
  border-bottom-color: #0090FE !important;
}

.el-tooltip__popper[x-placement^=bottom].is-light .popper__arrow {
  border-bottom-color: #0090FE !important;
}

.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  content: "";
  margin-right: 0;
}

.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::after {
  content: "*";
  color: #db2e43;
  margin-left: 4px;
}

.el-dialog__footer {
  border-top: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
}

.el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}

.el-popup-parent--hidden .fixed-header {
  padding-right: 0 !important;
}

.menudata-box .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 10px;
}

.menudata-box .avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.menudata-box .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 64px;
  height: 64px;
  line-height: 64px;
  text-align: center;
}

.menudata-box .avatar {
  width: 64px;
  height: 64px;
  display: block;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #fff2ea;
  color: #db2e43;
}

.el-switch.is-checked .el-switch__core {
  border-color: #db2e43;
  background-color: #db2e43;
}

#app .main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 239px;
  position: relative;
}
#app .sidebar-container {
  transition: width 0.28s;
  width: 239px !important;
  background-color: #fff;
  height: 100%;
  position: fixed;
  padding: 0 12px;
  font-size: 0px;
  top: 48px;
  bottom: 0;
  left: 0;
  z-index: 1001;
  border-right: 1px solid #eee;
  overflow: hidden;
}
#app .sidebar-container .change-nav {
  width: 239px;
  border-top: 1px solid #eee;
  padding: 12px 0;
  text-align: center;
  cursor: pointer;
  height: 40px;
  position: fixed;
  left: 0;
  bottom: 0;
}
#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}
#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}
#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}
#app .sidebar-container .el-scrollbar {
  height: 100%;
}
#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 88px);
}
#app .sidebar-container .is-horizontal {
  display: none;
}
#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}
#app .sidebar-container .svg-icon {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  color: #000;
}
#app .sidebar-container .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}
#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}
#app .sidebar-container .el-submenu__title {
  height: auto;
  padding: 6px 12px !important;
  margin: 12px 0 4px;
  line-height: 20px;
  font-size: 12px;
  color: #707275 !important;
}
#app .sidebar-container .el-submenu__title:hover {
  background: none !important;
  cursor: text;
}
#app .sidebar-container .submenu-title-noDropdown,
#app .sidebar-container .el-menu-item {
  font-size: 14px !important;
}
#app .sidebar-container .submenu-title-noDropdown:hover,
#app .sidebar-container .el-menu-item:hover {
  background-color: #fff !important;
}
#app .sidebar-container .is-active > .el-submenu__title {
  color: #707275 !important;
}
#app .sidebar-container .is-active > .svg-icon {
  color: #db2e43 !important;
}
#app .sidebar-container .nest-menu {
  margin-bottom: 4px;
}
#app .sidebar-container .nest-menu:last-child {
  margin-bottom: 0;
}
#app .sidebar-container .el-submenu .el-menu-item {
  padding: 0 0 0 12px !important;
  margin-top: 0;
  height: 32px;
  line-height: 32px;
  border-radius: 2px;
}
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title, #app .sidebar-container .el-submenu .el-menu-item {
  min-width: 239px !important;
  background-color: #fff !important;
}
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover, #app .sidebar-container .el-submenu .el-menu-item:hover {
  background-color: rgb(245, 247, 255) !important;
  color: #4a4c4f !important;
}
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title.is-active, #app .sidebar-container .el-submenu .el-menu-item.is-active {
  background-color: #fff2ea !important;
}
#app .hideSidebar .sidebar-container {
  width: 54px !important;
}
#app .hideSidebar .change-nav {
  width: 54px !important;
}
#app .hideSidebar .main-container {
  margin-left: 54px;
}
#app .hideSidebar .submenu-title-noDropdown {
  padding: 0 !important;
  position: relative;
  height: 32px;
  line-height: 32px;
  margin-bottom: 4px;
}
#app .hideSidebar .submenu-title-noDropdown.is-active .svg-icon {
  color: #db2e43 !important;
}
#app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 !important;
  text-align: center;
}
#app .hideSidebar .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin: 0;
}
#app .hideSidebar .submenu-title-noDropdown .el-tooltip .sub-el-icon {
  margin-left: 19px;
}
#app .hideSidebar .el-submenu {
  overflow: hidden;
}
#app .hideSidebar .el-submenu > .el-submenu__title {
  padding: 0 !important;
}
#app .hideSidebar .el-submenu > .el-submenu__title .svg-icon {
  margin-left: 20px;
}
#app .hideSidebar .el-submenu > .el-submenu__title .sub-el-icon {
  margin-left: 19px;
}
#app .hideSidebar .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}
#app .hideSidebar .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 239px !important;
}
#app .mobile .main-container {
  margin-left: 0px;
}
#app .mobile .sidebar-container {
  transition: transform 0.28s;
  width: 239px !important;
}
#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-239px, 0, 0);
}
#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}
.el-menu--vertical > .el-menu .sub-el-icon {
  margin-right: 12px;
  margin-left: -2px;
}
.el-menu--vertical .nest-menu .el-submenu > .el-submenu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: #fff !important;
}
.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

* {
  padding: 0;
  margin: 0;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif;
  font-size: 14px;
  overflow: hidden;
  background-color: #F1F6FA;
}

ul,
ol {
  list-style: none;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.navbar {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  background: #b71f40;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
.navbar .hamburger-container {
  line-height: 48px;
  height: 100%;
  float: left;
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;
}
.navbar .hamburger-container:hover {
  background: rgba(0, 0, 0, 0.025);
}
.navbar .breadcrumb-container {
  float: left;
}
.navbar .right-menu {
  float: right;
  height: 100%;
  line-height: 48px;
}
.navbar .right-menu:focus {
  outline: none;
}
.navbar .right-menu .safe {
  display: inline-block;
  padding: 0 8px;
  color: #fff;
  border-radius: 3px;
  line-height: 32px;
  margin-right: 5px;
  cursor: pointer;
}
.navbar .right-menu .safe:hover {
  background: rgba(255, 255, 255, 0.3);
}
.navbar .right-menu .safe .svg-container {
  display: inline-block;
  margin-right: 5px;
}
.navbar .right-menu .right-menu-item {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 14px;
  color: #fff;
  vertical-align: text-bottom;
}
.navbar .right-menu .right-menu-item.hover-effect {
  cursor: pointer;
  transition: background 0.3s;
}
.navbar .right-menu .right-menu-item.hover-effect:hover {
  background: rgba(0, 0, 0, 0.025);
}
.navbar .right-menu .avatar-container {
  margin-right: 30px;
  float: right;
  cursor: pointer;
}
.navbar .right-menu .avatar-container .avatar-wrapper {
  position: relative;
}
.navbar .right-menu .avatar-container .avatar-wrapper .user-avatar {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 10px;
}
.navbar .right-menu .avatar-container .avatar-wrapper .user-name {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 14px;
  color: #fff;
}
.navbar .right-menu .avatar-container .avatar-wrapper .el-icon-caret-bottom {
  cursor: pointer;
  position: absolute;
  right: -20px;
  top: 20px;
  font-size: 12px;
  color: #fff;
}

.app-container {
  padding: 20px;
}

.el-table td {
  padding: 12px 8px;
}

.el-table th {
  padding: 5px 8px;
  font-weight: normal;
}

.table-box2 .el-table th {
  padding: 12px 8px;
}

.mainWrapper {
  height: 100%;
  background: #fff;
}

.mainBox {
  background-color: #fff;
}
.mainBox .search-box {
  padding: 20px 30px;
  border-bottom: 1px solid #eef3f7;
}
.mainBox .search-box .filter-item {
  margin-right: 16px;
}
.mainBox .opt {
  display: inline-block;
  padding: 0 5px;
  color: #db2e43;
  cursor: pointer;
}
.mainBox .line {
  color: #409EFD;
}
.mainBox .serch-box {
  padding: 20px 30px;
  border-bottom: 1px solid #eef3f7;
}

.scrollbar-wrapper {
  overflow-x: hidden !important;
}

.el-table thead {
  color: #5e6978;
  font-weight: 400 !important;
}

.header {
  padding: 20px 24px;
  background: #fff;
  box-shadow: 0 1px 4px 0 rgba(218, 224, 230, 0.4);
}
.header h3 {
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
}
.header h3 .back-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  border: 1px solid #DAE0E6;
  margin-right: 10px;
  color: #00437d;
  cursor: pointer;
}

.el-tabs__header {
  margin-bottom: 0;
}

.el-tabs__nav-wrap {
  padding: 0 30px;
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #eef3f7;
}

.table-box {
  padding: 0 24px;
}
.table-box .el-dropdown-link {
  display: inline-block;
  background: #F0F2F5;
  border-radius: 3px 3px 3px 3px;
  padding: 0 10px;
  cursor: pointer;
}

.filter-container {
  float: left;
}

.page-box {
  float: right;
}

.el-dropdown-menu .opt {
  padding: 5px 10px;
  color: #db2e43;
  cursor: pointer;
  text-align: center;
}

.el-select-dropdown__item.selected {
  color: #db2e43;
}

.el-select .el-input.is-focus .el-input__inner,
.el-pagination__sizes .el-input .el-input__inner:hover {
  border-color: #db2e43;
}

.pageNum {
  display: inline-block;
  height: 34px !important;
  line-height: 34px !important;
  text-align: center;
  font-size: 14px !important;
  font-weight: 400;
}
.pageNum .divider {
  display: inline-block;
  margin: 0 2px;
  font-style: normal;
}

.form-box {
  padding: 20px 30px;
}
.form-box .form-box-hd {
  background: #fff;
  border-radius: 3px;
  padding: 20px;
}
.form-box .form-box-hd .form-box-left {
  width: 100px;
  float: left;
}
.form-box .form-box-hd .form-box-left .text {
  font-size: 16px;
  font-weight: 500;
  color: #1a2736;
}
.form-box .form-box-hd .form-box-right {
  padding-left: 100px;
}

.forminputWidth {
  width: 300px;
}

.form-foot {
  width: 100%;
  text-align: right;
  position: absolute;
  height: 90px;
  line-height: 90px;
  padding: 0 30px;
  background: #fafdff;
  box-shadow: 0px -1px 2px 0px rgba(218, 224, 230, 0.7);
}

.elTableBar .el-scrollbar {
  height: 100%;
}

.auth-img {
  display: inline-block;
  position: relative;
  top: 5px;
  margin-right: 10px;
}

.copy-img {
  position: absolute;
  top: 10px;
  right: 0;
  cursor: pointer;
}

.mr_0 {
  margin-right: 0 !important;
}

.mr_20 {
  margin-right: 20px;
}

.expire {
  padding: 10px 30px;
}

.tips-img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.expire-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.expire-tip {
  font-size: 12px;
  line-height: 24px;
}

.password-strength {
  display: flex;
  margin-bottom: 8px;
}
.password-strength li {
  flex: 1;
  padding-right: 5px;
}
.password-strength li span {
  display: block;
  height: 10px;
  background: #eee;
  line-height: 20px;
}
.password-strength li p {
  text-align: center;
}
.password-strength li:nth-child(1) span {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-right: 0px solid;
}
.password-strength li:nth-child(1) p {
  color: red;
}
.password-strength li:nth-child(2) span {
  border-left: 0px solid;
  border-right: 0px solid;
}
.password-strength li:nth-child(2) p {
  color: orange;
}
.password-strength li:nth-child(3) span {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-left: 0px solid;
}
.password-strength li:nth-child(3) p {
  color: #00d1b2;
}

#prompt {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  background: #000;
  text-align: center;
  color: white;
  padding-top: 25%;
  font-size: 20px;
  font-weight: bold;
}
#prompt.active {
  display: block;
}

@keyframes icon_animation {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes rolling {
  0% {
    left: -8px;
  }
  50% {
    left: 88px;
  }
  100% {
    left: 152px;
  }
}
@keyframes rolling2 {
  0% {
    left: -8px;
  }
  50% {
    left: 18px;
  }
  100% {
    left: 38px;
  }
}
@keyframes rolling3 {
  0% {
    left: -8px;
  }
  50% {
    left: 18px;
  }
  100% {
    left: 58px;
  }
}
@keyframes rolling4 {
  0% {
    left: -8px;
  }
  50% {
    left: 58px;
  }
  100% {
    left: 88px;
  }
}
.el-tooltip__popper {
  max-width: 60% !important;
}

.el-table::before {
  height: 0;
}
.el-table .doc-content p,
.el-table .doc-content b,
.el-table .doc-content h1,
.el-table .doc-content h2,
.el-table .doc-content h3,
.el-table .doc-content h4,
.el-table .doc-content h5,
.el-table .doc-content h6,
.el-table .doc-content i,
.el-table .doc-content font {
  font-size: 14px !important;
  font-weight: 400 !important;
  padding: 0 !important;
  font-style: normal !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 23px !important;
  color: #333 !important;
}

.el-tabs__item {
  box-shadow: none !important;
}
.el-tabs__item.is-active {
  color: #db2e43;
}
.el-tabs__item:hover {
  color: #db2e43;
}

.el-tabs__active-bar {
  background-color: #db2e43;
}

.ukey-dialog .el-dialog__body {
  padding: 0;
}

.tp-wrap {
  position: relative;
  width: 1246px;
}
.tp-wrap > div {
  display: inline-block;
}
.tp-wrap > div span {
  display: inline-block;
}
.tp-wrap > div span:focus {
  outline: none;
}
.tp-wrap > div.hx-wrap img {
  width: 160px;
}
.tp-wrap > div img.disabled {
  cursor: not-allowed;
}
.tp-wrap > div img:focus {
  outline: none;
}
.tp-wrap .img-box > img {
  cursor: pointer;
  width: 160px;
}
.tp-wrap .f-wrap {
  position: absolute;
  left: 60px;
  top: 280px;
}
.tp-wrap .x-wrap {
  position: absolute;
  left: 180px;
  top: 164px;
}
.tp-wrap .hx-wrap {
  position: absolute;
  left: 386px;
  top: 86px;
}
.tp-wrap .cloud-wrap {
  position: absolute;
  left: 560px;
  top: -10px;
}
.tp-wrap .firewall-wrap {
  position: absolute;
  left: 726px;
  top: 106px;
}
.tp-wrap .waf-wrap {
  position: absolute;
  left: 912px;
  top: 226px;
}
.tp-wrap .logAudit-wrap {
  position: absolute;
  left: 1066px;
  top: 316px;
}
.tp-wrap .database-wrap {
  position: absolute;
  left: 890px;
  top: 432px;
}
.tp-wrap .host-wrap {
  position: absolute;
  left: 732px;
  top: 560px;
}
.tp-wrap .hostAll-wrap {
  position: absolute;
  left: 402px;
  top: 162px;
}
.tp-wrap .hostAll-wrap img {
  width: 506px;
  height: 449px;
}
.tp-wrap .vpn-wrap {
  position: absolute;
  left: 550px;
  top: 657px;
}
.tp-wrap .fortress-wrap {
  position: absolute;
  left: 292px;
  top: 512px;
}
.tp-wrap .dot {
  position: absolute;
  top: -8px;
  left: -8px;
  transform: rotate(30deg);
  animation: 1.2s rolling_1 linear infinite normal;
}
.tp-wrap .net-dot1-box {
  position: absolute;
  top: 222px;
  left: 291px;
  width: 120px;
  transform: rotate(-32deg);
}
.tp-wrap .net-dot1-box .dot {
  animation: 1.2s rolling_1 linear infinite normal;
}
.tp-wrap .net-dot2-box {
  position: absolute;
  top: 118px;
  left: 495px;
  width: 78px;
  transform: rotate(-30deg);
}
.tp-wrap .net-dot2-box .dot {
  animation: 1.2s rolling_2 linear infinite normal;
}
.tp-wrap .net-dot3-box {
  position: absolute;
  top: 107px;
  left: 694px;
  width: 78px;
  transform: rotate(32deg);
}
.tp-wrap .net-dot3-box .dot {
  animation: 1.2s rolling_3 linear infinite normal;
}
.tp-wrap .net-dot4-box {
  position: absolute;
  top: 226px;
  left: 867px;
  width: 110px;
  transform: rotate(32deg);
}
.tp-wrap .net-dot4-box .dot {
  animation: 1.2s rolling_4 linear infinite normal;
}
.tp-wrap .net-dot5-box {
  position: absolute;
  top: 333px;
  left: 1043px;
  width: 104px;
  transform: rotate(34deg);
}
.tp-wrap .net-dot5-box .dot {
  animation: 1.2s rolling_4 linear infinite normal;
}
.tp-wrap .net-dot6-box {
  position: absolute;
  top: 470px;
  left: 1016px;
  width: 104px;
  transform: rotate(150deg);
}
.tp-wrap .net-dot6-box .dot {
  animation: 1.2s rolling_5 linear infinite normal;
}
.tp-wrap .net-dot7-box {
  position: absolute;
  top: 592px;
  left: 848px;
  width: 94px;
  transform: rotate(145deg);
}
.tp-wrap .net-dot7-box .dot {
  animation: 1.2s rolling_4 linear infinite normal;
}
.tp-wrap .net-dot8-box {
  position: absolute;
  top: 697px;
  left: 653px;
  width: 80px;
  transform: rotate(-26deg);
}
.tp-wrap .net-dot8-box .dot {
  animation: 1.2s rolling_4 linear infinite normal;
}
.tp-wrap .net-dot9-box {
  position: absolute;
  top: 546px;
  left: 408px;
  width: 96px;
  transform: rotate(-32deg);
}
.tp-wrap .net-dot9-box .dot {
  animation: 1.2s rolling_4 linear infinite normal;
}
.tp-wrap .net-dot10-box {
  position: absolute;
  top: 344px;
  left: 352px;
  width: 106px;
  transform: rotate(36deg);
}
.tp-wrap .net-dot10-box .dot {
  transform: rotate(-150deg);
  animation: 1.2s rolling_6 linear infinite normal;
}
.tp-wrap .net-dot11-box {
  position: absolute;
  top: 212px;
  left: 502px;
  width: 106px;
  transform: rotate(34deg);
}
.tp-wrap .net-dot11-box .dot {
  transform: rotate(-150deg);
  animation: 1.2s rolling_6 linear infinite normal;
}
.tp-wrap .net-dot12-box {
  position: absolute;
  top: 458px;
  left: 872px;
  width: 86px;
  transform: rotate(30deg);
}
.tp-wrap .net-dot12-box .dot {
  transform: rotate(-150deg);
  animation: 1.2s rolling_7 linear infinite normal;
}
.tp-wrap .net-dot13-box {
  position: absolute;
  top: 583px;
  left: 727px;
  width: 86px;
  transform: rotate(32deg);
}
.tp-wrap .net-dot13-box .dot {
  transform: rotate(-150deg);
  animation: 1.2s rolling_7 linear infinite normal;
}

@keyframes rolling_1 {
  0% {
    left: -8px;
  }
  50% {
    left: 58px;
  }
  100% {
    left: 100px;
  }
}
@keyframes rolling_2 {
  0% {
    left: -8px;
  }
  50% {
    left: 38px;
  }
  100% {
    left: 62px;
  }
}
@keyframes rolling_3 {
  0% {
    left: -8px;
  }
  50% {
    left: 25px;
  }
  100% {
    left: 42px;
  }
}
@keyframes rolling_4 {
  0% {
    left: -8px;
  }
  50% {
    left: 42px;
  }
  100% {
    left: 76px;
  }
}
@keyframes rolling_5 {
  0% {
    left: -8px;
  }
  50% {
    left: 49px;
  }
  100% {
    left: 90px;
  }
}
@keyframes rolling_6 {
  0% {
    left: 86px;
  }
  50% {
    left: 27px;
  }
  100% {
    left: -28px;
  }
}
@keyframes rolling_7 {
  0% {
    left: 36px;
  }
  50% {
    left: 0px;
  }
  100% {
    left: -36px;
  }
}
.net-popover {
  background-color: rgb(161, 188, 208);
  color: #fff;
  border-color: #3281C4;
}
.net-popover[x-placement^=right] .popper__arrow::after {
  border-right-color: #3281C4 !important;
}
.net-popover[x-placement^=top] .popper__arrow::after {
  border-top-color: #3281C4 !important;
}
.net-popover[x-placement^=left] .popper__arrow::after {
  border-left-color: #3281C4 !important;
}
.net-popover[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #3281C4 !important;
}

.color_red {
  color: red;
}

.color_blue {
  color: #409EFF;
}/*# sourceMappingURL=index.css.map */