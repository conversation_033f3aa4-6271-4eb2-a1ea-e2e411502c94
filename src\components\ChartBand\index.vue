<template>
  <div :id="id" :style="style"></div>
</template>

<script>
import { diskSize } from '@/utils/calculate';
export default {
  name: 'ChartBand',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.init();
    });
    window.addEventListener('resize', this.chart.resize);
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      this.$nextTick(() => {
        this.setOption();
        // console.log(this.chartData)
      });
    },
    setOption() {
      const that = this;
      let option = {};
      option = {
        color: ['#35AA47', '#005DFF', '#E5DE62'],
        grid: {
          top: '20px',
          left: '0',
          right: '20px',
          bottom: '20px',
          containLabel: true,
        },
        legend: {
          bottom: 0,
          textStyle: {
            color: '#DEF3FF',
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var result = '';
            // console.log(params);
            result +=
              moment(Number(params[0].axisValueLabel)).format('YYYY-MM-DD HH:mm:ss') +
              '</br>';
            params.forEach(function (item) {
              // console.log(item);
              result +=
                item.marker +
                ' ' +
                item.seriesName +
                ' : ' +
                diskSize(item.value) +
                '/s' +
                '</br>';
            });
            return result;
          },
        },

        xAxis: {
          type: 'category',
          data: that.flowTime,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            color: '#CBEDFF',
            fontSize: 14,
            formatter: function (value, index) {
              // console.log(value);
              let time;
              time = moment(Number(value)).format('MM-DD');
              return time;
            },
          }, // x轴字体颜色

          axisLine: {
            show: true,

            lineStyle: {
              color: '#003660', // x轴坐标轴颜色
            },
          },

          axisTick: {
            show: true,

            lineStyle: {
              color: '#DEF3FF',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#CBEDFF',
            fontSize: 14,
            formatter: function (value, index) {
              //console.log(value);
              let capacity;
              capacity = diskSize(value);
              return capacity + '/s';
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid', //设置网格线类型 dotted：虚线   solid:实线
              color: '#003660',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: that.chartData.flowInner,
            symbol: 'none',
            name: '入站流量',
            type: 'line',

            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: ' rgba(37, 161, 255, 0.07)' },

                  { offset: 1, color: ' rgba(37, 161, 255, 0)' },
                ]),
              },
            },
          },
          {
            data: that.chartData.flowOut,
            symbol: 'none',
            name: '出站流量',
            symbol: 'circle',
            type: 'line',
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: ' rgba(0, 153, 255, 0.97)' },

                  { offset: 1, color: ' rgba(0, 93, 255, 0.29)' },
                ]),
              },
            },
          },
          {
            data: that.chartData.flowTotal,
            symbol: 'none',
            name: '总流量',
            type: 'line',
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: ' rgba(255, 239, 151, 0.19 )' },

                  { offset: 1, color: ' rgba(255, 239, 151, 0.2)' },
                ]),
              },
            },
          },
        ],
      };

      this.chart.setOption(option, true);
    },
  },
};
</script>
