<template>
  <el-dialog
    width="520px"
    :visible.sync="visible"
    :title="!dataForm.id ? '设备添加' : '设备修改'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="120px"
      @keyup.enter.native="dataFormSubmitHandle()"
    >
      <el-form-item label="主机地址" prop="address">
        <el-input v-model="dataForm.address" />
      </el-form-item>
      <el-form-item label="系统类型" prop="osSystem">
        <el-select
          v-model="dataForm.osSystem"
          placeholder="请选择"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in systemList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="SNMP版本" prop="version">
        <el-select
          v-model="dataForm.version"
          placeholder="SNMP版本"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in versionList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="读团体字" prop="community">
        <el-input v-model="dataForm.community" />
      </el-form-item>
      <el-form-item label="端口号" prop="snmpPort">
        <el-input v-model="dataForm.snmpPort" />
      </el-form-item>
      <el-form-item label="检测方式" prop="checkType">
        <el-select
          v-model="dataForm.checkType"
          placeholder="请选择"
          clearable
          style="width: 64%"
        >
          <el-option
            v-for="item in checkTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" @click="testHandle()">测试</el-button>
        <span
          class="testIcon"
          :class="
            onlineState === 1
              ? 'el-icon-circle-check color-blue'
              : onlineState === 0
              ? 'el-icon-circle-close color-red'
              : ''
          "
        ></span>
      </el-form-item>
      <el-form-item label="主机名称" prop="hostName">
        <el-input v-model="dataForm.hostName" />
      </el-form-item>
      <!-- <el-form-item :label="$t('app.description')" prop="description">
        <el-input v-model="dataForm.description" type="textarea" :placeholder="" />
      </el-form-item> -->
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()"
        >确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
import { detect, modifySnmpDevice, getDeviceInfo } from '@/api/modules/monitor';
var validNumberPass = (rule, value, callback) => {
  //正整数
  let reg = /^[+]{0,1}(\d+)$/g;
  if (value === '') {
    callback(new Error('端口号不能为空'));
  } else if (!reg.test(value)) {
    callback(new Error('请输入0及0以上的整数'));
  } else {
    callback();
  }
};

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: '', //主机id
        address: '', //主机地址
        hostName: '', //主机名称
        osSystem: '', //系统类型
        version: '', //SNMP版本
        community: '', //读团体字
        snmpPort: '', //端口号
        userId: '',
        userName: '',
        checkType: '', //检测方式
      },
      onlineState: '',
      systemList: [
        {
          label: 'windows',
          value: '0',
        },
        {
          label: 'linux',
          value: '1',
        },
      ],
      versionList: [
        {
          label: 'V1',
          value: 0,
        },
        {
          label: 'V2c',
          value: 1,
        },
        // {
        //   label: 'V3',
        //   value: 2,
        // },
      ],
      checkTypeList: [
        {
          label: 'ping and snmp',
          value: 1,
        },
        {
          label: 'ping',
          value: 2,
        },
        {
          label: 'snmp',
          value: 3,
        },
      ],
    };
  },
  computed: {
    ...mapGetters(['userid', 'usertype', 'name']),
    dataRule() {
      return {
        address: [{ required: true, message: '主机地址不能为空', trigger: 'blur' }],
        version: [{ required: true, message: 'SNMP版本不能为空', trigger: 'change' }],
        osSystem: [{ required: true, message: '系统类型不能为空', trigger: 'blur' }],
        community: [{ required: true, message: '读团体字不能为空', trigger: 'blur' }],
        snmpPort: [{ required: true, validator: validNumberPass, trigger: 'blur' }],
        hostName: [{ required: true, message: '主机名称不能为空', trigger: 'blur' }],
      };
    },
  },

  created() { },
  mounted() {
    this.dataForm.userId = this.userid;
    this.dataForm.userName = this.name;
  },
  methods: {
    init() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields();
        this.onlineState = '';
        Promise.all([]).then(() => {
          console.log(this.dataForm.id);
          if (this.dataForm.id) {
            this.getInfo();
          }
        });
      });
    },
    //snmp 测试
    testHandle() {
      let data = {
        ip: this.dataForm.address,
        version: this.dataForm.version,
        community: this.dataForm.community,
        port: Number(this.dataForm.snmpPort),
        checkType: this.dataForm.checkType,
      };
      console.log(data);
      detect(data)
        .then((res) => {
          console.log(res);
          if (res.code !== 1) {
            return this.$message.error(res.msg);
          }
          if (res.data.deviceName == '' || res.data.deviceName == undefined) {
            this.dataForm.hostName = this.dataForm.address;
          } else {
            this.dataForm.hostName = res.data.deviceName;
          }

          this.onlineState = res.data.state;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 获取信息
    getInfo() {
      let data = {
        id: this.dataForm.id,
      };
      getDeviceInfo(data)
        .then((res) => {
          console.log(res);
          if (res.code !== 1) {
            return this.$message.error(res.msg);
          }
          this.dataForm = {
            ...this.dataForm,
            ...res.data,
          };
          // console.log(this.dataForm);
        })
        .catch(() => { });
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false;
          }
          // this.dataForm.snmpPort = Number(this.dataForm.snmpPort);
          console.log(this.dataForm);
          let data = {
            id: this.dataForm.id, //主机id
            address: this.dataForm.address, //主机地址
            hostName: this.dataForm.hostName, //主机名称
            osSystem: this.dataForm.osSystem, //系统类型
            version: this.dataForm.version, //SNMP版本
            community: this.dataForm.community, //读团体字
            snmpPort: Number(this.dataForm.snmpPort), //端口号
            checkType: this.dataForm.checkType, //检测方式
            userId: this.dataForm.userId,
            userName: this.dataForm.userName,
          };
          modifySnmpDevice(data)
            .then((res) => {
              if (res.code !== 1) {
                return this.$message.error(res.msg);
              }
              this.$message({
                message: !this.dataForm.id ? '添加成功' : '修改成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false;
                  this.$emit('refreshDataList');
                },
              });
            })
            .catch(() => { });
        });
      },
      1000,
      { leading: true, trailing: false }
    ),
  },
};
</script>
<style lang="scss" scoped>
.color-blue {
  color: #108ff4;
}
.color-red {
  color: #f95b6c;
}
.testIcon {
  display: inline-block;
  font-size: 24px;
  line-height: 38px;
  margin-left: 5px;
}
</style>
