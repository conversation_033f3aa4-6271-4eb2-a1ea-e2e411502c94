import request from '@/utils/request'

//镜像列表接口
export function fetchList(data) {
  return request({
    url: '/api/mirror/getMirrorList',
    method: 'post',
    params: data
  })
}
//镜像添加-修改

export function addMirror(data) {
  return request({
    url: '/api/mirror/addOrUpdateByMirror',
    method: 'post',
    params: data
  })
}
//镜像删除

export function delMirror(id) {
  return request({
    url: '/api/mirror/deleteMirrorById',
    method: 'post',
    params: {id:id}
  })
}

