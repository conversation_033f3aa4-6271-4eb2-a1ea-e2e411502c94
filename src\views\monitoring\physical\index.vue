<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="physical-box1">
      <el-row :gutter="15">
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <div id="ramChart" :style="{width: '120px', height: '120px'}"></div>
            <div class="grid-content-info">
              <h3>CPU使用率</h3>
              <p class="percent">20%</p>
              <p class="status">已使用</p>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <div id="cpuChart" :style="{width: '120px', height: '120px'}"></div>
            <div class="grid-content-info">
              <h3>内存使用率</h3>
              <p class="percent">40%</p>
              <p class="status">已使用</p>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <div class="grid-content">
            <div id="diskChart" :style="{width: '120px', height: '120px'}"></div>
            <div class="grid-content-info">
              <h3>磁盘使用率</h3>
              <p class="percent">71.43%</p>
              <p class="status">已使用</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="physical-box2">
      <div class="container">
        <div id="flowChart" :style="{width: '98%', height: '320px'}"></div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {
    this.initCharts();
  },
  methods: {
    initCharts() {
      this.chart = this.$echarts.init(document.getElementById("cpuChart"));
      this.chart_ram = this.$echarts.init(document.getElementById("ramChart"));
      this.chart_disk = this.$echarts.init(
        document.getElementById("diskChart")
      );
      this.chart_flow = this.$echarts.init(
        document.getElementById("flowChart")
      );
      this.setOptions();
    },
    setOptions() {
      // 指定图表的配置项和数据
      var colorObj = { min: "#01AAED", safe: "#5FB878", max: "#FF5722" }; //颜色对应以上区间值定义
      var maxVal = 100; //圆环极大值
      var valObj = { min: 30, max: 80 }; //自定义区间值
      //var percent = (Math.random() * 100).toFixed(2) - 0; //获取100以内的随机模拟数据
      var percent = [10, 60, 96]; //获取100以内的随机模拟数据
      function changecolor(percent) {
        var color;
        if (percent < valObj.min) {
          color = colorObj.min;
        } else if (percent >= valObj.min && percent <= valObj.max) {
          color = colorObj.safe;
        } else {
          color = colorObj.max;
        }
        return color;
      }
      var colors = ["#5793f3", "#d14a61", "#675bba"]; //实时流量颜色设置
      this.chart.setOption({
        tooltip: {
          //提示框浮层属性
          show: true,
          transitionDuration: 0.8,
          formatter: "{c}%" //提示框浮层内容格式器，支持字符串模板和回调函数两种形式
        },
        series: [
          {
            name: "CPU使用率",
            type: "pie", //饼图类型
            radius: ["70%", "80%"], //饼图的内、外半径
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: "rgba(255,255,255,0.3)"
              }
            },
            data: [
              {
                //系列中的数据内容数组
                value: 60,
                name: "",
                //该数据项是否被选中
                selected: false,
                // 单个扇区的标签配置
                label: {
                  normal: {
                    // 是显示标签
                    show: true,
                    position: "center",
                    fontSize: 14,
                    color: "#fff",
                    // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行
                    formatter: "{b}\n{d}%"
                  }
                },

                itemStyle: {
                  normal: {
                    color: changecolor(percent[0])
                  }
                }
              },
              {
                value: maxVal - percent[0]
              }
            ],
            animationEasingUpdate: "cubicInOut",
            animationDurationUpdate: 1000
          }
        ]
      });
      this.chart_ram.setOption({
        tooltip: {
          //提示框浮层属性
          show: true,
          transitionDuration: 0.8,
          formatter: "{c}%" //提示框浮层内容格式器，支持字符串模板和回调函数两种形式
        },
        series: [
          {
            name: "内存使用率",
            type: "pie", //饼图类型
            radius: ["70%", "80%"], //饼图的内、外半径
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: "rgba(255,255,255,0.3)"
              }
            },
            data: [
              {
                //系列中的数据内容数组
                value: percent,
                name: "",
                //该数据项是否被选中
                selected: false,
                // 单个扇区的标签配置
                label: {
                  normal: {
                    // 是显示标签
                    show: true,
                    position: "center",
                    fontSize: 14,
                    color: "#fff",
                    // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行
                    formatter: "{b}\n{d}%"
                  }
                },

                itemStyle: {
                  normal: {
                    color: changecolor(percent[1])
                  }
                }
              },
              {
                value: maxVal - percent[1]
              }
            ],
            animationEasingUpdate: "cubicInOut",
            animationDurationUpdate: 1000
          }
        ]
      });
      this.chart_disk.setOption({
        tooltip: {
          //提示框浮层属性
          show: true,
          transitionDuration: 0.8,
          formatter: "{c}%" //提示框浮层内容格式器，支持字符串模板和回调函数两种形式
        },
        series: [
          {
            name: "内存使用率",
            type: "pie", //饼图类型
            radius: ["70%", "80%"], //饼图的内、外半径
            hoverAnimation: false,
            label: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: "rgba(255,255,255,0.3)"
              }
            },
            data: [
              {
                //系列中的数据内容数组
                value: percent,
                name: "",
                //该数据项是否被选中
                selected: false,
                // 单个扇区的标签配置
                label: {
                  normal: {
                    // 是显示标签
                    show: true,
                    position: "center",
                    fontSize: 14,
                    color: "#fff",
                    // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行
                    formatter: "{b}\n{d}%"
                  }
                },

                itemStyle: {
                  normal: {
                    color: changecolor(percent[2])
                  }
                }
              },
              {
                value: maxVal - percent[2]
              }
            ],
            animationEasingUpdate: "cubicInOut",
            animationDurationUpdate: 1000
          }
        ]
      });
      this.chart_flow.setOption({
        color: colors,
        title: {
          text: "实时流量监控",
          x: "center"
        },
        tooltip: {
          trigger: "none",
          axisPointer: {
            type: "cross"
          }
        },
        legend: {
          data: ["inbound", "outbound"],
          bottom: 0
        },
        grid: {
          top: 70,
          bottom: 50
        },
        xAxis: {
          type: "category",
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {
            onZero: false,
            lineStyle: {
              color: colors[1]
            }
          },
          axisPointer: {
            label: {
              formatter: function(params) {
                return (
                  "inbound  " +
                  params.value +
                  (params.seriesData.length
                    ? "：" + params.seriesData[0].data
                    : "")
                );
              }
            }
          },
          data: [
            "2020-7-1 10:00",
            "2020-7-1 11:00",
            "2020-7-1 12:00",
            "2020-7-1 13:00",
            "2020-7-1 14:00",
            "2020-7-1 15:00",
            "2020-7-1 16:00"
          ]
        },
        yAxis: [
          {
            type: "value"
          }
        ],
        series: [
          {
            name: "inbound",
            type: "line",
            smooth: true,
            data: [
              2.6,
              5.9,
              9.0,
              26.4,
              28.7,
              70.7,
              175.6,
              182.2,
              48.7,
              18.8,
              6.0,
              2.3
            ]
          },
          {
            name: "outbound",
            type: "line",
            smooth: true,
            data: [
              3.9,
              5.9,
              11.1,
              18.7,
              48.3,
              69.2,
              231.6,
              46.6,
              55.4,
              18.4,
              10.3,
              0.7
            ]
          }
        ]
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 60px);
}
.physical-box1 {
  padding: 20px 30px;
  margin-bottom: 10px;
  background-color: #4f535b;
  .grid-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 160px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    color: #fff;
    .grid-content-info {
      margin: 0 20px 0 50px;
      h3 {
        font-weight: normal;
      }
      .percent {
        margin: 5px 0;
      }
      .status {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
.physical-box2 {
  padding: 10px;
  .container {
    padding: 20px 10px;
    background: #fff;
    border-radius: 3px;
    height: 360px;
  }
}
</style>

