<template>
  <div :id="id" :style="style"></div>
</template>

<script>
export default {
  name: 'BarVertical',
  props: {
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    chartData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      chart: '',
    };
  },
  computed: {
    style() {
      return {
        width: this.width,
        height: this.height,
      };
    },
  },
  watch: {
    chartData: {
      handler(newVal, oldVal) {
        if (this.chart) {
          this.chartData = newVal;
          this.$nextTick(() => {
            this.init();
          });
        } else {
          this.init();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.charts) {
        // 先销毁，释放内存
        this.charts.dispose();
      }
      this.init();
    });
  },

  beforeDestroy() {
    // 解除监听
    window.removeEventListener('resize', this.chart.resize);
    // 销毁 echart实例
    if (this.charts) {
      this.charts.dispose();
    }
  },

  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById(this.id));
      this.$nextTick(() => {
        this.setOption();
        // console.log(this.chartData)
      });
      window.addEventListener('resize', this.chart.resize);
    },
    setOption() {
      const that = this;
      let option = {};
      option = {
        grid: {
          top: '20px',
          left: '20px',
          right: '20px',
          bottom: '20px',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter: function (params) {
            var result = '';
            // console.log(params);
            // params.forEach(function (item) {
            // });
            result +=
              params[0].axisValueLabel +
              '</br>' +
              "<span style='display:inline-block;width:10px;height:10px;border-radius:50%;background-color:#0042A4'></span>" +
              ' ' +
              params[0].seriesName +
              ' : ' +
              params[0].data +
              '</br>';

            return result;
          },
        },

        xAxis: {
          type: 'category',
          data: that.chartData.xAxisData,
          splitLine: {
            show: false,
          }, //去除网格线
          axisLabel: {
            fontSize: 12,
            color: '#fff',
            interval: 0,
            rotate: -45,
            formatter: function (value) {
              if (value.length > 8) {
                return `${value.slice(0, 6)}...`;
              }
              return value;
            },
          }, // x轴字体颜色

          axisLine: {
            show: false, // x轴坐标轴颜色
          },

          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          nameTextStyle: {
            fontSize: 12,
            align: 'right',
          },
          axisLabel: {
            color: '#52C4FF',
            fontSize: 14,
          },
          smooth: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid', //设置网格线类型 dotted：虚线   solid:实线
              color: '#003660',
            },
          },
          axisTick: {
            //y轴刻度线
            show: false,
          },
          axisLine: {
            //y轴
            show: false,
          },
        },
        series: [
          {
            data: that.chartData.yAxisData,
            symbol: 'none',
            type: 'bar',
            name: '并发连接',
            barWidth: 15, //柱图宽度
            label: {
              show: true,
              position: 'top',
              color: '#fff',
            },
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: '#005EA4', // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: 'rgba(26,112,178,1)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#0091FF', // 100% 处的颜色
                    },
                  ],
                  false
                ),
              },
              // normal: {
              //   //柱体的颜色
              //   //右，下，左，上（1，0，0，0）表示从正右开始向左渐变
              //   color: function (params) {
              //     console.log(params);
              //     var colorList = [
              //       ["rgba(236, 89, 96, 0.6)", "rgba(236, 89, 96, 1)"],
              //       ["rgba(255, 180, 18, 0.6)", "rgba(255, 180, 18, 1)"],
              //       ["rgba(21, 221, 170, 0.6)", "rgba(21, 221, 170, 1)"],
              //     ];
              //     var dataIndex = params.dataIndex;
              //     if (dataIndex <= 1) {
              //       return new that.$echarts.graphic.LinearGradient(
              //         0,
              //         0,
              //         0,
              //         1,
              //         [
              //           {
              //             offset: 0,
              //             color: colorList[0][0],
              //           },
              //           {
              //             offset: 1,
              //             color: colorList[0][1],
              //           },
              //         ],
              //         false
              //       );
              //     } else if (dataIndex == 2) {
              //       return new that.$echarts.graphic.LinearGradient(
              //         0,
              //         0,
              //         0,
              //         1,
              //         [
              //           {
              //             offset: 0,
              //             color: colorList[1][0],
              //           },
              //           {
              //             offset: 1,
              //             color: colorList[1][1],
              //           },
              //         ],
              //         false
              //       );
              //     } else {
              //       return new that.$echarts.graphic.LinearGradient(
              //         0,
              //         0,
              //         0,
              //         1,
              //         [
              //           {
              //             offset: 0,
              //             color: colorList[2][0],
              //           },
              //           {
              //             offset: 1,
              //             color: colorList[2][1],
              //           },
              //         ],
              //         false
              //       );
              //     }
              //   },
              // },
            },
          },
        ],
      };

      this.chart.setOption(option, true);
    },
  },
};
</script>
