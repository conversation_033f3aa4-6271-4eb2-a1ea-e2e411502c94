<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <div class="alarm-wrap">
      <h1 class="pre-title">资产信息预览</h1>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <common-card title="云主机总数">
            <template v-slot:value>
              <img
                src="@/assets/monitor/top-icon1.png"
                alt=""
                class="icon-img"
              />
              <div class="info-box">
                <div class="value special">
                  {{ monitorDetail.online }}/{{ monitorDetail.snmpCount }}
                </div>
                <p class="sub-text">不在线总数{{ monitorDetail.offline }}台</p>
              </div>
            </template>
            <template v-slot:chart>
              <img src="@/assets/monitor/chart1.png" alt="" class="chart-img" />
            </template>
          </common-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <common-card title="CPU利用率">
            <template v-slot:value>
              <img
                src="@/assets/monitor/top-icon3.png"
                alt=""
                class="icon-img"
              />
              <div class="info-box">
                <div class="value">{{ monitorDetail.cpuRate }}%</div>
              </div>
            </template>
            <template v-slot:chart>
              <img src="@/assets/monitor/chart3.png" alt="" class="chart-img" />
            </template>
          </common-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <common-card title="内存利用率">
            <template v-slot:value>
              <img
                src="@/assets/monitor/top-icon4.png"
                alt=""
                class="icon-img"
              />
              <div class="info-box">
                <div class="value">{{ monitorDetail.memoryRate }}%</div>
              </div>
            </template>
            <template v-slot:chart>
              <img src="@/assets/monitor/chart4.png" alt="" class="chart-img" />
            </template>
          </common-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <common-card title="磁盘利用率">
            <template v-slot:value>
              <img
                src="@/assets/monitor/top-icon2.png"
                alt=""
                class="icon-img"
              />
              <div class="info-box">
                <div class="value">{{ monitorDetail.diskRate }}%</div>
              </div>
            </template>
            <template v-slot:chart>
              <img src="@/assets/monitor/chart2.png" alt="" class="chart-img" />
            </template>
          </common-card>
        </el-col>
      </el-row>
      <h2 class="alarm-title">告警信息</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12">
          <chart-card title="告警状态分析" :tags="chartTags1">
            <template>
              <polycyclic-graph
                id="polycyclic"
                width="100%"
                height="100%"
                :chart-data="totalData"
                v-if="graphVisible == true"
              />
            </template>
            <template v-slot:list>
              <div class="total-box">
                <div class="list">
                  <i class="dot bg-blue"> </i>
                  <span class="text"
                    >CPU利用率已超过{{ monitorDetail.cpuRate }}%</span
                  >
                </div>
                <div class="list">
                  <i class="dot bg-violet"> </i>
                  <span class="text"
                    >内存利用率已超过{{ monitorDetail.memoryRate }}%</span
                  >
                </div>
                <div class="list">
                  <i class="dot bg-warm"> </i>
                  <span class="text"
                    >磁盘利用率已超过{{ monitorDetail.diskRate }}%</span
                  >
                </div>
              </div>
            </template>
          </chart-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12">
          <chart-card title="CPU利用率告警状态分析" :tags="chartTags2">
            <template v-slot:tips>
              <el-tooltip
                class="item"
                effect="dark"
                :content="`当前共有${cpuTable.length}台云主机存在告警`"
                placement="bottom"
                v-if="cpuTable.length > 0"
              >
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                />
              </el-tooltip>
            </template>
            <template>
              <liquid-fill
                id="cpu"
                width="100%"
                height="100%"
                :chartData="cpuData"
                v-if="graphVisible == true"
              />
            </template>
            <template v-slot:list>
              <scroll-table
                :table-data="cpuTable"
                tab-ref="cpuTable"
                @send="handleInfo"
                v-if="tableVisible == true"
              />
            </template>
          </chart-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12">
          <chart-card title="内存利用率告警状态分析" :tags="chartTags2">
            <template v-slot:tips>
              <el-tooltip
                class="item"
                effect="dark"
                :content="`当前共有${memoryTable.length}台云主机存在告警`"
                placement="bottom"
                v-if="memoryTable.length > 0"
              >
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                />
              </el-tooltip>
            </template>
            <template>
              <liquid-fill
                id="storage"
                width="100%"
                height="100%"
                :chartData="memoryData"
                v-if="graphVisible == true"
              />
            </template>
            <template v-slot:list>
              <scroll-table
                :table-data="memoryTable"
                tab-ref="memoryTable"
                @send="handleInfo"
                v-if="tableVisible == true"
              />
            </template>
          </chart-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12">
          <chart-card title="磁盘利用率告警状态分析" :tags="chartTags2">
            <template v-slot:tips>
              <el-tooltip
                class="item"
                effect="dark"
                :content="`当前共有${diskTable.length}台云主机存在告警`"
                placement="bottom"
                v-if="diskTable.length > 0"
              >
                <img
                  src="@/assets/monitor/alarm.png"
                  alt=""
                  class="alarm-tips-img"
                />
              </el-tooltip>
            </template>
            <template>
              <liquid-fill
                id="disk"
                width="100%"
                height="100%"
                :chartData="diskData"
                v-if="graphVisible == true"
              />
            </template>
            <template v-slot:list>
              <scroll-table
                :table-data="diskTable"
                tab-ref="diskTable"
                @send="handleInfo"
                v-if="tableVisible == true"
              />
            </template>
          </chart-card>
        </el-col>
      </el-row>
    </div>
  </el-scrollbar>
</template>

<script>
import CommonCard from '@/components/CommonCard'; //
import ChartCard from '../components/ChartCard'; //告警分析card 模块
import PolycyclicGraph from '@/components/PolycyclicGraph'; //总告警分析
import LiquidFill from '@/components/LiquidFill'; //水球
import ScrollTable from '../components/ScrollTable';
import { monitoringDetails, warningLog } from '@/api/modules/monitor';
import { math } from '@/utils/math.js';
export default {
  components: {
    CommonCard,
    ChartCard,
    PolycyclicGraph,
    LiquidFill,
    ScrollTable,
  },
  data() {
    return {
      monitorDetail: {}, //资产信息
      chartTags1: [
        {
          name: 'CPU',
          bg: 'bg-blue',
        },
        {
          name: '内存',
          bg: 'bg-violet',
        },
        {
          name: '磁盘',
          bg: 'bg-warm',
        },
      ],
      chartTags2: [
        {
          name: '严重',
          bg: 'bg-red',
        },
        {
          name: '警告',
          bg: 'bg-warm',
        },
        {
          name: '健康',
          bg: 'bg-blue',
        },
      ],
      totalData: {
        xData: [],
        yData: [],
      },
      graphVisible: false,
      diskData: {
        name: '',
        value: 0,
        type: 1,
      },
      cpuData: {
        name: '',
        value: 0,
        type: 1,
      },
      memoryData: {
        name: '',
        value: 0,
        type: 1,
      },
      cpuTable: [],
      memoryTable: [],
      diskTable: [],
      tableVisible: false,
      timer: null,
    };
  },
  mounted() {
    this.getData();
    this.setTimer();
  },
  methods: {
    setTimer() {
      if (this.timer != null) {
        return;
      }
      this.timer = setInterval(() => {
        setTimeout(this.getData(), 0);
        console.log('刷新数据');
      }, 60000);
    },
    getData() {
      //资产信息
      monitoringDetails()
        .then((res) => {
          console.log(res);
          this.totalData.xData = [];
          this.totalData.yData = [];
          if (res.code == 1) {
            this.monitorDetail = res.data;
            this.totalData.xData = ['CPU', '内存', '磁盘'];
            this.totalData.yData = [
              res.data.cpuRate,
              res.data.memoryRate,
              res.data.diskRate,
            ];

            this.cpuData = {
              name: 'CPU利用率',
              value: math.divide(res.data.cpuRate, 100),
              type: res.data.cpuWaringType,
            };
            this.memoryData = {
              name: '内存利用率',
              value: math.divide(res.data.memoryRate, 100),
              type: res.data.memoryWaringType,
            };
            this.diskData = {
              name: '磁盘利用率',
              value: math.divide(res.data.diskRate, 100),
              type: res.data.diskWaringType,
            };
            this.$nextTick(() => {
              this.graphVisible = true;
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
      //设备列表
      warningLog()
        .then((res) => {
          console.log(res);

          if (res.code == 1) {
            res.data.forEach((item) => {
              if (item.type == 'cpu') {
                this.cpuTable = item.dataList;
              } else if (item.type == 'memory') {
                this.memoryTable = item.dataList;
              } else if (item.type == 'disk') {
                this.diskTable = item.dataList;
              }
            });
            this.$nextTick(() => {
              this.tableVisible = true;
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleInfo(item) {
      console.log(item);
      this.$router.push({
        path: `/monitoring/alarm/history`,
        query: {
          hostId: item.hostId,
        },
      });
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 48px);
  background: #fff;
}
.alarm-wrap {
  padding: 20px 24px;
  .pre-title {
    font-size: 24px;
    font-weight: 600;
  }
  .icon-img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 10px;
  }
  .chart-img {
    width: auto;
    height: 46px;
  }
  .info-box {
    .value {
      font-size: 22px;
      line-height: 48px;
      &.special {
        line-height: 32px;
      }
    }
    .sub-text {
      font-size: 12px;
      color: #999;
    }
  }
  .alarm-title {
    font-size: 16px;
    line-height: 32px;
    position: relative;
    margin-bottom: 20px;
    padding-left: 10px;
    &::before {
      content: '';
      height: 16px;
      border-radius: 10px;
      border: 2px solid #b71f40;
      background: #b71f40;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      margin-right: 6px;
    }
  }
  .total-box {
    margin: 30px 0 0 30px;
    .list {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 3px;
        background: #e3e3e3;
        border-radius: 50%;
        &.bg-warm {
          background: #ffc700;
        }
        &.bg-blue {
          background: #108ff4;
        }
        &.bg-violet {
          background: #6e3cfc;
        }
      }
    }
  }
  .alarm-tips-img {
    width: 26px;
  }
}
</style>
