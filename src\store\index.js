import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import errorLog from './modules/errorLog'
import permission from './modules/permission'
import tagsView from './modules/tagsView'
import user from './modules/user'
import settings from './modules/settings'
import getters from './getters'
import createPersistedState from "vuex-persistedstate"


Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    errorLog,
    permission,
    settings,
    tagsView,
    user
  },
  getters,
  // plugins: [createPersistedState(
  //   {
  //   storage: window.sessionStorage,
  //   // reducer(val) {
  //   //   return {
  //   //   // 只储存state中的assessmentData
  //   //   userid:val.userid
  //   //  }
  //   // }
  // }
  // )]
})

export default store
