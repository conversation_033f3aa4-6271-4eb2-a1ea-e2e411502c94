<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">云安全日志</h3>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="网络威胁" name="first">
          <threat ref="threat" />
        </el-tab-pane>
        <el-tab-pane label="主机威胁" name="second">
          <host ref="host" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import host from './host';
import threat from './threat';

export default {
  components: {
    host,
    threat,
  },
  data() {
    return {
      activeName: 'first',
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab);
      if (tab.index == 0) {
        this.$refs.threat.fireThreatRefresh();
      } else if (tab.index == 1) {
        this.$refs.host.hostThreatRefresh();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.mainWrapper {
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.mainBox::v-deep .el-tabs__item {
  height: 46px;
  line-height: 46px;
}
</style>
