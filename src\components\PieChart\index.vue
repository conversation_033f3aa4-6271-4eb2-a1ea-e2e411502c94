<template>
  <div class="charts-box">
    <div :id="id" class="chart" ref="echart"></div>
    <div class="chart-desc">
      <p>
        剩余量：<span class="num">{{ this.chartData.surplus }}</span>
        <span v-if="chartData.name == 'CPU'">MHZ</span>
        <span v-else-if="chartData.name == '内存'">GB</span>
        <span v-else-if="chartData.name == '硬盘'">M</span>
      </p>
      <p>
        总资源：
        <span class="num">{{ this.chartData.total }}</span>
        <span v-if="chartData.name == 'CPU'">MHZ</span>
        <span v-else-if="chartData.name == '内存'">GB</span>
        <span v-else-if="chartData.name == '硬盘'">M</span>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    id: String,
    colorArr: Array,
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      title: "",
      chart: null,
      percent: null,
      color: ""
    };
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.setOptions(newVal);
      },
      deep: true
    }
  },

  mounted() {
    this.initChart();
    window.addEventListener("resize", this.__resizeHanlder);
  },
  methods: {
    th__resizeHanlder() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    numFilter(value) {
      // 截取当前数据到小数点后x位--四舍五入
      let realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
    //圆环初始化
    initChart() {
      this.$nextTick(() => {
        this.clearChart();
        this.setOptions(this.chartData);
      });

      //  console.log(this.chartData);
    },
    clearChart() {
      this.chart && this.chart.clear(); //清除当前实例
    },
    //配置
    setOptions(val) {
      let _this = this;
      this.percent = this.numFilter((val.surplus / val.total) * 100);
      // console.log(val);
      // console.log(val.name);
      if (val.name == "CPU") {
        this.color = "#E11FFE";
      } else if (val.name == "内存") {
        this.color = "#2BD0E7";
      } else if (val.name == "硬盘") {
        this.color = "#3964F8";
      } else {
        this.color = "#EC5960";
      }
      // console.log( this.color);
      this.chart = this.$echarts.init(this.$refs.echart);
      let options = {
        // color: this.colorArr,
       
        // tooltip: {},
        title: {
          text: _this.percent + "%",
          subtext: val.name,
          textStyle: {
            color: "#fff",
            fontSize: 24, //圆环中间文字大小设置
            fontWeight: "400"
          },
          subtextStyle: {
            color: "#fff",
            fontSize: 14, //圆环中间文字大小设置
            fontWeight: "400"
          },
          left: "center",
          top: "center",
          itemGap: 1 // 主副标题距离
        },
        angleAxis: {
          // max:100,//满分
          clockwise: true, // 逆时针
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        radiusAxis: {
          type: "category",
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        polar: {
          center: ["50%", "50%"], //图形在画布中的位置
          radius: "140%" //图形大小
        },
        series: [
          {
            type: "bar",
            data: [
              {
                name: val.name + "占比",
                value: Number(val.surplus),
                itemStyle: {
                  color: _this.color
                }
              }
            ],
            coordinateSystem: "polar",
            roundCap: true,
            barWidth: 16, //环的宽度
            barGap: "-100%", // 两环重叠
            z: 2
          },
          {
            // 灰色环
            type: "bar",
            data: [
              {
                name:val.name + "总资源",
                value: Number(val.total),
                itemStyle: {
                  color: "#030F42",
                  // color: "#ff0",
                  shadowColor: "rgba(0, 0, 0, 0.2)",
                  shadowBlur: 5,
                  shadowOffsetY: 2
                }
              }
            ],
            coordinateSystem: "polar",
            roundCap: true,
            barWidth: 16,
            barGap: "-100%", // 两环重叠
            z: 1
          }
        ]
      };
      this.chart.setOption(options);
    },
    //容量转换
    // diskSize(num) {
    //   if (num == 0) return "0 B";
    //   var k = 1024; //设定基础容量大小
    //   var sizeStr = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]; //容量单位
    //   var i = 0; //单位下标和次幂
    //   for (var l = 0; l < 8; l++) {
    //     //因为只有8个单位所以循环八次
    //     if (num / Math.pow(k, l) < 1) {
    //       //判断传入数值 除以 基础大小的次幂 是否小于1，这里小于1 就代表已经当前下标的单位已经不合适了所以跳出循环
    //       break; //小于1跳出循环
    //     }
    //     i = l; //不小于1的话这个单位就合适或者还要大于这个单位 接着循环
    //   } // 例： 900 / Math.pow(1024, 0)  1024的0 次幂 是1 所以只要输入的不小于1 这个最小单位就成立了； //     900 / Math.pow(1024, 1)  1024的1次幂 是1024  900/1024 < 1 所以跳出循环 下边的 i = l；就不会执行  所以 i = 0； sizeStr[0] = 'B'; //     以此类推 直到循环结束 或 条件成立
    //   return (num / Math.pow(k, i)).toFixed(2) + " " + sizeStr[i]; //循环结束 或 条件成立 返回字符
    // },
    beforeDestroy() {
      if (!this.chart) {
        return;
      }
      window.removeEventListener("resize", this.__resizeHanlder);
      this.chart.dispose();
      this.chart = null;
    }
  }
};
</script>
<style lang="scss" scoped>
.charts-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .chart {
    width: 60%;
    height: 250px;
  }
  .chart-desc {
    display: flex;
    flex-direction: column;

    p {
      color: #52c4ff;
      margin-bottom: 14px;
      .num {
        color: #fff;
        font-size: 16px;
      }
    }
  }
}
</style>
