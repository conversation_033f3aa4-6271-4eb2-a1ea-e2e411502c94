// sidebar
$menuText:#4a4c4f;
$menuActiveText:#db2e43;
$subMenuActiveText:#db2e43; //https://github.com/ElemeFE/element/issues/12951

$menuBg:#fff;
$menuHover:#fff;


$subMenuBg:#fff;
$subMenuHover:rgba(245, 247, 255, 1);

$sideBarWidth: 239px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
