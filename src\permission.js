import router, { createRouter } from '@/router'

import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import Layout from '@/layout'
export function GetUrlRelativePath(url) {
  var arrUrl = url.split('//')

  var start = arrUrl[1].indexOf('/')
  var relUrl = arrUrl[1].substring(start)

  if (relUrl.indexOf('?') !== -1) {
    relUrl = relUrl.split('?')[0]
  }
  return relUrl
}
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  // document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      const hasGetUserInfo = store.getters.name
    //  const hasRoles = store.getters.roles && store.getters.roles.length > 0
     // const hasGetUserInfo = store.getters.name
      if ( hasGetUserInfo) {
        // console.log(to)
        next()
      } else {
        try {
          const fromPath = GetUrlRelativePath(window.location.href)
          // get user info
          await store.dispatch('user/getInfo')
          // console.log(store.getters.menus)
          await store.dispatch('permission/generateRoutes',store.getters.menus).then(accessRoutes => {
            // 根据roles权限生成可访问的路由表

            //debugger

            // initRouter 这个方法就是根据权限列表生产路由的方法；因项目而异，不同的人的实现方案也可能不一样
            const routes = accessRoutes
            router.options.routes = routes

            // router.options.routes = routes

            // 动态添加可访问路由表
            // router.addRoutes(routes)
            router.selfaddRoutes(routes);

            // next({ path: fromPath })
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
            //console.log(to);

          })

        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          console.log(error)
          //debugger
          Message.error({
            type:'error',
            message:error || 'Has Error'
          })
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      // 在免登录白名单，直接进入
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      // debugger
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
