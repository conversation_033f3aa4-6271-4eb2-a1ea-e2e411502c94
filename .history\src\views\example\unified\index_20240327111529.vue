<template>
  <div class="mainWrapper">
    <div class v-if="showTenant">
      <div class="header">
        <h3 class="title">模块管理</h3>
      </div>
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <div class="login-box clearfix" ref="loginbox" :collapse="isCollapse">
          <div
            class="tp-wrap clearfix"
            :style="{
              transform: `scale(${scalseNum},${scalseNum}) `
            }"
            v-if="poductList.length > 0"
          >
            <div class="f-wrap">
              <img src="@/assets/unified/fangwu.png" alt="fangwu" />
            </div>
            <div class="x-wrap">
              <img src="@/assets/unified/xiaozhushi.png" alt="" />
            </div>
            <!--统一密码平台  -->
            <!-- <div class="hx-wrap" v-if="productKeys.includes('hxapi')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover(key = 'hxapi')"
              >
                <p>{{ des }}</p>
                <span class="img-box" slot="reference"  @click="handleImg(key = 'hxapi')">
                  <img src="@/assets/unified/tongyimimapingtaiH.png" alt="统一密码平台" />
                </span>
              </el-popover>
            </div>
            <div class="hx-wrap" v-else>
              <img src="@/assets/unified/tongyimima.png" alt="统一密码平台" class="disabled" />
            </div> -->
            <!-- 云漏扫 -->
            <div class="hx-wrap" v-if="productKeys.includes('scan')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'scan'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'scan'))"
                >
                  <img src="@/assets/unified/ylsH.png" alt="云漏扫" />
                </span>
              </el-popover>
            </div>
            <div class="hx-wrap" v-else>
              <img
                src="@/assets/unified/yls.png"
                alt="云漏扫"
                class="disabled"
              />
            </div>
            <!-- 天翼专属云 -->
            <div class="cloud-wrap" v-if="productKeys.includes('cloud')">
              <el-popover
                placement="right"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'cloud'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'cloud'))"
                >
                  <img src="@/assets/unified/tianyiyunH.png" alt="天翼专属云" />
                </span>
              </el-popover>
            </div>
            <div class="cloud-wrap" v-else>
              <img
                src="@/assets/unified/tianyiyun.png"
                alt="天翼专属云"
                class="disabled"
              />
            </div>
            <!-- 云防火墙 -->
            <div class="firewall-wrap" v-if="productKeys.includes('firewall')">
              <el-popover
                placement="right"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'firewall'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'firewall'))"
                >
                  <img
                    src="@/assets/unified/fanghuoqiangH.png"
                    alt=" 云防火墙"
                  />
                </span>
              </el-popover>
            </div>
            <div class="firewall-wrap" v-else>
              <img
                src="@/assets/unified/fanghuoqiang.png"
                alt=" 云防火墙"
                class="disabled"
              />
            </div>
            <!-- "云Web应用防火墙" -->
            <div class="waf-wrap" v-if="productKeys.includes('waf')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'waf'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'waf'))"
                >
                  <img
                    src="@/assets/unified/yunWebH.png"
                    alt="云Web应用防火墙"
                  />
                </span>
              </el-popover>
            </div>
            <div class="waf-wrap" v-else>
              <img
                src="@/assets/unified/yunWeb.png"
                alt="云Web应用防火墙"
                class="disabled"
              />
            </div>
            <!-- 云日志审计 -->
            <div class="logAudit-wrap" v-if="productKeys.includes('logAudit')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'logAudit'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'logAudit'))"
                >
                  <img src="@/assets/unified/yunrizhiH.png" alt="云日志审计" />
                </span>
              </el-popover>
            </div>
            <div class="logAudit-wrap" v-else>
              <img
                src="@/assets/unified/yunrizhi.png"
                alt="云日志审计"
                class="disabled"
              />
            </div>
            <!-- 云数据库审计 -->
            <div class="database-wrap" v-if="productKeys.includes('database')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'database'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'database'))"
                >
                  <img
                    src="@/assets/unified/yunshujushenjiH.png"
                    alt="云数据库审计"
                  />
                </span>
              </el-popover>
            </div>
            <div class="database-wrap" v-else>
              <img
                src="@/assets/unified/yunshuju.png"
                alt="云数据库审计"
                class="disabled"
              />
            </div>
            <!-- 云主机安全 -->
            <div class="host-wrap" v-if="productKeys.includes('hostSecurity')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'hostSecurity'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'hostSecurity'))"
                >
                  <img src="@/assets/unified/yunzhujiH.png" alt="云主机安全"
                /></span>
              </el-popover>
            </div>
            <div class="host-wrap" v-else>
              <img
                src="@/assets/unified/yunzhuji.png"
                alt="云主机安全"
                class="disabled"
              />
            </div>
            <!-- 云主机 -->
            <!-- <div class="hostAll-wrap" v-if="productKeys.includes('hostSecurity')">
              <span class="img-box" slot="reference">
                <img src="@/assets/unified/yunHostH.png" alt="云主机" />
              </span>
            </div> -->
            <div class="hostAll-wrap">
              <img src="@/assets/unified/yunHost.png" alt="云主机" />
            </div>
            <!-- SSL VPN -->
            <div class="vpn-wrap" v-if="productKeys.includes('vpn')">
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'vpn'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'vpn'))"
                >
                  <img src="@/assets/unified/SSL-VPNH.png" alt="SSL VPN" />
                </span>
              </el-popover>
            </div>
            <div class="vpn-wrap" v-else>
              <img
                src="@/assets/unified/SSL-VPN.png"
                alt="SSL VPN"
                class="disabled"
              />
            </div>
            <!-- 云堡垒机 -->
            <div
              class="fortress-wrap"
              v-if="productKeys.includes('fortressMachine')"
            >
              <el-popover
                placement="top"
                width="260"
                trigger="hover"
                popper-class="net-popover"
                @show="tipHover((key = 'fortressMachine'))"
              >
                <p>{{ des }}</p>
                <span
                  class="img-box"
                  slot="reference"
                  @click="handleImg((key = 'fortressMachine'))"
                >
                  <img src="@/assets/unified/yunbaoleijiH.png" alt="云堡垒机" />
                </span>
              </el-popover>
            </div>
            <div class="fortress-wrap" v-else>
              <img
                src="@/assets/unified/yunbaoleiji.png"
                alt="云堡垒机"
                class="disabled"
              />
            </div>
            <div class="net-dot1-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot2-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot3-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot4-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot5-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot6-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot7-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot8-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot9-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot10-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot11-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot12-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
            <div class="net-dot13-box">
              <img src="@/assets/unified/shang.png" alt="" class="dot" />
            </div>
          </div>

          <!-- <ul class="login-item-container clearfix" v-if="poductList.length > 0">
            <li
              class="login-item"
              v-for="(item, index) in poductList"
              :key="index"
              @click="handleClick(item)"
            >
              <div class="login-item-box">
                <el-tooltip class="item" effect="light" placement="right">
                  <div slot="content" style="max-width: 320px; line-height: 22px">
                    {{ item.des }}
                  </div>
                  <img
                    src="@/assets/unified/fhq.png"
                    alt
                    class="product-img"
                    v-if="item.key == 'firewall'"
                  />
                  <img
                    src="@/assets/unified/ssl.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'waf'"
                  />
                  <img
                    src="@/assets/unified/blj.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'fortressMachine'"
                  />

                  <img
                    src="@/assets/unified/zj.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'hostSecurity'"
                  />
                  <img
                    src="@/assets/unified/rzsj.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'logAudit'"
                  />
                  <img
                    src="@/assets/unified/sjk.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'database'"
                  />
                  <img
                    src="@/assets/unified/vpn.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'vpn'"
                  />
                  <img
                    src="@/assets/unified/password.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'hxapi'"
                  />
                  <img
                    src="@/assets/unified/cloud.png"
                    alt
                    class="product-img"
                    v-else-if="item.key == 'cloud'"
                  />
                </el-tooltip>

                <span class="name">{{ item.productName }}</span>
               
              </div>
            </li>
          </ul> -->
          <div v-if="noProduct">
            <div class="product-null-box">
              <img src="@/assets/unified/noProduct.png" alt />
              <p class="tip">暂无产品，请先配置</p>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import waves from '@/directive/waves'; // waves directive
import { parseTime, param } from '@/utils';
import Pagination from '@/components/Pagination'; // secondary package based on el-pagination
import {
  hostLogin,
  host,
  databaseLogin,
  networkList,
  zsTackLogin,
} from '@/api/unified.js';
import Cookies from 'js-cookie';
import { getToken, setToken, removeToken } from '@/utils/auth';
import store from '@/store';
import { Loading } from 'element-ui';
import { mapGetters } from 'vuex';
import { productList } from '@/api/system';
export default {
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      userId: '',
      userName: '',
      loading: false,
      jumpUrl: null,
      showTenant: true,
      account: '',
      poductList: [],
      noProduct: false,
      productKeysArr: [],
      productKeys: {} || '',
      des: '',
      scalseNum: 1,
      screenWidth: document.body.clientWidth, // 屏幕尺寸宽度
      screenHeight: document.body.clientHeight, // 屏幕尺寸高度
    };
  },
  created() {
    this.userId = this.userid;
    this.getnetworkList();
  },
  computed: {
    ...mapGetters(['userid', 'sidebar']),
    isCollapse() {
      this.resizeWin();
      // return !this.sidebar.opened;
    },
  },
  watch: {
    // 新窗口打开页面，解决浏览器拦截问题
    // jumpUrl() {
    //   if (this.jumpUrl) {
    //     window.open(this.jumpUrl, "_blank");
    //   }
    //   this.jumpUrl = null;
    // }
  },
  mounted() {
    this.resizeWin();
    const that = this;
    window.onresize = () => {
      return (() => {
        that.resizeWin();
      })();
    };
  },

  methods: {
    resizeWin() {
      // window.screenWidth = document.body.clientWidth;
      // window.screenHeight = document.body.clientHeight;
      this.$nextTick(() => {
        this.screenWidth = this.$refs.loginbox.clientWidth;
        //console.log(this.screenWidth);
        let scalW = this.screenWidth / 1680;
        // console.log(  this.scalseNum);

        this.scalseNum = scalW;
      });
    },
    tipHover(key) {
      // console.log(key);
      // console.log(this.poductList);
      this.poductList.filter((item) => {
        if (item.key == key) {
          this.des = item.des;
          return;
        }
      });
    },
    handleImg(key) {
      this.poductList.filter((item) => {
        if (item.key == key) {
          if (item.key == 'cloud') {
            zsTackLogin()
              .then((res) => {
                // console.log(res);
                if (res.code == 1) {
                  window.open(`${res.data}`, '_blank');
                }
              })
              .catch((error) => {
                console.log(error);
              });
          } else {
            // console.log(item);
            window.open(`${item.url}`, '_blank');
          }
        }
      });
    },
    newLink(url) {
      let form = document.createElement('form');
      form.action = url;
      form.target = '_blank';
      form.method = 'POST';
      document.body.appendChild(form);
      form.submit();
    },
    getnetworkList() {
      //console.log(this.userId);
      this.loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      networkList(this.userId)
        .then((res) => {
          // console.log(res);
          this.productKeysArr = [];
          if (res.ok == true) {
            this.poductList = res.obj;
            this.loading.close();
            if (this.poductList.length == 0) {
              this.noProduct = true;
            } else {
              for (let i = 0; i < this.poductList.length; i++) {
                this.productKeysArr.push(this.poductList[i].key);
              }
              // console.log(this.productKeysArr);
              this.productKeys = this.productKeysArr.join(',');
              // this.productKeys = ''
              // console.log(this.productKeys);
            }
          }
        })
        .catch((error) => {
          console.log(error);
          this.loading.close();
        });
    },
    handleClick(item) {
      // console.log(item);
      if (item.key == 'cloud') {
        zsTackLogin()
          .then((res) => {
            console.log(res);
            if (res.code == 1) {
              window.open(`${res.data}`, '_blank');
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading.close();
          });
      } else {
        window.open(`${item.url}`, '_blank');
      }

      //       if (item.key == "hostSecurity") {
      //         let data = {
      //           username: item.userName,
      //           password: this.$md5(item.password),
      //           password_token: Base64.encode(item.password), //encode 加密 decode 解密
      //           vcode: "",
      //         };
      //         var mywin = window.open("_blank");
      //         mywin.location = `${
      //           item.url
      //         }/login/login?username=admin&password=${this.$md5(
      //           item.password
      //         )}&password_token=${Base64.encode(item.password)}`;

      //         this.loading = Loading.service({
      //           lock: true,
      //           text: "加载中……",
      //           background: "rgba(0, 0, 0, 0.7)",
      //         });
      //         // console.log(data);
      //         hostLogin()
      //           .then((res) => {
      //             console.log(res);
      //             let url = res.data.data.redirect;
      //             //console.log(url)
      //             if (res.code == 1) {
      //               //https://*************:8443/login/login?username=admin&password=a0050f235160436b1db4b152943f0081&password_token=SE9TVGNlc2hpQDEyMw%3D%3D&vcode=

      //               setTimeout(() => {
      //                 this.loading.close();
      //                 mywin.close();
      //                 this.jumpUrl = `${item.url}${url}`;
      //               }, 100);
      //             }
      //           })
      //           .catch((error) => {
      //             this.loading.close();
      //           });
      //       } else if (item.key == "database") {
      //         this.userId = localStorage.getItem("userid");
      //         console.log(this.userId);
      //         let data = {
      //           userId: this.userId,
      //           key: "database",
      //         };
      //         databaseLogin(data)
      //           .then((res) => {
      //             this.loading = Loading.service({
      //               lock: true,
      //               text: "加载中……",
      //               background: "rgba(0, 0, 0, 0.7)",
      //             });
      //             console.log(res);
      //             if (res.ok == true) {
      //               let url = res.obj.url;
      //               let token = res.obj.token;
      //               setTimeout(() => {
      //                 this.loading.close();
      //                 window.open(`${item.url}${url}?token=${token}`, "_blank");
      //               }, 1000);
      //             }
      //           })
      //           .catch((error) => {
      //             this.loading.close();
      //           });
      //       } else if (item.key == "firewall") {
      //         console.log(item.url);
      //         console.log(item.userName);
      //         console.log(item.password);
      //          console.log(Base64.encode(item.userName));
      //         console.log(Base64.encode(item.password));
      //         window.open(
      //           `${item.url}/sso.php?userName=${Base64.encode(item.userName)}&password=${Base64.encode(item.password)}
      // `,
      //           "_blank"
      //         );
      //       } else if (item.key == "waf") {
      //        window.open(
      //           `${item.url}/sso.php?userName=${Base64.encode(item.userName)}&password=${Base64.encode(item.password)}
      // `,
      //           "_blank"
      //         );
      //       } else {
      //         window.open(`${item.url}`, "_blank");
      //       }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 120px);
}

.mainWrapper {
  .login-box {
    display: flex;
    padding: 30px 0;
    box-sizing: border-box;
    position: relative;
    height: 100%;
    align-items: center;
    justify-content: center;

    .login-item {
      width: 33.3%;
      float: left;
      margin-bottom: 40px;
      cursor: pointer;

      .login-item-box {
        display: flex;
        height: 0.8rem;
        margin-right: 24px;
        // justify-content: center;
        align-items: center;
        position: relative;
        // overflow: hidden;
        background: #ffffff;
        box-shadow: 0px 3px 10px 1px rgba(214, 223, 245, 0.83),
          inset 0px 0px 6px 1px rgba(13, 108, 249, 0.14);
        border-radius: 12px 12px 12px 12px;

        .product-img {
          display: inline-block;
          width: 0.36rem;
          height: auto;
          margin: 0 40px;
        }

        .name {
          display: inline-block;
          font-size: 24px;
          position: relative;
          padding: 16px 0;

          &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 26px;
            height: 3px;
            background: #4783f2;
          }
        }

        .overlaybox {
          display: flex;
          height: 220px;
          justify-content: center;
          align-items: center;
          position: absolute;
          bottom: -220px;
          left: 0;
          background: linear-gradient(
            313deg,
            rgba(0, 125, 219, 1) 0%,
            rgba(0, 69, 120, 1) 100%
          );
          padding: 20px;
          color: #fff;
          line-height: 1.6;
        }
      }

      .login-item-box:hover .overlaybox {
        bottom: 0;
      }
    }

    .product-null-box {
      text-align: center;
      margin-top: 120px;

      .tip {
        color: #005ea4;
        margin-top: 30px;
        font-size: 16px;
      }

      .configuration {
        display: inline-block;
        padding: 5px 16px;
        background: #409eff;
        border-radius: 10px;
        color: #fff;
        margin-top: 30px;
        cursor: pointer;
      }
    }
  }

  .container {
    padding: 20px 10px;
    background: #fff;
    border-radius: 3px;
    height: auto;

    .filter-container {
      margin-bottom: 10px;

      .filter-item {
        margin-right: 10px;
      }
    }

    .el-pagination {
      margin-top: 10px;
    }

    .normal {
      color: #409eff;
    }

    .abnormal {
      color: #f56c6c;
    }
  }
}
</style>
<style>
.product-img:focus {
  outline: none !important;
}
.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='right'].is-light .popper__arrow:after {
  border-right-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow {
  border-left-color: #004578;
}

.el-tooltip__popper[x-placement^='left'].is-light .popper__arrow:after {
  border-left-color: #004578;
}

/* 控制主题颜色 */
.el-tooltip__popper.is-light {
  background: #0043b1 !important;
}
</style>
