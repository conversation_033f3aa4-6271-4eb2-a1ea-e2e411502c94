@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

* {
  padding: 0;
  margin: 0;
}

$blue:rgb(64, 158, 255);

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif;
  font-size: 14px;
  overflow: hidden;
  background-color: #F1F6FA;
}

ul,
ol {
  list-style: none;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}



// main-container global css
.navbar {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  background: #b71f40;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 48px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 48px;

    &:focus {
      outline: none;
    }

    .safe {
      display: inline-block;
      padding: 0 8px;
      color: #fff;
      border-radius: 3px;
      line-height: 32px;
      margin-right: 5px;
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, .3);
      }

      .svg-container {
        display: inline-block;
        margin-right: 5px;
      }
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 14px;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      float: right;
      cursor: pointer;

      .avatar-wrapper {
        // margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .user-name {
          display: inline-block;
          padding: 0 8px;
          height: 100%;
          font-size: 14px;
          color: #fff;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 20px;
          font-size: 12px;
          color: #fff;
        }
      }
    }
  }
}

.app-container {
  padding: 20px;
}

.el-table td {
  padding: 12px 8px;
}

.el-table th {
  padding: 5px 8px;
  font-weight: normal;
}

.table-box2 {
  .el-table th {
    padding: 12px 8px;

  }
}

.mainWrapper {
  height: 100%;
  background: #fff;
}

.mainBox {
  // padding: 20px 10px;
  background-color: #fff;

  .search-box {
    padding: 20px 30px;
    border-bottom: 1px solid #eef3f7;

    .filter-item {
      margin-right: 16px;
    }
  }


  .opt {
    display: inline-block;
    padding: 0 5px;
    color: #db2e43;
    cursor: pointer;
  }

  .line {

    color: #409EFD;

  }

  .serch-box {
    padding: 20px 30px;
    border-bottom: 1px solid #eef3f7;
  }
}

.scrollbar-wrapper {
  overflow-x: hidden !important;
}

.el-table thead {
  color: #5e6978;
  font-weight: 400 !important;
}

.header {
  padding: 20px 24px;
  background: #fff;
  box-shadow: 0 1px 4px 0 rgba(218, 224, 230, .4);

  h3 {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;

    .back-icon {
      display: inline-block;
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      border: 1px solid #DAE0E6;
      margin-right: 10px;
      color: #00437d;
      cursor: pointer;

    }

  }

}

.el-tabs__header {
  margin-bottom: 0;
}

.el-tabs__nav-wrap {
  padding: 0 30px;
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #eef3f7;
}

.table-box {
  padding: 0 24px;

  .el-dropdown-link {
    display: inline-block;
    background: #F0F2F5;
    border-radius: 3px 3px 3px 3px;
    padding: 0 10px;
    cursor: pointer;
  }
}

.filter-container {
  float: left;
}

.page-box {
  float: right;
}

.el-dropdown-menu {
  .opt {
    padding: 5px 10px;
    color: #db2e43;
    cursor: pointer;
    text-align: center;
  }
}

.el-select-dropdown__item.selected {
  color: #db2e43;

}

.el-select .el-input.is-focus .el-input__inner,
.el-pagination__sizes .el-input .el-input__inner:hover {
  border-color: #db2e43;
}

.pageNum {
  display: inline-block;
  height: 34px !important;
  line-height: 34px !important;
  text-align: center;
  font-size: 14px !important;
  font-weight: 400;

  .divider {
    display: inline-block;
    margin: 0 2px;
    font-style: normal;
  }
}

.form-box {
  padding: 20px 30px;

  .form-box-hd {
    background: #fff;
    border-radius: 3px;
    padding: 20px;

    .form-box-left {
      width: 100px;
      float: left;

      .text {
        font-size: 16px;
        font-weight: 500;
        color: #1a2736;

      }
    }

    .form-box-right {
      padding-left: 100px;

    }
  }

}

.forminputWidth {
  width: 300px;
}

.form-foot {
  width: 100%;
  text-align: right;
  position: absolute;
  height: 90px;
  line-height: 90px;
  padding: 0 30px;
  background: #fafdff;
  box-shadow: 0px -1px 2px 0px rgba(218, 224, 230, 0.7);
}

.elTableBar {
  .el-scrollbar {
    height: 100%;
  }
}

.auth-img {
  display: inline-block;
  position: relative;
  top: 5px;
  margin-right: 10px;
}

.copy-img {
  position: absolute;
  top: 10px;
  right: 0;
  cursor: pointer;
}

.mr_0 {
  margin-right: 0 !important;
}

.mr_20 {
  margin-right: 20px;
}

.expire {
  padding: 10px 30px;
}

.tips-img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.expire-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

}

.expire-tip {
  font-size: 12px;
  line-height: 24px;
}

.password-strength {
  display: flex;

  margin-bottom: 8px;

  li {
    flex: 1;
    padding-right: 5px;

    span {
      display: block;
      height: 10px;
      background: #eee;
      line-height: 20px;
    }

    p {
      text-align: center;
    }

    &:nth-child(1) {
      span {
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-right: 0px solid;
      }

      p {
        color: red;
      }
    }

    &:nth-child(2) {
      span {
        border-left: 0px solid;
        border-right: 0px solid;
      }

      p {
        color: orange;
      }
    }

    &:nth-child(3) {
      span {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        border-left: 0px solid;

      }

      p {
        color: #00d1b2;
      }
    }
  }
}

#prompt {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  background: #000;
  text-align: center;
  color: white;
  padding-top: 25%;
  font-size: 20px;
  font-weight: bold;

  &.active {
    display: block;
  }
}

@keyframes icon_animation {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes rolling {
  0% {
    left: -8px;
  }

  50% {
    left: 88px;
  }

  100% {
    left: 152px;
  }
}

@keyframes rolling2 {
  0% {
    left: -8px;
  }

  50% {
    left: 18px;
  }

  100% {
    left: 38px;
  }
}

@keyframes rolling3 {
  0% {
    left: -8px;
  }

  50% {
    left: 18px;
  }

  100% {
    left: 58px;
  }
}

@keyframes rolling4 {
  0% {
    left: -8px;
  }

  50% {
    left: 58px;
  }

  100% {
    left: 88px;
  }
}

.el-tooltip__popper {
  max-width: 60% !important;

}

.el-table {

  &::before {
    height: 0;
  }

  .doc-content {

    p,
    b,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    i,
    font {
      font-size: 14px !important;
      font-weight: 400 !important;
      padding: 0 !important;
      font-style: normal !important;
      padding: 0 !important;
      margin: 0 !important;
      line-height: 23px !important;
      color: #333 !important;
    }
  }
}

.el-tabs__item {
  box-shadow: none !important;

  &.is-active {
    color: #db2e43;
  }

  &:hover {
    color: #db2e43;
  }
}

.el-tabs__active-bar {
  background-color: #db2e43;
}

.ukey-dialog .el-dialog__body {
  padding: 0;
}


.tp-wrap {
  position: relative;
  width: 1246px;

  &>div {
    display: inline-block;

    span {
      display: inline-block;

      &:focus {
        outline: none;
      }

    }

    &.hx-wrap {
      img {
        width: 160px;
      }
    }

    img {

      &.disabled {
        cursor: not-allowed;
      }

      &:focus {
        outline: none;
      }

    }
  }

  .img-box {
    &>img {
      cursor: pointer;
      width: 160px;
    }
  }

  .f-wrap {
    position: absolute;
    left: 60px;
    top: 280px;
  }

  .x-wrap {
    position: absolute;
    left: 180px;
    top: 164px;
  }

  .hx-wrap {
    position: absolute;
    left: 386px;
    top: 86px;
  }

  .cloud-wrap {
    position: absolute;
    left: 560px;
    top: -10px;
  }

  .firewall-wrap {
    position: absolute;
    left: 726px;
    top: 106px;
  }

  .waf-wrap {
    position: absolute;
    left: 912px;
    top: 226px;
  }

  .logAudit-wrap {
    position: absolute;
    left: 1066px;
    top: 316px;
  }

  .database-wrap {
    position: absolute;
    left: 890px;
    top: 432px;
  }

  .host-wrap {
    position: absolute;
    left: 732px;
    top: 560px;
  }

  .hostAll-wrap {
    position: absolute;
    left: 402px;
    top: 162px;

    img {
      width: 506px;
      height: 449px;
    }
  }

  .vpn-wrap {
    position: absolute;
    left: 550px;
    top: 657px;
  }

  .fortress-wrap {
    position: absolute;
    left: 292px;
    top: 512px;
  }

  .dot {
    position: absolute;
    top: -8px;
    left: -8px;
    transform: rotate(30deg);
    animation: 1.2s rolling_1 linear infinite normal;
  }

  .net-dot1-box {
    position: absolute;
    top: 222px;
    left: 291px;
    width: 120px;
    // border: 1px dashed #00518d;
    transform: rotate(-32deg);

    .dot {

      animation: 1.2s rolling_1 linear infinite normal;
    }
  }

  .net-dot2-box {
    position: absolute;
    top: 118px;
    left: 495px;
    width: 78px;
    // border: 1px dashed #00518d;
    transform: rotate(-30deg);

    .dot {

      animation: 1.2s rolling_2 linear infinite normal;
    }
  }

  .net-dot3-box {
    position: absolute;
    top: 107px;
    left: 694px;
    width: 78px;
    // border: 1px dashed #00518d;
    transform: rotate(32deg);

    .dot {

      animation: 1.2s rolling_3 linear infinite normal;
    }
  }

  .net-dot4-box {
    position: absolute;
    top: 226px;
    left: 867px;
    width: 110px;
    // border: 1px dashed #00518d;
    transform: rotate(32deg);

    .dot {

      animation: 1.2s rolling_4 linear infinite normal;
    }
  }

  .net-dot5-box {
    position: absolute;
    top: 333px;
    left: 1043px;
    width: 104px;
    // border: 1px dashed #00518d;
    transform: rotate(34deg);

    .dot {

      animation: 1.2s rolling_4 linear infinite normal;
    }
  }

  .net-dot6-box {
    position: absolute;
    top: 470px;
    left: 1016px;
    width: 104px;
    // border: 1px dashed #00518d;
    transform: rotate(150deg);

    .dot {

      animation: 1.2s rolling_5 linear infinite normal;
    }
  }

  .net-dot7-box {
    position: absolute;
    top: 592px;
    left: 848px;
    width: 94px;
    // border: 1px dashed #00518d;
    transform: rotate(145deg);

    .dot {

      animation: 1.2s rolling_4 linear infinite normal;
    }
  }

  .net-dot8-box {
    position: absolute;
    top: 697px;
    left: 653px;
    width: 80px;
    // border: 1px dashed #00518d;
    transform: rotate(-26deg);

    .dot {

      animation: 1.2s rolling_4 linear infinite normal;
    }
  }

  .net-dot9-box {
    position: absolute;
    top: 546px;
    left: 408px;
    width: 96px;
    // border: 1px dashed #00518d;
    transform: rotate(-32deg);

    .dot {

      animation: 1.2s rolling_4 linear infinite normal;
    }
  }

  .net-dot10-box {
    position: absolute;
    top: 344px;
    left: 352px;
    width: 106px;
    // border: 1px dashed #00518d;
    transform: rotate(36deg);

    .dot {
      transform: rotate(-150deg);
      animation: 1.2s rolling_6 linear infinite normal;
    }
  }

  .net-dot11-box {
    position: absolute;
    top: 212px;
    left: 502px;
    width: 106px;
    // border: 1px dashed #00518d;
    transform: rotate(34deg);

    .dot {
      transform: rotate(-150deg);
      animation: 1.2s rolling_6 linear infinite normal;
    }
  }

  .net-dot12-box {
    position: absolute;
    top: 458px;
    left: 872px;
    width: 86px;
    // border: 1px dashed #00518d;
    transform: rotate(30deg);

    .dot {
      transform: rotate(-150deg);
      animation: 1.2s rolling_7 linear infinite normal;
    }
  }

  .net-dot13-box {
    position: absolute;
    top: 583px;
    left: 727px;
    width: 86px;
    // border: 1px dashed #00518d;
    transform: rotate(32deg);

    .dot {
      transform: rotate(-150deg);
      animation: 1.2s rolling_7 linear infinite normal;
    }
  }
}

@keyframes rolling_1 {
  0% {
    left: -8px;
  }

  50% {
    left: 58px;
  }

  100% {
    left: 100px;
  }
}

@keyframes rolling_2 {
  0% {
    left: -8px;
  }

  50% {
    left: 38px;
  }

  100% {
    left: 62px;
  }
}

@keyframes rolling_3 {
  0% {
    left: -8px;
  }

  50% {
    left: 25px;
  }

  100% {
    left: 42px;
  }
}

@keyframes rolling_4 {
  0% {
    left: -8px;
  }

  50% {
    left: 42px;
  }

  100% {
    left: 76px;
  }
}

@keyframes rolling_5 {
  0% {
    left: -8px;
  }

  50% {
    left: 49px;
  }

  100% {
    left: 90px;
  }
}

@keyframes rolling_6 {
  0% {
    left: 86px;

  }

  50% {
    left: 27px;
  }

  100% {
    left: -28px;
  }
}

@keyframes rolling_7 {
  0% {
    left: 36px;

  }

  50% {
    left: 0px;
  }

  100% {
    left: -36px;
  }
}

.net-popover {
  background-color: rgba(161, 188, 208, 1);
  color: #fff;
  border-color: #3281C4;

  &[x-placement^=right] .popper__arrow::after {
    border-right-color: #3281C4 !important;
  }

  &[x-placement^=top] .popper__arrow::after {
    border-top-color: #3281C4 !important;
  }

  &[x-placement^=left] .popper__arrow::after {
    border-left-color: #3281C4 !important;
  }

  &[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: #3281C4 !important;
  }
}

.color_red {
  color: red;
}

.color_blue {
  color: #409EFF;
}
