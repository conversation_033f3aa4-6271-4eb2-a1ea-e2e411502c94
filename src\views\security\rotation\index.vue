<template>
  <el-carousel :interval="setTime" arrow="always" height="100vh" width="100%" v-if="show">
    <el-carousel-item>
      <network-carousel></network-carousel>
    </el-carousel-item>
    <el-carousel-item>
      <password-carousel></password-carousel>
    </el-carousel-item>
  </el-carousel>
</template>
<script>
import networkCarousel from "../network/index.vue";
import passwordCarousel from "../password/index.vue";
import { queryTime } from "@/api/security.js";
export default {
  components: {
    passwordCarousel,
    networkCarousel,
  },
  data() {
    return {
      setTime: 6000,
      show: false,
    };
  },
  mounted() {
    this.getTime();
  },
  watch: {
   
  },
  methods: {
    getTime() {
      queryTime()
        .then((res) => {
          // console.log(res);
          this.setTime = res.subTitle * 1000;
          this.$nextTick(()=>{
            this.show = true;
          })
          
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
>
