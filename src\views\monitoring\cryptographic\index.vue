<template>
  <div class="mainWrapper">
    <div class="mainBox">
      <div class="header clearfix">
        <h3 class="title">云密码监控</h3>
      </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="加密机监控" name="first">
      <encryptor />
    </el-tab-pane>
  <el-tab-pane label="密码服务实例监控" name="second">
   <passwordService/>>
  </el-tab-pane>
  <el-tab-pane label="加密机信息" name="third">
  <encryptorInfo/>>
  </el-tab-pane>
  <el-tab-pane label="应用服务器监控" name="fourth">
  <appMonitor/>
  </el-tab-pane>
  <el-tab-pane label="签章服务器信息" name="five">
  <signatureInfo/>
  </el-tab-pane>
  <el-tab-pane label="签名服务器信息" name="six">
  <autographInfo/>
  </el-tab-pane>
  <el-tab-pane label="应用服务器信息" name="seven">
  <appInfo/>
  </el-tab-pane>
</el-tabs>
  </div>
  </div>
</template>
<script>
  import encryptor from './encryptor'
  import passwordService from './passwordService'
  import encryptorInfo from './encryptorInfo'
  import appMonitor from './appMonitor'
  import signatureInfo from './signatureInfo'
  import autographInfo from './autographInfo'
  import appInfo from './appInfo'
  export default {
    components: {
       encryptor,
       passwordService,
       encryptorInfo,
       appMonitor,
       signatureInfo,
       autographInfo,
       appInfo

       },
    data() {
      return {
        activeName: 'first'
      };
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event);
      }
    }
  };
</script>
<style scoped lang="scss">
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .mainBox {
    .header {
      .title {
        float: left;
      }
      .tab-box {
        padding-left: 100px;

        .tab-item {
          float: left;
          padding: 2px 10px;
          line-height: 24px;
          cursor: pointer;
        }
        .activeColor {
          color: #005ea4;
          border-bottom: 2px solid #005ea4;
        }
      }
    }
    .filter-item {
      margin-right: 20px;
    }
    .border-card-box {
      margin-top: 20px;
    }
  }
}
.mainBox::v-deep .el-tabs__item {
      height: 46px ;
      line-height: 46px;
    }
</style>
