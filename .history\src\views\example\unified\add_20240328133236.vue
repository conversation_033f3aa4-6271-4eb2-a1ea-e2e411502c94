<template>
    <el-dialog width="460px" :visible.sync="true" title="添加产品" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <el-form ref="dataForm" :model="dataForm" :rules="dataRule" label-width="120px"
            @keyup.enter.native="dataFormSubmitHandle()">
            <el-form-item label="用户名" prop="NAME">
                <el-input v-model="dataForm.name"  />
            </el-form-item>
            <el-form-item label="密码">
                <el-input v-model="dataForm.password"  type="password"  />
            </el-form-item>
          
        </el-form>
        <template slot="footer">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">确定
            </el-button>
        </template>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            // visible: false,
            dataForm: {
                name:'',
                password:'',

            },
            // dataRule:{}
        }
    },
    computed: {
        dataRule() {
            return {
                waring_threshold: [
                    { required: true, validator: validNumberPass1, trigger: 'blur' },
                ],
                severe_threshold: [
                    { required: true, validator: validNumberPass1, trigger: 'blur' },
                ],
            };
        },
    },
    methods: {
        dataFormSubmitHandle(){

        }
    },

}
</script>

<style lang="scss" scoped></style>