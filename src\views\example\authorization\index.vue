<template>
  <div class="mainWrapper">
    <!-- <div class="login-box">
        <ul class="login-item-container clearfix">
          <li class="login-item" v-for="(item,index) in poductList" :key="index">
            <router-link to="/">
              <div class="login-item-box">
                <div>
                     <h2>{{item.name}}</h2>
                     <span class="status active" v-if="item.status==1">激活</span>
                      <span class="status " v-if="item.status==2">未激活</span>
                </div>
             
                
              </div>
            </router-link>
          </li>
        </ul>
      </div>-->
    <div class="mainBox auth-box" v-if="showTenant">
      <div class="header">
        <h3 class="title">授权周期</h3>
      </div>
      <div class="auth-content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="云防火墙" name="firewallData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="firewallData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="name"
                    label="客户"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="effective_time"
                    label="有效时间"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="addition"
                    label="其他信息"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <!-- <el-tab-pane label="云Web应用防火墙" name="wafData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="wafData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="cust"
                    label="客户"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="time"
                    label="有效时间"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="info"
                    label="其他信息"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane> -->
          <el-tab-pane label="云日志审计" name="logAuditData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="logAuditData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="sn"
                    label="设备SN"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="cust"
                    label="客户"
                    sortable
                    width="180"
                  ></el-table-column>
                  <el-table-column
                    prop="type"
                    label="类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="capacity"
                    label="存储大小"
                  ></el-table-column>
                  <el-table-column
                    prop="time"
                    label="有效时间"
                    sortable
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云堡垒机" name="fortressData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="fortressData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="customInfo"
                    label="客户信息"
                    sortable
                    width="120"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="authType"
                    label="类型"
                    sortable
                    width="120"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column prop="status" label="状态" width="120">
                    <template slot-scope="scope">
                      <div>
                        <span v-if="scope.row.status == 0" class="normal"
                          >未激活</span
                        >
                        <span v-if="scope.row.status == 1" class="abnormal"
                          >已激活</span
                        >
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="prodId"
                    label="产品ID"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="module"
                    label="授权模块"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="limitResource"
                    label="授权资源数"
                  ></el-table-column>
                  <el-table-column
                    prop="limitConn"
                    label="授权资源并发连接数"
                  ></el-table-column>
                  <el-table-column
                    prop="expireDate"
                    label="过期时间"
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云数据库审计" name="databaseData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="databaseData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="customer"
                    label="单位名称"
                    sortable
                    width="120"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="expiredTime"
                    label="审计有效期"
                    sortable
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="instanceNum"
                    label="资产数"
                    width="90"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="deviceName"
                    label="设备名称"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="version"
                    label="版本号"
                    width="80"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="publish"
                    label="发布号"
                    width="80"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="mgt"
                    label="管理中心编译号"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="proc"
                    label="数据处理中心编译号"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="buildTime"
                    label="编译时间"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="factory"
                    label="设备厂家"
                    show-overflow-tooltip
                  ></el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
          <el-tab-pane label="云主机安全" name="hostData">
            <div class="auth-item">
              <el-table-bar>
                <el-table
                  :data="hostData"
                  style="width: 100%; margin-bottom: 20px"
                  row-key="id"
                >
                  <el-table-column
                    prop="companyName"
                    label="授权对象"
                    sortable
                  ></el-table-column>
                  <el-table-column label="许可类型" sortable>
                    <template slot-scope="scope">
                      <div>
                        <span v-if="scope.row.type == 0" class="normal"
                          >正式授权</span
                        >
                        <span v-if="scope.row.type == 1" class="abnormal"
                          >试用授权</span
                        >
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="模块名称">
                    <template slot-scope="scope">
                      <div>
                        <span v-if="scope.row.name == 'secaudit'">安全审计</span>
                        <span v-if="scope.row.name == 'bd'">增强病毒库</span>
                        <span v-if="scope.row.name == 'netflow'">网卡流量统计</span>
                        <span v-if="scope.row.name == 'cloudfasten'">云加固</span>
                        <span v-if="scope.row.name == 'webshell'">webshell扫描</span>
                        <span v-if="scope.row.name == 'ips'">入侵防御</span>
                        <span v-if="scope.row.name == 'antibrute'">防暴力破解</span>
                        <span v-if="scope.row.name == 'av'">防恶意软件</span>
                        <span v-if="scope.row.name == 'fw'">防火墙</span>
                        <span v-if="scope.row.name == 'baseline'">安全基线</span>
                        <span v-if="scope.row.name == 'vulnerability'">漏洞管理</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="到期时间" prop="time">
                  </el-table-column>
                </el-table>
              </el-table-bar>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { getList } from "@/api/auth.js";
import { mapGetters, mapState, mapMutations } from "vuex";
import { Loading } from "element-ui";
export default {
  data() {
    return {
      activeName: "firewallData",
      showTenant: true,
      firewallData: [],
      listLoading: false,
      wafData: [
        {
          cust: "Hillstone",
          type: "平台",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证",
        },
        {
          cust: "Hillstone",
          type: "WAF规则库",
          time: "有效期剩余28天",
          info: "系统受限，请尽快购买许可证",
        },
        {
          cust: "",
          type: "虚拟机CPU",
          time: "未授权",
          info: "",
        },
        {
          cust: "",
          type: "反爬虫服务",
          time: "未授权",
          info: "",
        },
        {
          cust: "",
          type: "WAF IP信誉库",
          time: "未授权",
          info: "",
        },
      ],
      logAuditData: [
        {
          sn: "0000000000000",
          cust: "Hillstone Network ",
          type: "VHSA",
          capacity: "200G",
          time: "--",
        },
      ],
      fortressData: [],
      databaseData: [],
      hostData: [],
    };
  },
  computed: {
    ...mapGetters(["userid"]),
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.listLoading = Loading.service({
        lock: true,
        text: "加载中……",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let userId = this.userid;
      console.log(userId);
      getList(userId)
        .then((res) => {
          console.log(res);
          this.listLoading.close();
          if(res.obj.fortressMachine!=''){
               this.fortressData.push(res.obj.fortressMachine);
          }
       
          // //console.log( this.fortressData)
          if(res.obj.database !=''){
               this.databaseData.push(res.obj.database);
          }
         
          // //console.log(this.databaseData);
          if(res.obj.firewall !=''){
              this.firewallData = res.obj.firewall;
          }
        
          // console.log( this.firewallData)
          if(res.obj.hostSecurity!=''){
             this.hostData = res.obj.hostSecurity;
          }
          if(res.data.logAudit!=''){
            this.logAuditData=res.obj.logAudit;
          }
         
        })
        .catch((error) => {
          this.listLoading.close();
        });
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
  },
};
</script>
<style lang="scss" scoped>
.elTableBar {
  height: calc(100vh - 174px);
}
.mainWrapper {
  height: calc(100vh - 60px);
  background: #fff;
  .auth-box {
    .auth-content {
      margin-top: 10px;
      .auth-item {
        padding: 0 30px;
      }
    }
  }
}
</style>
