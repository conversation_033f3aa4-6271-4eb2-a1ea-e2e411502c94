apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: zj-server-smp-web
  name: zj-server-smp-web
  namespace: zj-server
spec:
  replicas: 1
  strategy:
    type: RollingUpdate # Recreate：在创建新Pods之前，所有现有的Pods会被杀死 RollingUpdate：滚动升级，逐步替换的策略，同时滚动升级时，支持更多的附加参数
    rollingUpdate:
      maxSurge: 1  #maxSurge：1 表示滚动升级时会先启动1个pod
      maxUnavailable: 1 #maxUnavailable：1 表示滚动升级时允许的最大Unavailable的pod个数，也可以填写比例，maxUnavailable=50%
  selector:
    matchLabels:
      app: zj-server-smp-web
  template:
    metadata:
      labels:
        app: zj-server-smp-web
    spec:
      containers:
      - name: zj-server-smp-web
        image: harbor.kube.com/zj-server/zj-server-smp-web
        ports:
        - containerPort: 80
          protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: zj-server-smp-web
  name: zj-server-smp-web
  namespace: zj-server # 正常创建的项目空间名称
spec:
  ports:
    - name: http81
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: zj-server-smp-web
  sessionAffinity: None
  type: NodePort
