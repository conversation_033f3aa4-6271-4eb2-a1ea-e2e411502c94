   server {
        listen       80;
        server_name  localhost;


        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
             # 如果vue-router使用的是history模式，需要设置这个 
            try_files $uri $uri/ /index.html;
        }
        # 反向代理后端api
        location /api {
            rewrite ^/api/(.*)$ /$1 break; 
            proxy_pass http://zj-server-smp-test.zj-server:8086/;
        } 
    }
